package com.moego.common.utils;

import static com.moego.common.enums.CompanyFunctionControlConst.ENABLE_STRIPE_READER;
import static com.moego.common.enums.CompanyFunctionControlConst.IS_NEW_PRICING;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.moego.common.dto.CompanyFunctionControlDto;
import com.moego.common.dto.FeatureQuotaDto;
import com.moego.common.dto.NewPricingLevelDto;
import com.moego.common.dto.TokenPairDTO;
import com.moego.common.enums.CompanyFunctionControlConst;
import com.moego.common.enums.FeatureConst;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.Base64;
import java.util.Map;
import javax.crypto.KeyGenerator;
import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

public class AccountUtil {

    public static final String JWT_SIGN_STR_ACCOUNT = "GqkEEPwUPDZvtCRj";
    public static final String JWT_SIGN_STR_STAFF = "KZBHhPhPTruONnnw";

    public static String getPasswordMd5(String password, String passwordSalt) {
        try {
            return getPasswordMd5NoMoego(password, passwordSalt);
        } catch (NoSuchAlgorithmException e) {
            throw new CommonException(ResponseCodeEnum.SERVER_ERROR);
        }
    }

    /**
     * 兼容旧版php的md5校验方式
     *
     * @param password
     * @param passwordSalt
     * @return
     * @throws NoSuchAlgorithmException
     */
    public static String getPasswordMd5NoMoego(String password, String passwordSalt) throws NoSuchAlgorithmException {
        String dataStr = md5(password) + passwordSalt;
        // spring 自带的md5工具
        MessageDigest md = MessageDigest.getInstance("MD5");
        md.update(dataStr.getBytes(StandardCharsets.UTF_8));
        return new BigInteger(1, md.digest()).toString(16);
    }

    public static String md5(String md5Str) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance("MD5");
        md.update(md5Str.getBytes(StandardCharsets.UTF_8));
        return new BigInteger(1, md.digest()).toString(16);
    }

    public static String toHex(byte[] bytes) {
        final char[] HEX_DIGITS = "0123456789abcdef".toCharArray();
        StringBuilder ret = new StringBuilder(bytes.length * 2);
        for (byte aByte : bytes) {
            ret.append(HEX_DIGITS[(aByte >> 4) & 0x0f]);
            ret.append(HEX_DIGITS[aByte & 0x0f]);
        }
        return ret.toString();
    }

    public static String getStaffToken(Integer accountId, Integer businessId, Integer staffId) {
        return JWT.create()
                .withAudience(
                        String.valueOf(accountId),
                        String.valueOf(businessId),
                        String.valueOf(staffId),
                        CommonUtil.getRandomString(16))
                .sign(Algorithm.HMAC256(JWT_SIGN_STR_STAFF));
    }

    public static String getAccountToken(Integer accountId) {
        return JWT.create()
                .withAudience(String.valueOf(accountId), CommonUtil.getRandomString(16))
                .sign(Algorithm.HMAC256(JWT_SIGN_STR_ACCOUNT));
    }

    /**
     * 生成customer token
     *
     * @param algorithm
     * @return
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeyException
     */
    public static TokenPairDTO generateTokenPair(String algorithm)
            throws NoSuchAlgorithmException, InvalidKeyException {
        KeyGenerator keyGen = KeyGenerator.getInstance(algorithm);
        SecretKey key = keyGen.generateKey();
        byte[] token = keyGen.generateKey().getEncoded();
        Mac mac = Mac.getInstance(algorithm);
        mac.init(key);
        mac.update(token);
        byte[] hash = mac.doFinal();

        String KEY = Base64.getUrlEncoder().encodeToString(key.getEncoded());
        String TOKEN = Base64.getUrlEncoder().encodeToString(token);
        String HASH = Base64.getUrlEncoder().encodeToString(hash);

        TokenPairDTO tokenPair = new TokenPairDTO();
        tokenPair.setAlgorithm(algorithm);
        tokenPair.setKey(KEY);
        tokenPair.setToken(TOKEN);
        tokenPair.setHash(HASH);

        return tokenPair;
    }

    /**
     * 校验customer token
     *
     * @param tokenPair
     * @return
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeyException
     */
    public static boolean verifyToken(TokenPairDTO tokenPair) throws NoSuchAlgorithmException, InvalidKeyException {
        SecretKey dKey = new SecretKeySpec(Base64.getUrlDecoder().decode(tokenPair.getKey()), tokenPair.getAlgorithm());
        Mac mac = Mac.getInstance(tokenPair.getAlgorithm());
        mac.init(dKey);
        mac.update(Base64.getUrlDecoder().decode(tokenPair.getToken()));
        byte[] dHash = mac.doFinal();
        return Arrays.equals(dHash, Base64.getUrlDecoder().decode(tokenPair.getHash()));
    }

    public static void updatePlanFeatureByWIPEnable(
            Map<String, FeatureQuotaDto> planFeature, String code, boolean wipEnable) {
        if (planFeature == null) {
            return;
        }
        FeatureQuotaDto quotaDto = planFeature.get(code);
        if (quotaDto == null) {
            quotaDto = new FeatureQuotaDto();
            quotaDto.setCode(code);
        }
        quotaDto.updateByEnable(wipEnable);
        planFeature.put(code, quotaDto);
    }

    public static String getPasswordSalt() {
        return CommonUtil.getRandomString(5);
    }

    /**
     * 是否是plan1的level取值范围
     *
     * @param level
     * @return
     */
    private static boolean levelIsPlanVersion1(Integer level) {
        if (CompanyFunctionControlConst.PLAN_VERSION_1_START_LEVEL <= level
                && level <= CompanyFunctionControlConst.PLAN_VERSION_1_END_LEVEL) {
            return true;
        }
        return false;
    }

    private static Integer getPremiumTypeByLevelForPlan1(Integer level) {
        if (CompanyFunctionControlConst.LEVEL_1.equals(level)
                || CompanyFunctionControlConst.LEVEL_4.equals(level)
                || CompanyFunctionControlConst.LEVEL_5.equals(level)) {
            return CompanyFunctionControlConst.TIER_LEVEL_FIRST;
        } else {
            // 可能的level有2、3、6
            return CompanyFunctionControlConst.TIER_LEVEL_SECOND;
        }
    }

    /**
     * 是否是plan2的level取值范围
     *
     * @param level
     * @return
     */
    private static boolean levelIsPlanVersion2(Integer level) {
        if (CompanyFunctionControlConst.PLAN_VERSION_2_START_LEVEL <= level
                && level <= CompanyFunctionControlConst.PLAN_VERSION_2_END_LEVEL) {
            return true;
        }
        return false;
    }

    private static Integer getPremiumTypeByLevelForPlan2(Integer level) {
        if (CompanyFunctionControlConst.LEVEL_8.equals(level)
                || (level >= CompanyFunctionControlConst.LEVEL_10 && level <= CompanyFunctionControlConst.LEVEL_37)) {
            // level 可能的数据8  11-100  new_pricing 69
            return CompanyFunctionControlConst.TIER_LEVEL_SECOND;
        } else {
            // level只有7、9两种可能 new_pricing 39
            return CompanyFunctionControlConst.TIER_LEVEL_FIRST;
        }
    }

    /**
     * 是否是plan3的level取值范围
     *
     * @param level
     * @return
     */
    private static boolean levelIsPlanVersion3(Integer level) {
        return CompanyFunctionControlConst.PLAN_VERSION_3_START_LEVEL <= level
                && level <= CompanyFunctionControlConst.PLAN_VERSION_3_END_LEVEL;
    }

    private static Integer getPremiumTypeByLevelForPlan3(Integer level) {
        if (level < CompanyFunctionControlConst.PLAN_VERSION_3_MAX_TIER_FIRST_LEVEL) {
            return CompanyFunctionControlConst.TIER_LEVEL_FIRST;
        } else if (level < CompanyFunctionControlConst.PLAN_VERSION_3_MAX_TIER_SECOND_LEVEL) {
            return CompanyFunctionControlConst.TIER_LEVEL_SECOND;
        } else if (level < CompanyFunctionControlConst.PLAN_VERSION_3_MAX_TIER_THIRD_LEVEL) {
            return CompanyFunctionControlConst.TIER_LEVEL_THIRD;
        } else if (level < CompanyFunctionControlConst.PLAN_VERSION_3_MAX_TIER_FOURTH_LEVEL) {
            return CompanyFunctionControlConst.TIER_LEVEL_FOURTH;
        }
        return CompanyFunctionControlConst.PREMIUM_TYPE_FREE;
    }

    public static Boolean checkFeatureCodeIsEnable(String code, Map<String, FeatureQuotaDto> featureQuotaDtoMap) {
        FeatureQuotaDto feature = featureQuotaDtoMap.get(code);
        if (feature == null) {
            return false;
        }
        if (feature.getEnable()) {
            if (FeatureConst.MAX_QUOTA.equals(feature.getQuota())) {
                return true;
            } else return feature.getQuota() > FeatureConst.MIN_QUOTA;
        }
        return false;
    }

    /**
     * 根据level生成tierLevel
     * 作用：
     * 1、用于判断change plan的升降级，tierLevel新旧比较判断
     * 2、根据tierLevel、planVersion获取对应的费率
     *
     * @param level
     * @return
     */
    public static NewPricingLevelDto convertNewPricingType(Integer level) {
        if (level == null || CompanyFunctionControlConst.LEVEL_0.equals(level)) {
            return new NewPricingLevelDto();
        }
        Integer premiumType = CompanyFunctionControlConst.PREMIUM_TYPE_FREE;
        Integer planVersion = CompanyFunctionControlConst.PLAN_VERSION_3;

        // old olg plan
        if (levelIsPlanVersion1(level)) {
            // 1-6
            planVersion = CompanyFunctionControlConst.PLAN_VERSION_1;
            premiumType = getPremiumTypeByLevelForPlan1(level);
        } else if (levelIsPlanVersion2(level)) {
            // 2.0 plan
            // 7-100
            planVersion = CompanyFunctionControlConst.PLAN_VERSION_2;
            premiumType = getPremiumTypeByLevelForPlan2(level);
        } else if (levelIsPlanVersion3(level)) {
            // new pricing
            // 1000-1100是t1，1100-1200是t2，1200-1300是t3
            // 新增solo plan和starter属于同一级别, 为了区分, 增加版本号, 从而保证升降机逻辑不变
            if (level.equals(CompanyFunctionControlConst.TIER_FIRST_SOLO_PLAN_LEVEL)) {
                planVersion = CompanyFunctionControlConst.PLAN_VERSION_4;
            }
            if (level.equals(CompanyFunctionControlConst.TIER_SECOND_BD_LEVEL)
                    || level.equals(CompanyFunctionControlConst.TIER_THIRD_BD_LEVEL)
                    || level.equals(CompanyFunctionControlConst.TIER_FORTH_BD_LEVEL)) {
                planVersion = CompanyFunctionControlConst.PLAN_VERSION_5;
            }
            premiumType = getPremiumTypeByLevelForPlan3(level);
        }
        return new NewPricingLevelDto(premiumType, planVersion);
    }

    public static Integer getPremiumTypeByDefaultLevel(Integer level) {
        if (level == null) {
            return CompanyFunctionControlConst.PREMIUM_TYPE_FREE;
        }
        if (CompanyFunctionControlConst.TIER_FIRST_DEFAULT_LEVEL.equals(level)) {
            // tier 1
            return CompanyFunctionControlConst.TIER_LEVEL_FIRST;
        } else if (CompanyFunctionControlConst.TIER_SECOND_DEFAULT_LEVEL.equals(level)) {
            // tier 2
            return CompanyFunctionControlConst.TIER_LEVEL_SECOND;
        } else if (CompanyFunctionControlConst.TIER_THIRD_DEFAULT_LEVEL.equals(level)) {
            // tier 3
            return CompanyFunctionControlConst.TIER_LEVEL_THIRD;
        } else {
            return CompanyFunctionControlConst.PREMIUM_TYPE_FREE;
        }
    }

    /**
     * 必须都有值，没有值则按free账号返回信息
     *
     * @param level
     * @param isNewPricing      是否是newPricing
     * @param enableStripReader 是否启用 strip reader
     * @return
     */
    public static CompanyFunctionControlDto getCompanyControlDto(
            Integer level, Integer isNewPricing, Byte enableStripReader) {
        CompanyFunctionControlDto controlDto = new CompanyFunctionControlDto();
        // 免费的商户
        if (level == null || isNewPricing == null || CompanyFunctionControlConst.LEVEL_0.equals(level)) {
            return controlDto;
        }
        int intLevel = level;
        // level >1000的特殊判断
        if (level > CompanyFunctionControlConst.LEVEL_1000) {
            Integer featurePremiumType = getPremiumTypeByDefaultLevel(level);
            if (CompanyFunctionControlConst.TIER_LEVEL_FIRST.equals(featurePremiumType)) {
                // 39
                controlDto.setPremiumType(CompanyFunctionControlConst.PREMIUM_TYPE_39);
                controlDto.setAllowSendMassText(false);
                controlDto.setAllowSendTwoWay(false);
                controlDto.setAllowReviewBooster(false);
                controlDto.setAllowSmartScheduleForRepeat(false);
                controlDto.setAllowCallForwarding(false);
            }
            if (CompanyFunctionControlConst.TIER_LEVEL_SECOND.equals(featurePremiumType)
                    || CompanyFunctionControlConst.TIER_LEVEL_THIRD.equals(featurePremiumType)) {
                // 69
                controlDto.setPremiumType(CompanyFunctionControlConst.PREMIUM_TYPE_69);
                controlDto.setAllowSendMassText(true);
                controlDto.setAllowSendTwoWay(true);
                controlDto.setAllowReviewBooster(true);
                controlDto.setAllowSmartScheduleForRepeat(true);
                controlDto.setAllowCallForwarding(true);
            }
            return controlDto;
        }
        int appointmentNumber = CompanyFunctionControlConst.APPOINTMENT_NUMBER_UNLIMITED;
        boolean newPricing = IS_NEW_PRICING.equals(isNewPricing);
        if (newPricing) {
            appointmentNumber = CompanyFunctionControlConst.APPOINTMENT_NUMBER_UNLIMITED;
            // 老版本的level配置
            if (CompanyFunctionControlConst.LEVEL_8.equals(level)
                    || (intLevel >= CompanyFunctionControlConst.LEVEL_10
                            && intLevel <= CompanyFunctionControlConst.LEVEL_37)) {
                // level 可能的数据8  11-37  new_pricing 69
                controlDto.setPremiumType(CompanyFunctionControlConst.PREMIUM_TYPE_69);
                controlDto.setAllowSendMassText(true);
                controlDto.setAllowSendTwoWay(true);
                controlDto.setAllowReviewBooster(true);
                controlDto.setAllowSmartScheduleForRepeat(true);
                controlDto.setAllowCallForwarding(true);
            } else {
                // level只有7、9两种可能 new_pricing 39
                controlDto.setPremiumType(CompanyFunctionControlConst.PREMIUM_TYPE_39);
                controlDto.setAllowSendMassText(false);
                controlDto.setAllowSendTwoWay(false);
                controlDto.setAllowReviewBooster(false);
                controlDto.setAllowSmartScheduleForRepeat(false);
                controlDto.setAllowCallForwarding(false);
            }
        } else {
            // 老老版本的level配置
            // 不管39还是69都允许发送短信，还是无限量的
            // 但不允许发送mass text
            // 允许review booster
            // 可能的level有1、2、3、4、5、6
            if (CompanyFunctionControlConst.LEVEL_1.equals(level)
                    || CompanyFunctionControlConst.LEVEL_4.equals(level)
                    || CompanyFunctionControlConst.LEVEL_5.equals(level)) {
                controlDto.setPremiumType(CompanyFunctionControlConst.PREMIUM_TYPE_39);
                controlDto.setAllowSmartScheduleForRepeat(false);
                controlDto.setAllowCallForwarding(false);
            } else {
                // 可能的level有2、3、6
                controlDto.setPremiumType(CompanyFunctionControlConst.PREMIUM_TYPE_69);
                controlDto.setAllowSmartScheduleForRepeat(true);
                controlDto.setAllowCallForwarding(true);
            }
            controlDto.setAllowSendMassText(false);
            controlDto.setAllowSendTwoWay(true);
            controlDto.setAllowReviewBooster(true);
            if (CompanyFunctionControlConst.LEVEL_1.equals(level)
                    || CompanyFunctionControlConst.LEVEL_4.equals(level)) {
                appointmentNumber = CompanyFunctionControlConst.APPOINTMENT_NUMBER_100;
            } else {
                appointmentNumber = CompanyFunctionControlConst.APPOINTMENT_NUMBER_UNLIMITED;
            }
        }
        controlDto.setAppointmentNumber(appointmentNumber);

        // stripe reader whitelist #ERP-1186
        controlDto.setEnableStripeReader(ENABLE_STRIPE_READER.equals(enableStripReader));
        return controlDto;
    }
}
