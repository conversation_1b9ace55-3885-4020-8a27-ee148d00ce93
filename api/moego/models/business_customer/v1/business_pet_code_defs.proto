syntax = "proto3";

package moego.models.business_customer.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// create def for pet code
message BusinessPetCodeCreateDef {
  // pet code abbreviation
  string abbreviation = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 50
    // not blank
    pattern: ".*\\S+.*"
  }];

  // pet code description
  string description = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 50
    // not blank
    pattern: ".*\\S+.*"
  }];

  // pet code color
  string color = 3 [(validate.rules).string = {
    min_len: 1
    max_len: 50
    // not blank
    pattern: ".*\\S+.*"
  }];
  // sort
  optional int64 sort = 4;
}

// update def for pet code
message BusinessPetCodeUpdateDef {
  // pet code abbreviation
  optional string abbreviation = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 50
    // not blank
    pattern: ".*\\S+.*"
  }];

  // pet code description
  optional string description = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 50
    // not blank
    pattern: ".*\\S+.*"
  }];

  // pet code color
  optional string color = 3 [(validate.rules).string = {
    min_len: 1
    max_len: 50
    // not blank
    pattern: ".*\\S+.*"
  }];
}
