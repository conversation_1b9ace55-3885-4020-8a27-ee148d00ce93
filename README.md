# moego-svc-split-payment

Server for go svc server template

## Development

1. init project

    ```bash
    make init
    ```
2. project structure
   当前项目的目录结构：

   ```
   ├── ci
   │   ├── Dockerfile
   │   ├── ci.yaml
   │   └── lint.sh
   ├── config
   │   ├── config.go
   │   ├── config.yaml
   │   └── config_test.go
   ├── hooks
   │   ├──commit-msg
   │   ├── pre-commit
   │   └── pre-push
   ├── internal
   │   ├── entity
   │   ├── logic
   │   ├── repo
   │   │   ├── rpc
   │   │   │   └── rpc.go
   │   │   ├── db
   │   │   │   └── db.go
   │   │   ├── cache
   │   │   │   └── cache.go
   │   │   ├── mock
   │   │   │   └── repo_mock.go
   │   ├── service
   │   │   ├── service.go
   │   │   └── xxx_service.go
   │   │   └── wire.go
   │   ├── test
   │   ├── utils
   │   └── main.go
   ├── .gitignore
   ├── .golangci.yml
   ├── CODEOWNERS
   ├── go.mod
   │   └── go.sum
   ├── Makefile
   ├── README.md
   ```

其中:
- `ci` 目录下是ci的配置文件，包括Dockerfile、ci.yaml、lint.sh，每个服务按照自己需要修改其中的服务名
- `config` 目录下是配置文件的结构体定义，以及配置文件的读取
- `hooks` 存放 git hooks
- `internal` 是项目的主要代码目录，其中包括了:
    - `entity` 存放服务内部的实体定义
    - `logic` 存放服务的业务逻辑，该层以 entity 定义进行操作，会调用 repo 获取外部资源
    - `repo` 提供外部依赖接口定义及实现，包括 rpc、db、cache 等
    - `service` GRPC 接口的实现，wire.go 用于依赖注入，该层会将对外 proto 转化为 entity 并调用 logic 层
    - `test` 使用单元测试实现的接口测试，用来保证 t2 环境 production 分支制品的接口准确性
    - `utils` 常用的工具函数
    - `main.go` 服务启动的入口
- `.gitignore` gitignore 文件
- `.golangci.yml` golangci-lint 配置文件
- `go.mod` go mod 文件
- `CODEOWNERS` 包含对应目录的 Code Review necessary reviewers
- `Makefile` 包含一些常用命令
- `README.md` 项目说明
