// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"context"
	"github.com/MoeGolibrary/go-lib/gorm"
	"github.com/MoeGolibrary/go-lib/grpc"
	"github.com/MoeGolibrary/go-lib/redis"
	"github.com/MoeGolibrary/go-lib/server"
	"github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/marketing/v1"
	"github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
	ordersvcpb2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2"
	ordersvcpb3 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/temp_order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/config"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/controller"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/appointment"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/business"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/depositrule"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/file"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/grooming"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/message"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/promotion"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/subscription"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/service"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/service/handler"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/service/helper"
	"github.com/growthbook/growthbook-golang"
)

// Injectors from wire.go:

func NewServer() server.Server {
	db := newOrderDB()
	orderRepo := repo.NewOrderRepo(db)
	orderItemRepo := repo.NewOrderItemRepo(db)
	orderPaymentRepo := repo.NewOrderPaymentRepo(db)
	orderLineDiscountRepo := repo.NewOrderLineDiscountRepo(db)
	refundOrderPaymentRepo := repo.NewRefundOrderPaymentRepo(db)
	refundOrderRepo := repo.NewRefundOrderRepo(db)
	depositChangeLogRepo := repo.NewDepositChangeLogRepoRepo(db)
	orderPromotionRepo := repo.NewOrderPromotionRepo(db)
	orderPromotionItemRepo := repo.NewOrderPromotionItemRepo(db)
	txRepo := repo.NewTXRepo(db)
	v := newRedisClient()
	client := newGrowthBookClient()
	userFlagClient := repo.NewUserFlagClient(v, client)
	paymentDB := newPaymentDB()
	legacyPaymentRepo := repo.NewLegacyPaymentRepo(paymentDB)
	legacyRefundRepo := repo.NewLegacyRefundRepo(paymentDB)
	groomingClient := grooming.NewGroomingClient()
	paymentClient := repo.NewPaymentClient()
	fileClient := file.New()
	organizationRepo := depositrule.NewBusinessRepo()
	customerRepo := depositrule.NewCustomerRepo()
	orderService := service.NewOrderService(orderRepo, orderItemRepo, orderPaymentRepo, orderLineDiscountRepo, refundOrderPaymentRepo, refundOrderRepo, depositChangeLogRepo, orderPromotionRepo, orderPromotionItemRepo, txRepo, userFlagClient, legacyPaymentRepo, legacyRefundRepo, groomingClient, paymentClient, fileClient, organizationRepo, customerRepo)
	refundOrderItemRepo := repo.NewRefundOrderItemRepo(db)
	messageClient := message.NewMessageClient()
	refundOrderService := service.NewRefundOrderService(orderRepo, orderItemRepo, orderPaymentRepo, refundOrderRepo, refundOrderItemRepo, refundOrderPaymentRepo, depositChangeLogRepo, legacyPaymentRepo, legacyRefundRepo, organizationRepo, customerRepo, txRepo, paymentClient, groomingClient, messageClient, fileClient)
	depositOrderService := service.NewDepositOrderService(depositChangeLogRepo, orderRepo, orderItemRepo, txRepo)
	discountCodeServiceClient := newDiscountClient()
	tipsSplitRepo := repo.NewTipsSplitRepo(db)
	tipsSplitDetailRepo := repo.NewTipsSplitDetailRepo(db)
	legacyOrderTipsSplitDetailRepo := repo.NewLegacyOrderTipsSplitDetailRepo(db)
	txTipsSplitRepo := repo.NewTxTipsSplitRepo(db)
	tipsSplitEngine := helper.NewTipsSplitEngine()
	businessClient := business.NewBusinessClient()
	tipsSplitStatusClient := repo.NewTipsSplitStatusClient(v)
	tipsSplitService := service.NewTipsSplitService(orderRepo, orderItemRepo, tipsSplitRepo, tipsSplitDetailRepo, legacyOrderTipsSplitDetailRepo, txTipsSplitRepo, tipsSplitEngine, businessClient, groomingClient, tipsSplitStatusClient)
	paymentEventHandler := handler.NewPaymentEventHandler(refundOrderService, orderPaymentRepo, refundOrderPaymentRepo, txRepo, paymentClient)
	orderServiceServer := controller.NewOrderServer(orderService, refundOrderService, depositOrderService, discountCodeServiceClient, tipsSplitService, businessClient, paymentEventHandler)
	appointmentClient := appointment.New()
	orderPaymentService := service.NewOrderPaymentService(txRepo, orderRepo, orderPaymentRepo, paymentClient, appointmentClient, v, groomingClient)
	promotionClient := promotion.New()
	subscriptionClient := subscription.New()
	promotionService := service.NewPromotionService(promotionClient, subscriptionClient, orderPromotionRepo, orderPromotionItemRepo, orderRepo)
	ordersvcpbOrderServiceServer := controller.NewOrderServerV2(orderPaymentService, promotionService, depositOrderService, orderService, refundOrderService)
	exportServiceServer := controller.NewExportServer(orderService, refundOrderService)
	splitTipsServiceServer := controller.NewTipsSplitServiceServer(tipsSplitService, orderService, refundOrderService)
	orderItemAssignedAmountRepo := repo.NewOrderItemAssignedAmountRepo(db)
	itemAssignService := service.NewItemAssignService(orderItemAssignedAmountRepo)
	assignItemAmountServiceServer := controller.NewAssignItemAmountServer(itemAssignService)
	depositruleRepo := depositrule.NewDepositRuleRepo(db)
	depositRuleService := service.NewDepositRulesService(depositruleRepo, organizationRepo, customerRepo, paymentClient, groomingClient)
	depositRuleServiceServer := controller.NewDepositRuleServer(depositRuleService)
	orderTaskServiceServer := controller.NewOrderTaskServer(orderPaymentService, promotionService, orderService)
	migrationService := service.NewMigrationService(userFlagClient, txRepo)
	migrationServiceServer := controller.NewMigrationServer(orderService, migrationService)
	serverServer := newServer(orderServiceServer, ordersvcpbOrderServiceServer, exportServiceServer, splitTipsServiceServer, assignItemAmountServiceServer, depositRuleServiceServer, orderTaskServiceServer, migrationServiceServer)
	return serverServer
}

// wire.go:

func newServer(
	serviceServer ordersvcpb.OrderServiceServer,
	serviceServerV2 ordersvcpb2.OrderServiceServer,
	exportServer ordersvcpb2.ExportServiceServer,
	tipsSplitServer ordersvcpb2.SplitTipsServiceServer,
	assignItemAmountServer ordersvcpb3.AssignItemAmountServiceServer,
	depositRuleServer ordersvcpb2.DepositRuleServiceServer,
	orderTaskServer ordersvcpb2.OrderTaskServiceServer,
	migrationServer ordersvcpb2.MigrationServiceServer,
) server.Server {
	s := server.NewDefaultServer()
	ordersvcpb.RegisterOrderServiceServer(s, serviceServer)
	ordersvcpb2.RegisterOrderServiceServer(s, serviceServerV2)
	ordersvcpb2.RegisterExportServiceServer(s, exportServer)
	ordersvcpb2.RegisterSplitTipsServiceServer(s, tipsSplitServer)
	ordersvcpb2.RegisterDepositRuleServiceServer(s, depositRuleServer)
	ordersvcpb2.RegisterOrderTaskServiceServer(s, orderTaskServer)
	ordersvcpb2.RegisterMigrationServiceServer(s, migrationServer)
	ordersvcpb3.RegisterAssignItemAmountServiceServer(s, assignItemAmountServer)

	return s
}

func newOrderDB() *gorm.DB {
	return gorm.Must(gorm.OpenPostgres(config.OrderDSN(), nil))
}

func newPaymentDB() *repo.PaymentDB {
	return (*repo.PaymentDB)(gorm.Must(gorm.OpenMySQL(config.PaymentDSN(), nil)))
}

func newRedisClient() redis.UniversalClient { return redis.NewClient(config.Redis()) }

func newDiscountClient() marketingsvcpb.DiscountCodeServiceClient {
	return grpc.NewClient("moego-svc-marketing:9090", marketingsvcpb.NewDiscountCodeServiceClient)
}

func newGrowthBookClient() *growthbook.Client {
	gb, err := growthbook.NewClient(context.Background(), config.GrowthBook()...)
	if err != nil {
		panic(err)
	}
	return gb
}
