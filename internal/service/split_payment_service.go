package service

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	googlemoney "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/emptypb"

	"github.com/MoeGolibrary/go-lib/zlog"
	errorsModelsV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/errors/v1"
	splitpaymentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/split_payment/v1"

	splitpayment "github.com/MoeGolibrary/moego-svc-split-payment/internal/logic/split_payment"
	"github.com/MoeGolibrary/moego-svc-split-payment/internal/utils/money"
)

type splitPaymentService struct {
	*splitpaymentpb.UnimplementedSplitPaymentServiceServer
	splitPayment *splitpayment.SplitPayment
}

func newSplitPaymentService(splitPayment *splitpayment.SplitPayment) *splitPaymentService {
	return &splitPaymentService{
		splitPayment: splitPayment,
	}
}

// split payment, 执行分账操作
func (s *splitPaymentService) SplitPayment(ctx context.Context, req *splitpaymentpb.SplitPaymentRequest) (
	*splitpaymentpb.SplitPaymentResponse, error) {

	zlog.Info(ctx, fmt.Sprintf("invoke SplitPayment, req=%+v", req))

	// 参数校验
	if err := s.checkSplitPaymentRequest(req); err != nil {
		zlog.Error(ctx, "check request err", zap.Error(err))
		return &splitpaymentpb.SplitPaymentResponse{
			Result:     false,
			ResultCode: errorsModelsV1.Code_CODE_SPLIT_PAYMENT_ACCEPTANCE_FAILED.String(),
			ResultMsg:  "split payment reqeust invaild",
		}, nil
	}
	// 调用logic层做具体业务操作
	if err := s.splitPayment.SplitPayment(ctx, req); err != nil {
		// 这里是异步处理，同步返回的是受理结果，这里有err就是受理失败了
		zlog.Error(ctx, "split payment acceptance failed", zap.Error(err))
		return &splitpaymentpb.SplitPaymentResponse{
			Result:     false,
			ResultCode: errorsModelsV1.Code_CODE_SPLIT_PAYMENT_ACCEPTANCE_FAILED.String(),
			ResultMsg:  "split payment acceptance failed",
		}, nil
	}
	return &splitpaymentpb.SplitPaymentResponse{
		Result:     true,
		ResultCode: "SUCCESS",
	}, nil
}

// reverse split payment, 撤销分账操作(分账逆向)
func (s *splitPaymentService) ReverseSplitPayment(ctx context.Context, req *splitpaymentpb.ReverseSplitPaymentRequest) (
	*splitpaymentpb.ReverseSplitPaymentResponse, error) {

	zlog.Info(ctx, fmt.Sprintf("invoke ReverseSplitPayment, req=%+v", req))
	// 参数校验
	if err := s.checkReverseSplitPaymentRequest(req); err != nil {
		zlog.Error(ctx, "check request err", zap.Error(err))
		return &splitpaymentpb.ReverseSplitPaymentResponse{
			Result:     false,
			ResultCode: errorsModelsV1.Code_CODE_SPLIT_PAYMENT_ACCEPTANCE_FAILED.String(),
			ResultMsg:  "reverse split payment reqeust invaild",
		}, nil
	}

	if err := s.splitPayment.ReverseSplitPayment(ctx, req); err != nil {
		zlog.Error(ctx, "reverse split payment acceptance failed", zap.Error(err))
		// 这里是异步处理，同步返回的是受理结果，这里有err就是受理失败了
		return &splitpaymentpb.ReverseSplitPaymentResponse{
			Result:     false,
			ResultCode: errorsModelsV1.Code_CODE_SPLIT_PAYMENT_ACCEPTANCE_FAILED.String(),
			ResultMsg:  "reverse split payment acceptance failed",
		}, nil
	}
	return &splitpaymentpb.ReverseSplitPaymentResponse{
		Result:     true,
		ResultCode: "SUCCESS",
	}, nil
}

func (s *splitPaymentService) SyncReverseSplitPayment(ctx context.Context,
	req *splitpaymentpb.ReverseSplitPaymentRequest) (*splitpaymentpb.ReverseSplitPaymentResponse, error) {

	zlog.Info(ctx, fmt.Sprintf("invoke SyncReverseSplitPayment, req=%+v", req))
	// 参数校验，出现异常的不能重试，返回明确结果
	if err := s.checkReverseSplitPaymentRequest(req); err != nil {
		zlog.Error(ctx, "check request err", zap.Error(err))
		return &splitpaymentpb.ReverseSplitPaymentResponse{
			Result:     false,
			ResultCode: errorsModelsV1.Code_CODE_SPLIT_PAYMENT_ACCEPTANCE_FAILED.String(),
			ResultMsg:  "sync reverse split payment reqeust invaild",
		}, nil
	}

	result, err := s.splitPayment.SyncReverseSplitPayment(ctx, req)
	// 有error的，直接抛，让上游重试
	if err != nil {
		zlog.Error(ctx, "sync reverse split payment failed", zap.Error(err))
		return nil, status.Error(codes.Unknown, "sync reverse split payment error")
	}
	return &splitpaymentpb.ReverseSplitPaymentResponse{
		Result:     result,
		ResultCode: "SYNC_EXECUTE_COMPLETED",
	}, nil
}

// // get split detail, 获取分账明细
// func (s *splitPaymentService) GetSplitDetailList(_ context.Context, _ *splitpaymentpb.GetSplitDetailsRequest) (
// 	*splitpaymentpb.GetSplitDetailsResponse, error) {
// 	//TODO: 后续此方法需要查询loan的接口获取具体分账明细
// 	return &splitpaymentpb.GetSplitDetailsResponse{}, nil
// }

// // get refund detail, 获取退款分账明细
// func (s *splitPaymentService) GetRefundDetailList(_ context.Context, _ *splitpaymentpb.GetRefundDetailsRequest) (
// 	*splitpaymentpb.GetRefundDetailsResponse, error) {
// 	//TODO: 后续此方法需要查询loan的接口获取具体分账明细
// 	return &splitpaymentpb.GetRefundDetailsResponse{}, nil
// }

// 提供给定时任务调用的接口
func (s *splitPaymentService) RetrySplitPaymentForTask(ctx context.Context, _ *emptypb.Empty) (*emptypb.Empty, error) {
	zlog.Info(ctx, "invoke RetrySplitPaymentForTask")
	if err := s.splitPayment.RetrySplitPayment(ctx); err != nil {
		zlog.Error(ctx, "retry to split payment failed", zap.Error(err))
		return nil, status.Error(codes.Unknown, "retry to split payment failed")
	}
	return &emptypb.Empty{}, nil
}

func (s *splitPaymentService) BatchGetDetailList(ctx context.Context,
	reqeust *splitpaymentpb.BatchGetDetailRequest) (*splitpaymentpb.BatchGetDetailResponse, error) {

	zlog.Info(ctx, fmt.Sprintf("invoke BatchGetDetailList, req=%+v", reqeust))
	if reqeust == nil || len(reqeust.ExternalTransactionIdList) <= 0 {
		return nil, status.Error(codes.InvalidArgument, "split payment request is nil")
	}

	result, err := s.splitPayment.BatchGetDetailList(ctx, reqeust)

	if err != nil {
		return nil, err
	}
	return &splitpaymentpb.BatchGetDetailResponse{
		DetailList: result,
	}, nil
}

func (s *splitPaymentService) RetryBatchChargeForTask(ctx context.Context, _ *emptypb.Empty) (*emptypb.Empty, error) {
	zlog.Info(ctx, "invoke RetryBatchChargeForTask")
	if err := s.splitPayment.RetryBatchCharge(ctx); err != nil {
		zlog.Error(ctx, "RetryBatchChargeForTask failed", zap.Error(err))
		return nil, status.Error(codes.Unknown, "RetryBatchChargeForTask failed")
	}
	return &emptypb.Empty{}, nil
}

func (s *splitPaymentService) BatchChargeForTask(ctx context.Context, _ *emptypb.Empty) (*emptypb.Empty, error) {
	zlog.Info(ctx, "invoke BatchChargeForTask")
	if err := s.splitPayment.BatchChargeForTask(ctx); err != nil {
		zlog.Error(ctx, "BatchChargeForTask failed", zap.Error(err))
		return nil, status.Error(codes.Unknown, "BatchChargeForTask failed")
	}
	return &emptypb.Empty{}, nil
}

// 校验分账请求参数
func (s *splitPaymentService) checkSplitPaymentRequest(req *splitpaymentpb.SplitPaymentRequest) error {

	if req == nil {
		return status.Error(codes.InvalidArgument, "split payment request is nil")
	}

	// 分账金额为负数
	totalAmount := req.TotalAmount
	if money.IsMoneyNegative(totalAmount) {
		return status.Error(codes.InvalidArgument, "split total amount is negative")
	}

	// 没有平台分账金额直接返回
	platformSplitList := req.PlatformSplitList
	if len(platformSplitList) == 0 {
		return nil
	}

	// 平台的分账金额大于总金额，直接抛出异常
	var platformAmountList = make([]*googlemoney.Money, 0)
	for _, platformSplit := range platformSplitList {
		platformAmountList = append(platformAmountList, platformSplit.Amount)
	}
	totalPlatformMoney := money.GetTotalMoneySum(platformAmountList)
	if money.IsMoneyGreaterThan(totalPlatformMoney, totalAmount) {
		return status.Error(codes.InvalidArgument, "total platform split amount is greater than total amount")
	}

	return nil
}

// 校验逆向分账请求参数
func (s *splitPaymentService) checkReverseSplitPaymentRequest(req *splitpaymentpb.ReverseSplitPaymentRequest) error {

	if req == nil {
		return status.Error(codes.InvalidArgument, "reverse split payment request is nil")
	}

	// 逆向分账金额为负数
	totalAmount := req.ReverseAmount
	if money.IsMoneyNegative(totalAmount) {
		return status.Error(codes.InvalidArgument, "reverse total amount is negative")
	}

	// 没有平台逆向分账金额直接返回
	platformReverseList := req.PlatformReverseList
	if len(platformReverseList) == 0 {
		return nil
	}

	// 平台逆向分账金额大于总金额，直接抛出异常
	var platformReverseAmountList = make([]*googlemoney.Money, 0)
	for _, platformReverse := range platformReverseList {
		platformReverseAmountList = append(platformReverseAmountList, platformReverse.Amount)
	}
	totalPlatformReverseMoney := money.GetTotalMoneySum(platformReverseAmountList)
	if money.IsMoneyGreaterThan(totalPlatformReverseMoney, totalAmount) {
		return status.Error(codes.InvalidArgument, "total platform reverse amount is greater than total reverse amount")
	}

	return nil
}
