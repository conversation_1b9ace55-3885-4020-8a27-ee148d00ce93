package datadog

import (
	"context"
	"testing"

	"gotest.tools/v3/assert"

	"github.com/MoeGolibrary/moego/backend/app/tools/configinit"
)

func TestOpenAIClient_RecheckByAI_Normal(t *testing.T) {
	t.Skip("manual test")
	setup()
	config := configinit.GetMessageAlertCfgFromNacos()
	t.Logf("config:%+v", config)
	client := NewAuditClient(config)

	// Test normal messages that should be considered safe
	normalTexts := []string{
		"Hello, Could you please confirm your appointment day?",
		"Thank you for choosing our pet grooming service.",
		"Your appointment is scheduled for tomorrow at 2 PM.",
		"Please bring your pet's vaccination records.",
		"We look forward to seeing you and your pet soon!",
	}

	for _, text := range normalTexts {
		got, err := client.RecheckByAI(context.Background(), text)
		assert.Equal(t, nil, err)
		t.Logf("Normal text: %s -> Result: %s", text, got)
	}
}

func TestOpenAIClient_RecheckByAI_Suspicious(t *testing.T) {
	t.<PERSON><PERSON>("manual test")
	config := getMockConfig()
	t.Logf("config:%+v", config)
	client := NewAuditClient(config)

	// Test suspicious messages that might be flagged as malicious
	suspiciousTexts := []string{
		`moe_account_id:996359, receiverName:foo-bar
原文:someone hacked the account and he's asking for 800$ crypto 	to give us back the access are familiar with crypto ? i don't know how to do that !!
分数:85；原因:涉及加密货币付款（禁止类：高风险金融服务-加密货币），疑似诈骗引导`,

		`moe_account_id:449975, receiverName:foo-bar
原文:Immigration recommendation, lead to America, telegram: ********
分数:80；原因:第三方线索引流/移民中介推广，疑似欺骗性营销（禁止类：Third-party lead generation services and marketing）`,

		`moe_account_id:172070, receiverName:foo-bar
原文:Selling clips + tele ******** beautiful girl
分数:95；原因:涉性内容与招揽（禁止类：S.H.A.F.T. - Sex）`,
	}

	for _, text := range suspiciousTexts {
		got, err := client.RecheckByAI(context.Background(), text)
		assert.Equal(t, nil, err)
		t.Logf("Result: %s", got)
	}
}
