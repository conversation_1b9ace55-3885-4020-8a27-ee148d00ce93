package datadog

import (
	"context"
	"io/ioutil"
	"math/rand"
	"os"
	"path/filepath"
	"testing"

	yaml "gopkg.in/yaml.v2"
	"gotest.tools/v3/assert"

	"github.com/MoeGolibrary/moego/backend/app/tools/configinit"
)

// getMockConfig creates a mock configuration for testing by reading from local YAML file
func getMockConfig() *configinit.MessageConfig {
	// Get current working directory
	currentDir, _ := os.Getwd()

	// Navigate to the config file path relative to the current directory
	configPath := filepath.Join(currentDir, "..", "..", "config", "local", "message-alert.yaml")

	// Read the YAML file
	data, err := ioutil.ReadFile(configPath)
	if err != nil {
		panic("Failed to read config file: " + err.Error())
	}

	// Parse YAML into MessageConfig struct
	config := &configinit.MessageConfig{}
	if err := yaml.Unmarshal(data, config); err != nil {
		panic("Failed to parse config YAML: " + err.Error())
	}

	return config
}

func TestOpenAIClient_AuditLogs_Normal(t *testing.T) {
	t.Skip("manual test")
	setup()
	config := getMockConfig()
	t.Logf("config:%+v", config)
	client := NewAuditClient(config)
	texts := []string{
		"Please respond with one of the following to SECURE your appointment day:\n\n'Y' to Confirm\n'N' to Cancel",
		"Hello, Could you please confirm your appointment day?",
		"Please pay your open invoice through our secured portal: https://client.moego.pet/payment/online/5f6db7ac8d31462c9a8bdb18003151b3",
		"https://venmo.com/u/ChristopherSeigler；",
		"Hey Kristina, can you PAYID to 0406488801 (A Ryan) for $70 for wash and nails, thank you!!",
		"$135 https://venmo.com/u/Martinho-Santos Thank you",
		"Just have a google for dog safe antihistamines there is an ingredient in some that dogs can’t have, see you next time x",
		"https://amzn.eu/d/75NsBS5",
		"Thank you for choosing Zoomies Pet Grooming Salon. We would love for you to leave us a review by clicking the link below! Thank you! https://g.page/r/CZbK9Mfz-VH1EB0/review",
		"Hi Elaine, this is Puppuccino Darling Square  Thank you for dropping by to ask about scaling!         We've checked Lucky's teeth condition and recommend that you take him to the vet for scaling. There are swollen areas on the gum and need to be treated as soon as possible.         Thanks so much!",
		"Price 145,00 number Zelle ********** Felipe Erickson",
	}
	body := []map[string]interface{}{}
	for _, v := range texts {
		body = append(body, map[string]interface{}{"messageBody": v,
			"moe_account_id": rand.Intn(1000000), "receiverName": "foo-bar"})
	}
	got, err := client.AuditLogs(context.Background(), body)
	assert.Equal(t, nil, err)
	t.Logf("got:%s", got)
	assert.Equal(t, got, "无风险短信")
}

func TestOpenAIClient_AuditLogs(t *testing.T) {
	t.Skip("manual test")
	config := getMockConfig()
	t.Logf("config:%+v", config)
	client := NewAuditClient(config)
	texts := []string{
		"someone hacked the account and he's asking for 800$ crypto \nto give us back the access are familiar with crypto ? i don't know how to do that !!",
		"Please respond with one of the following to SECURE your appointment day:\n\n'Y' to Confirm\n'N' to Cancel",
		"Immigration recommendation, lead to America, telegram: ********",
		"Selling clips + tele ******** beautiful girl",
	}
	body := []map[string]interface{}{}
	for _, v := range texts {
		body = append(body, map[string]interface{}{"messageBody": v,
			"moe_account_id": rand.Intn(1000000), "receiverName": "foo-bar"})
	}
	got, err := client.AuditLogs(context.Background(), body)
	assert.Equal(t, nil, err)
	t.Logf("got:%s", got)
}
