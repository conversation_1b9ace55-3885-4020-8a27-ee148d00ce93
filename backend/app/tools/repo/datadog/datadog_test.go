package datadog

import (
	"encoding/json"
	"fmt"
	"os"
	"testing"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"

	"github.com/MoeGolibrary/moego/backend/app/tools/configinit"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/config/nacos"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/debuglog"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/recovery"
	rpc "github.com/MoeGolibrary/moego/backend/common/rpc/framework"
)

func setup() {
	currentDir, _ := os.Getwd()
	fmt.Printf("currentDir:%v\n", currentDir)
	currentDir = currentDir[:len(currentDir)-len("repo/datadog")]
	rpc.ServerConfigPath = currentDir + "config/local/config.yaml"
	_ = rpc.NewServer()
}

func TestNewDDClient(t *testing.T) {
	t.Skip("manual test")
	setup()
	config := configinit.GetMessageAlertCfgFromNacos()
	t.Logf("config:%+v", config)

	// Create client
	client, err := NewDDClient(config)
	assert.NoError(t, err)
	assert.NotNil(t, client)
	assert.NotNil(t, client.client)
	assert.NotNil(t, client.ctx)

	// Skip the actual API call in unit tests to avoid network dependencies
	logs, err := client.MessageLogs()
	assert.NoError(t, err)
	t.Logf("%s", lo.Must(json.Marshal(logs)))
}
