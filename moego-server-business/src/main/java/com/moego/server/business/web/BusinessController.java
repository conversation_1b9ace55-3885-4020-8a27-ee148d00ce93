package com.moego.server.business.web;

import com.moego.common.distributed.LockManager;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.response.ResponseResult;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.CountryUtils;
import com.moego.common.utils.StringMoegoUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.server.business.config.SessionConfig;
import com.moego.server.business.dto.CalendarSettingDTO;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.dto.SmartScheduleSettingDTO;
import com.moego.server.business.mapperbean.MoeBusiness;
import com.moego.server.business.mapperbean.MoeStaff;
import com.moego.server.business.params.SmartScheduleSettingParams;
import com.moego.server.business.service.BusinessDateFormatUtil;
import com.moego.server.business.service.BusinessService;
import com.moego.server.business.service.BusinessSessionService;
import com.moego.server.business.service.StaffService;
import com.moego.server.business.service.dto.BusinessOptionsDto;
import com.moego.server.business.service.dto.BusinessSettingDTO;
import com.moego.server.business.service.dto.CalendarSettingUpdateDTO;
import com.moego.server.business.service.dto.CreateBusinessReturnDto;
import com.moego.server.business.vo.BusinessInitVo;
import com.moego.server.business.web.request.SwitchBusinessRequest;
import com.moego.server.business.web.vo.SmartScheduleSettingVO;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(path = "/business")
public class BusinessController {

    // 商家头像路径最大长度
    public static final int AVATAR_PATH_LENGTH = 255;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private StaffService staffService;

    @Autowired
    private BusinessSessionService businessSessionService;

    @Autowired
    private SessionConfig sessionConfig;

    @Autowired
    private HttpServletResponse httpResponse;

    @Autowired
    private LockManager lockManager;

    @Autowired
    private MigrateHelper migrateHelper;

    @PostMapping("/create")
    @Auth(AuthType.ACCOUNT)
    public ResponseResult<CreateBusinessReturnDto> insert(@RequestBody BusinessInitVo initVo) {
        throw new CommonException(
                ResponseCodeEnum.PARAMS_ERROR,
                "Please update your app to the latest version before creating a new business.");
    }

    @GetMapping("/setting")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<BusinessSettingDTO> getBusinessSetting(AuthContext context) {
        return ResponseResult.success(businessService.getBusinessCurrentSetting(context.getBusinessId()));
    }

    @GetMapping("/options")
    @Auth(AuthType.ANONYMOUS)
    public ResponseResult<BusinessOptionsDto> getBusinessOptions() {
        return ResponseResult.success(businessService.getBusinessOptions());
    }

    @GetMapping("/info")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<MoeBusinessDto> getBusinessInfo(AuthContext context) {
        MoeBusinessDto moeBusinessDto = new MoeBusinessDto();
        MoeBusiness moeBusiness = businessService.getBusinessInfo(context.getBusinessId());
        BeanUtils.copyProperties(moeBusiness, moeBusinessDto);
        moeBusinessDto.setDateFormat(BusinessDateFormatUtil.getDateFormatByType(
                moeBusiness.getDateFormatType().intValue()));
        return ResponseResult.success(moeBusinessDto);
    }

    /**
     * 修改business的信息，修改preference
     *
     * @param context
     * @param business
     */
    @PutMapping(value = "/info")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<Boolean> updateBusinessInfo(AuthContext context, @RequestBody MoeBusiness business) {
        // 这里先校验几个容易超出长度的 URL 参数，入参后续需要配合前端一起修改
        checkParams(business);
        // 几个重要数据禁止通过 api 修改
        business.setCompanyId(null);
        business.setBusinessMode(null);
        business.setCountry(null);
        business.setAppType(null);
        business.setId(context.getBusinessId());
        if (business.getBusinessMode() != null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "business mode is not allowed to change.");
        }
        if (StringMoegoUtil.isOversize(business.getAvatarPath(), AVATAR_PATH_LENGTH)) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "business avatar path too long.");
        }
        // Update country alpha2 code
        if (Objects.nonNull(business.getCountry()) && Objects.isNull(business.getCountryAlpha2Code())) {
            business.setCountryAlpha2Code(CountryUtils.getCountryTwoCodes(business.getCountry()));
        }
        if (StringUtils.isBlank(business.getInvitationCode())) {
            return ResponseResult.success(businessService.updateBusinessSelective(business));
        }
        String resourceKey = lockManager.getResourceKey(LockManager.INVITATION_CODE, business.getInvitationCode());
        String uuid = CommonUtil.getUuid();
        try {
            if (!lockManager.lock(resourceKey, uuid)) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Invitation code already exists");
            }
            Integer businessId = businessService.getBusinessIdByInvitationCode(business.getInvitationCode());
            if (Objects.nonNull(businessId)) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Invitation code already exists");
            }
            return ResponseResult.success(businessService.updateBusinessSelective(business));
        } finally {
            lockManager.unlock(resourceKey, uuid);
        }
    }

    private void checkParams(MoeBusiness business) {
        boolean facebookTooLong = StringUtils.isNotBlank(business.getFacebook())
                && business.getFacebook().length() > 1000;
        if (facebookTooLong) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "facebook too long.");
        }
        boolean instagramTooLong = StringUtils.isNotBlank(business.getInstagram())
                && business.getInstagram().length() > 1000;
        if (instagramTooLong) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "instagram too long.");
        }
        boolean googleTooLong = StringUtils.isNotBlank(business.getGoogle())
                && business.getGoogle().length() > 1000;
        if (googleTooLong) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "google too long.");
        }
        boolean yelpTooLong =
                StringUtils.isNotBlank(business.getYelp()) && business.getYelp().length() > 1000;
        if (yelpTooLong) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "yelp too long.");
        }
    }

    @GetMapping("/calendar/setting")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<CalendarSettingDTO> getCalendarSetting(AuthContext context) {
        return ResponseResult.success(businessService.getCalendarSetting(context.getBusinessId()));
    }

    @PutMapping("/calendar/setting")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<Integer> updateCalendarSetting(
            AuthContext context, @RequestBody CalendarSettingUpdateDTO calendarSetting) {
        String validationResult = CalendarSettingUpdateDTO.validate(calendarSetting);
        if (!validationResult.equals("VALID")) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, validationResult);
        }
        businessService.updateCalendarSetting(
                context.companyId(), context.getBusinessId(), context.getAccountId(), calendarSetting);
        return ResponseResult.success(1);
    }

    @GetMapping("/smartSchedule/setting")
    @Auth(AuthType.COMPANY)
    public ResponseResult<SmartScheduleSettingVO> getSmartScheduleSetting(AuthContext context) {
        SmartScheduleSettingVO smartScheduleSettingVO = new SmartScheduleSettingVO();
        SmartScheduleSettingDTO smartScheduleSettingDTO =
                businessService.getSmartScheduleSetting(context.companyId(), context.businessId());
        BeanUtils.copyProperties(smartScheduleSettingDTO, smartScheduleSettingVO);
        return ResponseResult.success(smartScheduleSettingVO);
    }

    @PutMapping("/smartSchedule/setting")
    @Auth(AuthType.COMPANY)
    public ResponseResult<SmartScheduleSettingVO> updateSmartScheduleSetting(
            AuthContext context, @RequestBody SmartScheduleSettingParams setting) {
        SmartScheduleSettingVO smartScheduleSettingVO = new SmartScheduleSettingVO();
        SmartScheduleSettingDTO smartScheduleSettingDTO = businessService.updateSmartScheduleSetting(
                context.companyId().intValue(),
                businessService.getBusinessIdForMobile(context.companyId(), context.businessId()),
                setting);
        BeanUtils.copyProperties(smartScheduleSettingDTO, smartScheduleSettingVO);
        return ResponseResult.success(smartScheduleSettingVO);
    }

    @PostMapping("/staff/insert")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<Integer> insert(AuthContext context, @RequestBody MoeStaff moeStaff) {
        moeStaff.setBusinessId(context.getBusinessId());
        moeStaff.setCreateById(context.getStaffId());
        return ResponseResult.success(businessService.insertStaff(moeStaff));
    }

    @PostMapping("/switch")
    @Auth(AuthType.ACCOUNT)
    public void switchBusiness(@RequestBody @Valid SwitchBusinessRequest request) {
        AuthContext context = AuthContext.get();
        int accountId = context.getAccountId();
        Long sessionId = context.sessionId();
        Integer fromBusinessId = context.getBusinessId();
        int toBusinessId = request.businessId();

        // 1. from = to, no need to switch
        if (Objects.equals(fromBusinessId, toBusinessId)) {
            return;
        }

        // 2. switch business
        var staff = staffService.switchBusiness(accountId, toBusinessId);
        if (staff == null) {
            throw ExceptionUtil.bizException(Code.CODE_SWITCH_BUSINESS_ERROR);
        }

        // 3. update session data
        businessSessionService.updateSessionData(
                sessionId, staff.getCompanyId(), staff.getBusinessId(), staff.getId(), null);
    }
}
