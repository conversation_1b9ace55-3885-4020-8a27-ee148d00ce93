package com.moego.server.business.web;

import com.google.protobuf.Duration;
import com.moego.common.enums.VersionEnums;
import com.moego.common.utils.PermissionUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.organization.v1.GetStaffDetailRequest;
import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.business.config.SessionConfig;
import com.moego.server.business.service.AccountService;
import com.moego.server.business.service.BusinessService;
import com.moego.server.business.service.BusinessSessionService;
import com.moego.server.business.service.CompanyService;
import com.moego.server.business.service.RelevantAccountService;
import com.moego.server.business.service.StaffService;
import com.moego.server.business.service.WebSocketTokenDTO;
import com.moego.server.business.service.dto.AccountRelatedInformationV2Dto;
import com.moego.server.business.service.dto.MyAccountCenterInfoDto;
import com.moego.server.business.web.request.SwitchAccountRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/business/account/v2")
public class AccountV2Controller {

    @Autowired
    private CompanyService companyService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private StaffService staffService;

    @Autowired
    private BusinessSessionService businessSessionService;

    @Autowired
    private RelevantAccountService relevantAccountService;

    @Autowired
    private HttpServletResponse httpResponse;

    @Autowired
    private SessionConfig sessionConfig;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private StaffServiceGrpc.StaffServiceBlockingStub staffServiceBlockingStub;

    @PostMapping("/switch")
    @Auth(AuthType.ACCOUNT)
    public void switchAccount(@RequestBody @Valid SwitchAccountRequest request) {
        AuthContext context = AuthContext.get();
        var sessionId = context.sessionId();
        var fromAccountId = context.accountId().intValue();
        var toAccountId = request.accountId().intValue();
        var impersonator = context.impersonator();

        if (fromAccountId == toAccountId) {
            return;
        }

        // 1. get relevant account
        var accountList = relevantAccountService.getAccountRelevantInfo(fromAccountId);
        var relevantAccount = accountList.stream()
                .filter(account -> Objects.equals(toAccountId, account.getAccountId()))
                .findFirst()
                .orElseThrow(() -> ExceptionUtil.bizException(Code.CODE_ACCOUNT_NOT_EXIST));

        // 2. create session and cookie for relevant account
        var sessionContext = sessionConfig.getSessionContext();
        var companyId = relevantAccount.getCompanyId();
        var businessId = relevantAccount.getBusinessId();
        var staffId = relevantAccount.getStaffId();
        var isOwner = PermissionUtil.hasOwnerPermission(relevantAccount.getEmployeeCategory());
        var maxAge = businessSessionService.getNewMaxAgeForOwner(sessionId, companyId, isOwner);
        if (businessId.equals(0)) {
            var workingLocationList = staffServiceBlockingStub
                    .getStaffDetail(GetStaffDetailRequest.newBuilder()
                            .setCompanyId(companyId)
                            .setId(staffId)
                            .build())
                    .getStaff()
                    .getWorkingLocationListList();
            businessId = (int) workingLocationList.get(0).getId();
        }
        businessSessionService.createSessionAndSetCookie(
                toAccountId,
                companyId,
                businessId,
                staffId,
                sessionId,
                maxAge,
                impersonator,
                sessionContext,
                httpResponse);

        // 3. delete current session
        businessSessionService.deleteSession(sessionId);
    }

    @GetMapping("/queryWithBusiness")
    @Auth(AuthType.ACCOUNT)
    public MyAccountCenterInfoDto queryWithBusiness() {
        var accountId = AuthContext.get().accountId().intValue();
        var result = accountService.queryWithBusiness(accountId);
        // clear account token
        if (result.getAccount() != null) {
            result.getAccount().setToken(null);
        }
        // clear staff token
        if (result.getBusinessInfo() != null) {
            for (var business : result.getBusinessInfo()) {
                business.setStaffToken(null);
            }
        }
        // clear account token and staff token for relevant account
        if (result.getRelevantAccountDto() != null) {
            for (var account : result.getRelevantAccountDto()) {
                account.setAccountToken(null);
                account.setStaffToken(null);
            }
        }
        return result;
    }

    @GetMapping("/info")
    @Auth(AuthType.ANONYMOUS)
    public AccountRelatedInformationV2Dto info(AuthContext context) {
        var accountId = context.getAccountId();
        var businessId = context.getBusinessId();
        var staffId = context.getStaffId();
        var sessionId = context.sessionId();
        var companyId = context.getCompanyId();

        var result = accountService.getAccountRelatedInformationV2(accountId, companyId, businessId, staffId);
        if (companyId != null && companyService.isInBoardingWhiteList(companyId.longValue())) {
            result.setVersion(VersionEnums.VERSION_BOARDING_DAYCARE.getVersion());
        } else {
            result.setVersion(VersionEnums.VERSION_NEW_ACCOUNT_STRUCTURE.getVersion());
        }
        if (businessId != null) {
            result.setPreference(businessService.getBusinessPreferenceFromCompanyLevel(businessId));
        }

        var sessionContext = sessionConfig.getSessionContext();
        if (sessionId == null && accountId != null) {
            Duration maxAge = null;
            var staff = result.getStaff();
            if (staff != null && companyId != null) {
                var isOwner = PermissionUtil.hasOwnerPermission(staff.getEmployeeCategory());
                maxAge = businessSessionService.getNewMaxAgeForOwner(null, companyId, isOwner);
            }

            businessSessionService.createSessionAndSetCookie(
                    accountId, companyId, businessId, staffId, null, maxAge, "", sessionContext, httpResponse);
        }
        if (result.getStaff() != null && staffId != null && businessId != null) {
            ThreadPool.execute(() -> {
                staffService.updateAccountLastVisitedInfo(staffId, businessId);
            });
        }

        return result;
    }

    /**
     * 前端发起 ws 长连接之前，需要调这个接口申请一个 token，再用 token 建立 ws 长连接
     * 返回的 token 有效期为 10 分钟，需要在有效期内建立 ws 长连接，否则需要重新申请
     *
     * @param context
     * @return web socket token
     */
    @PostMapping("/ws")
    @Auth(AuthType.ACCOUNT)
    public WebSocketTokenDTO ws(AuthContext context) {
        var accountId = context.accountId();
        var companyId = context.getCompanyId();
        var businessId = context.getBusinessId();
        var staffId = context.getStaffId();
        var sessionId = context.sessionId();
        var impersonator = context.impersonator();

        var token = businessSessionService.createWebSocketSession(
                accountId, companyId, businessId, staffId, sessionId, impersonator);
        return new WebSocketTokenDTO(token);
    }
}
