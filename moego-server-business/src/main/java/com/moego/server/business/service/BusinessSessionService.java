package com.moego.server.business.service;

import com.google.protobuf.Duration;
import com.google.protobuf.util.Durations;
import com.moego.common.enums.StaffEnum;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.organization.v1.LocationBriefView;
import com.moego.idl.models.organization.v1.LocationModel;
import com.moego.idl.service.account.v1.CreateSessionRequest;
import com.moego.idl.service.account.v1.DeleteSessionByIdRequest;
import com.moego.idl.service.account.v1.GetSessionRequest;
import com.moego.idl.service.account.v1.SessionServiceGrpc;
import com.moego.idl.service.account.v1.UpdateSessionRequest;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.GetLocationListByEnterpriseStaffRequest;
import com.moego.idl.service.organization.v1.GetStaffDetailRequest;
import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import com.moego.idl.service.permission.v1.CheckPermissionRequest;
import com.moego.idl.service.permission.v1.PermissionServiceGrpc;
import com.moego.lib.common.auth.SessionData;
import com.moego.lib.common.auth.SessionDataKey;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.common.proto.ProtoUtils;
import com.moego.lib.common.util.RequestUtils;
import com.moego.lib.permission.PermissionEnums;
import com.moego.server.business.common.consts.AccountSourceConst;
import com.moego.server.business.config.SessionConfig;
import com.moego.server.business.mapperbean.MoeStaff;
import com.moego.server.business.service.dto.LoginTargetDTO;
import io.grpc.StatusRuntimeException;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
@Slf4j
public class BusinessSessionService {

    @Autowired
    private SessionServiceGrpc.SessionServiceBlockingStub sessionServiceBlockingStub;

    @Autowired
    private MigrateHelper migrateHelper;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private BusinessServiceGrpc.BusinessServiceBlockingStub businessServiceBlockingStub;

    @Autowired
    private StaffServiceGrpc.StaffServiceBlockingStub staffServiceBlockingStub;

    @Autowired
    private StaffService staffService;

    @Autowired
    private PermissionServiceGrpc.PermissionServiceBlockingStub permissionServiceBlockingStub;

    @Value("${moego.session.moego-pay-max-age:3600}")
    private Long moegoPayMaxAge;

    @Value("${moego.session.moego-pay-mobile-max-age:3600}")
    private Long moegoPayMobileMaxAge;

    private static final Duration WS_DURATION = Durations.fromMinutes(10);

    public String createWebSocketSession(
            long accountId,
            Integer companyId,
            Integer businessId,
            Integer staffId,
            Long sessionId,
            String impersonator) {
        var sessionData = companyId != null && businessId != null && staffId != null
                ? buildBusinessSessionData(companyId, businessId, staffId)
                : Map.of();

        var builder = CreateSessionRequest.newBuilder()
                .setAccountId(accountId)
                .setSource(AccountSourceConst.SOURCE_BUSINESS_WS)
                .setIp(RequestUtils.getIP())
                .setUserAgent(RequestUtils.getUserAgent())
                .setDeviceId(RequestUtils.getDeviceId())
                .setRefererLink(RequestUtils.getReferer())
                .setRefererSessionId(sessionId != null ? sessionId : 0)
                .setRenewable(false)
                .setMaxAge(WS_DURATION)
                .setSessionData(ProtoUtils.mapToStruct(sessionData))
                .setAllowFrozenAccount(false)
                .setAllowDeletedAccount(false);

        if (StringUtils.hasText(impersonator)) {
            builder.setImpersonator(impersonator);
        }

        var createResponse = sessionServiceBlockingStub.createSession(builder.build());
        return createResponse.getSessionToken();
    }

    public void createSessionAndSetCookie(
            long accountId,
            Integer companyId,
            Integer businessId,
            Integer staffId,
            Long sessionId,
            Duration maxAge,
            String impersonator,
            SessionConfig.SessionContext sessionContext,
            HttpServletResponse response) {
        var source = sessionContext.source();
        var targetDomain = sessionContext.targetDomain();

        // create session
        var sessionData = companyId != null && businessId != null && staffId != null
                ? buildBusinessSessionData(companyId, businessId, staffId)
                : Map.of();

        var builder = CreateSessionRequest.newBuilder()
                .setAccountId(accountId)
                .setSource(source.name())
                .setIp(sessionContext.ip())
                .setUserAgent(sessionContext.userAgent())
                .setDeviceId(RequestUtils.getDeviceId())
                .setRefererLink(sessionContext.refererLink())
                .setRefererSessionId(sessionId != null ? sessionId : 0)
                .setMaxAge(maxAge != null ? maxAge : Durations.fromSeconds(source.maxAge()))
                .setSessionData(ProtoUtils.mapToStruct(sessionData))
                .setAllowFrozenAccount(false)
                .setAllowDeletedAccount(false);

        if (StringUtils.hasText(impersonator)) {
            // impersonator 在 switch account 时，绕开了 MIS 的 renewable 限制
            // 如果要继承原先 impersonator 会话的 renewable，需要查一遍会话表，比较麻烦
            // 出于安全考虑，这里先写死 false，不允许会话续期
            builder.setImpersonator(impersonator).setRenewable(false);
        }

        var createResponse = sessionServiceBlockingStub.createSession(builder.build());
        var sessionToken = createResponse.getSessionToken();

        // set cookie
        Cookie sessionCookie = new Cookie(source.cookieName(), sessionToken);
        sessionCookie.setDomain(targetDomain);
        sessionCookie.setPath("/");
        sessionCookie.setMaxAge(source.maxAge());
        sessionCookie.setHttpOnly(true);
        sessionCookie.setSecure(true);
        response.addCookie(sessionCookie);
    }

    public Duration getNewMaxAgeForOwner(Long sessionId, int companyId, boolean isOwner) {
        try {
            var session = sessionId == null
                    ? null
                    : sessionServiceBlockingStub.getSession(
                            GetSessionRequest.newBuilder().setId(sessionId).build());

            var impersonator = session == null ? null : session.getImpersonator();
            var isMobile = session == null
                    ? RequestUtils.getUserAgent().toLowerCase().contains("moegobusiness")
                    : session.getUserAgent().toLowerCase().contains("moegobusiness");

            // impersonate 会话保持原有的 max age
            if (StringUtils.hasText(impersonator)) {
                return session.getMaxAge();
            }

            // 非 owner 会话暂不需要修改 max age
            if (!isOwner) {
                return null;
            }

            var isMoeGoPayUser = companyService.isMoeGoPayEnable(companyId);
            // 非 MoeGo pay 用户不需要修改 max age
            if (!isMoeGoPayUser) {
                return null;
            }

            // MoeGo pay owner 根据 web / app 端分别限制 max age
            return Durations.fromSeconds(isMobile ? moegoPayMobileMaxAge : moegoPayMaxAge);
        } catch (Exception e) {
            // log error and ignore it, since it's not a critical error
            log.error("getNewMaxAgeForOwner error for session id {}, companyId {}", sessionId, companyId, e);
        }
        return null;
    }

    public void updateSessionData(long sessionId, int companyId, int businessId, int staffId, Duration maxAge) {
        var sessionData = buildBusinessSessionData(companyId, businessId, staffId);

        var builder =
                UpdateSessionRequest.newBuilder().setId(sessionId).setSessionData(ProtoUtils.mapToStruct(sessionData));
        if (maxAge != null) {
            builder.setMaxAge(maxAge);
        }
        sessionServiceBlockingStub.updateSession(builder.build());
    }

    public void updateSessionData(long sessionId, SessionData sessionData, Duration maxAge) {
        Map<String, String> sessionDataMap = sessionData.buildMap();
        if (sessionData.getPlatformSessionData() != null
                && sessionData.getPlatformSessionData().getCompanyId() > 0) {
            var migrateStatus = migrateHelper.getMigrateStatusByCompanyId(
                    sessionData.getPlatformSessionData().getCompanyId());
            switch (migrateStatus) {
                case MIGRATING -> throw ExceptionUtil.bizException(
                        Code.CODE_FORBIDDEN, "Company data is migrating, please try again later");
                case MIGRATED -> sessionDataMap.put(SessionDataKey.SESSION_DATA_COMPANY_IS_MIGRATE, "true");
                default -> sessionDataMap.put(SessionDataKey.SESSION_DATA_COMPANY_IS_MIGRATE, "false");
            }
        }
        var builder = UpdateSessionRequest.newBuilder()
                .setId(sessionId)
                .setSessionData(ProtoUtils.mapToStruct(sessionDataMap));
        if (maxAge != null) {
            builder.setMaxAge(maxAge);
        }
        sessionServiceBlockingStub.updateSession(builder.build());
    }

    public Map<String, String> buildBusinessSessionData(int companyId, int businessId, int staffId) {
        Map<String, String> sessionData = new HashMap<>();

        var migrateStatus = migrateHelper.getMigrateStatusByCompanyId(companyId);
        switch (migrateStatus) {
            case MIGRATING -> throw ExceptionUtil.bizException(
                    Code.CODE_FORBIDDEN, "Company data is migrating, please try again later");
            case MIGRATED -> sessionData.put(SessionDataKey.SESSION_DATA_COMPANY_IS_MIGRATE, "true");
            default -> sessionData.put(SessionDataKey.SESSION_DATA_COMPANY_IS_MIGRATE, "false");
        }
        sessionData.put(SessionDataKey.SESSION_DATA_COMPANY_ID, String.valueOf(companyId));
        sessionData.put(SessionDataKey.SESSION_DATA_BUSINESS_ID, String.valueOf(businessId));
        sessionData.put(SessionDataKey.SESSION_DATA_STAFF_ID, String.valueOf(staffId));
        return sessionData;
    }

    public void deleteSession(long sessionId) {
        var deleteRequest =
                DeleteSessionByIdRequest.newBuilder().setId(sessionId).build();
        sessionServiceBlockingStub.deleteSessionById(deleteRequest);
    }

    // 获取 staff 的可登入的 company + business
    public LoginTargetDTO getLoginTarget(MoeStaff staff) {
        // 迁移前的 staff 只 work in 一个 business
        if (staff.getBusinessId() != 0) {
            // business 被删除，跳过这个 staff
            if (!businessService.isBusinessAvailable(staff.getBusinessId())) {
                return null;
            }
            return new LoginTargetDTO(staff.getCompanyId(), staff.getBusinessId(), staff);
        }
        if (!staff.getEnterpriseId().equals(0)) {
            var loginTargetDTO = getLoginTargetByEnterpriseStaff(staff);
            if (loginTargetDTO == null) {
                return null;
            }
            // 如果用户同时是 enterprise staff 和这个 company 的 owner, 优先用 company owner staff
            if (loginTargetDTO.getStaff().getEmployeeCategory().equals(StaffEnum.EMPLOYEE_CATEGORY_MASTER_OWNER)) {
                return loginTargetDTO;
            }
            var companyOwnerStaff =
                    getOwnerStaff(loginTargetDTO.getStaff().getAccountId(), loginTargetDTO.getCompanyId());
            if (companyOwnerStaff != null) {
                loginTargetDTO.setStaff(companyOwnerStaff);
            }
            return loginTargetDTO;
        }
        return getLoginTargetByCompanyStaff(staff);
    }

    public LoginTargetDTO getLoginTargetByEnterpriseStaff(MoeStaff staff) {
        if (!staff.getEmployeeCategory().equals(StaffEnum.EMPLOYEE_CATEGORY_MASTER_OWNER)) {
            var checkRes = permissionServiceBlockingStub.checkPermission(CheckPermissionRequest.newBuilder()
                    .setRoleId(staff.getRoleId())
                    .setEnterpriseId(staff.getEnterpriseId())
                    .addAllPermissionNameList(List.of(PermissionEnums.ENTERPRISE_ACCESS_TENANT.getPermissionName()))
                    .build());
            if (!checkRes.getNoPermissionListList().isEmpty()) {
                return null;
            }
        }
        var locations = businessServiceBlockingStub
                .getLocationListByEnterpriseStaff(GetLocationListByEnterpriseStaffRequest.newBuilder()
                        .setEnterpriseId(staff.getEnterpriseId())
                        .setStaffId(staff.getId())
                        .build())
                .getLocationsList();
        if (locations.isEmpty()) {
            log.error("enterprise {} staff {} working location list is empty", staff.getEnterpriseId(), staff.getId());
            return null;
        }

        Map<Long, List<Long>> companyLocationIdMap = locations.stream()
                .collect(Collectors.groupingBy(
                        LocationModel::getCompanyId, Collectors.mapping(LocationModel::getId, Collectors.toList())));

        if (!staff.getLastVisitBusinessId().equals(0)) {
            // find the last visit business in the company location map
            for (var entry : companyLocationIdMap.entrySet()) {
                if (entry.getValue().contains(staff.getLastVisitBusinessId().longValue())) {
                    staff.setCompanyId(entry.getKey().intValue());
                    return new LoginTargetDTO(staff.getCompanyId(), staff.getLastVisitBusinessId(), staff);
                }
            }
        }
        // if last visit business is not in the working location list, or it is 0
        // use the first company and first location
        var firstCompanyId = companyLocationIdMap.keySet().stream().findFirst().get();
        var firstLocationId =
                companyLocationIdMap.get(firstCompanyId).stream().findFirst().get();
        staff.setCompanyId(firstCompanyId.intValue());
        return new LoginTargetDTO(staff.getCompanyId(), firstLocationId.intValue(), staff);
    }

    public LoginTargetDTO getLoginTargetByCompanyStaff(MoeStaff staff) {
        List<Integer> workingLocationIds;
        try {
            var staffDetailRes = staffServiceBlockingStub
                    .getStaffDetail(GetStaffDetailRequest.newBuilder()
                            .setId(staff.getId())
                            .setCompanyId(staff.getCompanyId())
                            .build())
                    .getStaff();
            workingLocationIds = staffDetailRes.getWorkingLocationListList().stream()
                    .map(LocationBriefView::getId)
                    .map(Long::intValue)
                    .toList();
        } catch (StatusRuntimeException e) {
            if (ExceptionUtil.extractCode(e).equals(Code.CODE_STAFF_WORKING_LOCATION_LIST_IS_EMPTY)) {
                log.error("company {} staff {} working location list is empty", staff.getCompanyId(), staff.getId());
                return null;
            }
            throw e;
        }
        if (!staff.getLastVisitBusinessId().equals(0) && workingLocationIds.contains(staff.getLastVisitBusinessId())) {
            return new LoginTargetDTO(staff.getCompanyId(), staff.getLastVisitBusinessId(), staff);
        }
        return new LoginTargetDTO(staff.getCompanyId(), workingLocationIds.get(0), staff);
    }

    public MoeStaff getOwnerStaff(Integer accountId, Integer companyId) {
        var query = new MoeStaff();
        query.setCompanyId(companyId);
        query.setAccountId(accountId);
        query.setEmployeeCategory(StaffEnum.EMPLOYEE_CATEGORY_OWNER);
        query.setAllowLogin(StaffEnum.ALLOW_LOGIN_TRUE);
        query.setStatus(StaffEnum.STATUS_NORMAL);
        var staffs = staffService.queryMoeStaff(query);
        if (!staffs.isEmpty()) {
            return staffs.get(0);
        }
        return null;
    }
}
