package com.moego.svc.business.customer.controller;

import com.moego.idl.models.activity_log.v1.Resource;
import com.moego.idl.service.activity_log.v1.ActivityLogServiceGrpc;
import com.moego.idl.service.activity_log.v1.CreateActivityLogRequest;
import com.moego.idl.service.business_customer.v1.BatchGetPetIncidentReportRequest;
import com.moego.idl.service.business_customer.v1.BatchGetPetIncidentReportResponse;
import com.moego.idl.service.business_customer.v1.BatchGetPetIncidentReportResponse.IncidentReportsList;
import com.moego.idl.service.business_customer.v1.BusinessPetIncidentReportServiceGrpc;
import com.moego.idl.service.business_customer.v1.CreatePetIncidentReportRequest;
import com.moego.idl.service.business_customer.v1.CreatePetIncidentReportResponse;
import com.moego.idl.service.business_customer.v1.DeletePetIncidentReportRequest;
import com.moego.idl.service.business_customer.v1.DeletePetIncidentReportResponse;
import com.moego.idl.service.business_customer.v1.GetPetIncidentReportRequest;
import com.moego.idl.service.business_customer.v1.GetPetIncidentReportResponse;
import com.moego.idl.service.business_customer.v1.ListPetIncidentReportRequest;
import com.moego.idl.service.business_customer.v1.ListPetIncidentReportResponse;
import com.moego.idl.service.business_customer.v1.UpdatePetIncidentReportRequest;
import com.moego.idl.service.business_customer.v1.UpdatePetIncidentReportResponse;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.util.JsonUtil;
import com.moego.svc.business.customer.converter.PetIncidentReportConverter;
import com.moego.svc.business.customer.service.PetIncidentReportService;
import com.moego.svc.business.customer.utils.CustomerPetActionUtils;
import io.grpc.stub.StreamObserver;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class PetIncidentReportController
        extends BusinessPetIncidentReportServiceGrpc.BusinessPetIncidentReportServiceImplBase {

    private final PetIncidentReportConverter petIncidentReportConverter;

    private final PetIncidentReportService petIncidentReportService;

    private final ActivityLogServiceGrpc.ActivityLogServiceBlockingStub activityLogServiceStub;

    @Override
    public void getPetIncidentReport(
            GetPetIncidentReportRequest request, StreamObserver<GetPetIncidentReportResponse> responseObserver) {
        var incidentReport = petIncidentReportService.getPetIncidentReport(request.getId(), request.getCompanyId());

        responseObserver.onNext(GetPetIncidentReportResponse.newBuilder()
                .setIncidentReport(incidentReport)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void listPetIncidentReport(
            ListPetIncidentReportRequest request, StreamObserver<ListPetIncidentReportResponse> responseObserver) {
        var incidentReports = petIncidentReportService.listPetIncidentReport(
                request.getCompanyId(), request.getBusinessId(), request.getPetId());
        responseObserver.onNext(ListPetIncidentReportResponse.newBuilder()
                .addAllIncidentReports(incidentReports)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void createPetIncidentReport(
            CreatePetIncidentReportRequest request, StreamObserver<CreatePetIncidentReportResponse> responseObserver) {

        var record = petIncidentReportConverter.createRecord(request);
        var id = petIncidentReportService.createPetIncidentReport(
                record, request.getIncidentReport().getPetIdsList());

        var incidentReport = petIncidentReportService.getPetIncidentReport(id, request.getCompanyId());

        activityLogServiceStub.createActivityLog(CreateActivityLogRequest.newBuilder()
                .setCompanyId(request.getCompanyId())
                .setBusinessId(request.getBusinessId())
                .setOperatorId(Long.toString(request.getStaffId()))
                .setAction(CustomerPetActionUtils.CREATE)
                .setResourceType(Resource.Type.PET_INCIDENT_REPORT)
                .setDetails(JsonUtil.toJson(request))
                .build());

        responseObserver.onNext(CreatePetIncidentReportResponse.newBuilder()
                .setIncidentReport(incidentReport)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void updatePetIncidentReport(
            UpdatePetIncidentReportRequest request, StreamObserver<UpdatePetIncidentReportResponse> responseObserver) {

        petIncidentReportService.updatePetIncidentReport(
                petIncidentReportConverter.updateRecord(request),
                request.getIncidentReport().getPetIdsList());

        activityLogServiceStub.createActivityLog(CreateActivityLogRequest.newBuilder()
                .setCompanyId(request.getCompanyId())
                .setBusinessId(request.getBusinessId())
                .setOperatorId(Long.toString(request.getStaffId()))
                .setAction(CustomerPetActionUtils.UPDATE)
                .setResourceType(Resource.Type.PET_INCIDENT_REPORT)
                .setDetails(JsonUtil.toJson(request))
                .build());

        responseObserver.onNext(UpdatePetIncidentReportResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void deletePetIncidentReport(
            DeletePetIncidentReportRequest request, StreamObserver<DeletePetIncidentReportResponse> responseObserver) {
        petIncidentReportService.deletePetIncidentReport(request.getCompanyId(), request.getId());

        activityLogServiceStub.createActivityLog(CreateActivityLogRequest.newBuilder()
                .setCompanyId(request.getCompanyId())
                .setBusinessId(request.getBusinessId())
                .setOperatorId(Long.toString(request.getStaffId()))
                .setAction(CustomerPetActionUtils.DELETE)
                .setResourceType(Resource.Type.PET_INCIDENT_REPORT)
                .setDetails(JsonUtil.toJson(request))
                .build());

        responseObserver.onNext(DeletePetIncidentReportResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void batchGetPetIncidentReport(
            BatchGetPetIncidentReportRequest request,
            StreamObserver<BatchGetPetIncidentReportResponse> responseObserver) {
        var incidentReports = petIncidentReportService.batchGetPetIncidentReport(
                request.getCompanyId(), request.getBusinessId(), request.getPetIdsList());

        Map<Long, IncidentReportsList> incidentReportsMap = new HashMap<>();

        request.getPetIdsList().forEach(petId -> {
            incidentReportsMap.put(
                    petId,
                    IncidentReportsList.newBuilder()
                            .addAllIncidentReports(incidentReports.stream()
                                    .filter(incidentReport ->
                                            incidentReport.getPetIdsList().contains(petId))
                                    .sorted(Comparator.comparing(
                                            incidentReport -> incidentReport
                                                    .getIncidentTime()
                                                    .getSeconds(),
                                            Comparator.reverseOrder()))
                                    .collect(Collectors.toList()))
                            .build());
        });

        responseObserver.onNext(BatchGetPetIncidentReportResponse.newBuilder()
                .putAllIncidentReportsMap(incidentReportsMap)
                .build());
        responseObserver.onCompleted();
    }
}
