package com.moego.svc.business.customer.service;

import static com.moego.idl.models.errors.v1.Code.CODE_PARAMS_ERROR;
import static com.moego.svc.business.customer.repository.jooq.Tables.MOE_PET_CODE;
import static com.moego.svc.business.customer.repository.jooq.Tables.MOE_PET_PET_CODE_BINDING;

import com.moego.idl.models.organization.v1.Tenant;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.svc.business.customer.enums.StatusEnum;
import com.moego.svc.business.customer.repository.jooq.tables.records.MoePetCodeRecord;
import com.moego.svc.business.customer.repository.jooq.tables.records.MoePetPetCodeBindingRecord;
import com.moego.svc.business.customer.utils.SortUtils;
import com.moego.svc.business.customer.utils.StatusUtils;
import com.moego.svc.business.customer.utils.TenantUtils;
import java.time.Instant;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.OrderField;
import org.jooq.impl.DSL;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class PetCodeService {
    private final DSLContext dsl;

    /**
     * Order by `moe_pet_code.sort desc, moe_pet_code.id asc`
     */
    private static final OrderField<?>[] ORDER_FIELDS =
            new OrderField[] {MOE_PET_CODE.SORT.desc(), MOE_PET_CODE.ID.asc()};

    public Condition tenantCondition(Tenant tenant) {
        return MOE_PET_CODE.COMPANY_ID.eq(tenant.getCompanyId());
    }

    /**
     * Get a pet code.
     * <br>
     * SQL:
     * ```
     * select {all fields} from moe_pet_code
     * where id = ? and {tenant condition};
     * ```
     *
     * @param id     pet code id
     * @param tenant tenant
     * @return pet code
     */
    public MoePetCodeRecord getPetCode(Tenant tenant, long id) {
        var conditions = DSL.and(MOE_PET_CODE.ID.eq((int) id), tenantCondition(tenant));
        var code = dsl.fetchOne(MOE_PET_CODE, conditions);
        if (code == null) {
            // TODO: add new error code
            throw ExceptionUtil.bizException(CODE_PARAMS_ERROR, "Code not found");
        }
        return code;
    }

    /**
     * List pet codes by tenant.
     * <br>
     * SQL:
     * ```
     * select {all fields} from moe_pet_code
     * where {tenant condition} and status = 1
     * order by sort desc, id asc;
     * ```
     *
     * @param tenant tenant
     * @return pet code list, or empty list if not found
     */
    public List<MoePetCodeRecord> listPetCode(Tenant tenant) {
        return dsl.selectFrom(MOE_PET_CODE)
                .where(tenantCondition(tenant))
                .and(MOE_PET_CODE.STATUS.eq(StatusEnum.NORMAL.getValue()))
                .orderBy(ORDER_FIELDS)
                .fetch();
    }

    /**
     * Create a pet code.
     * If the abbr (code number) is already used in the tenant, throw exception.
     * The sort value will be set to the id by default, which means the code will be displayed last.
     *
     * @param record pet code to create
     * @return created pet code
     */
    @Transactional
    public MoePetCodeRecord createPetCode(MoePetCodeRecord record) {
        checkAbbrUsed(record);
        var needUpdateSort = record.getSort() == null;
        record.insert();
        if (needUpdateSort) {
            record.setSort(record.getId()).update();
            record.refresh();
        }
        return record;
    }

    /**
     * Update a pet code.
     * If the abbr (code number) is already used in the tenant, throw exception.
     *
     * @param record pet code to update
     */
    public void updatePetCode(MoePetCodeRecord record) {
        if (!record.changed()) {
            log.warn("pet record id {} is not changed", record.getId());
            return;
        }
        if (record.changed(MOE_PET_CODE.CODE_NUMBER)) {
            checkAbbrUsed(record);
        }
        record.update();
    }

    /**
     * Sort pet codes according to sortedIds.
     * The codes will be sorted according to the order of `sortedIds`. If there are codes of the tenant
     * whose ids are not included in `sortedIds`, they will be sorted to the end. If an id in `sortedIds` does not exist
     * or does not belong to the tenant, it will be ignored.
     * <br>
     * SQL:
     * ```
     * -- for each code:
     * update moe_pet_code set sort = ?, update_time = ? where id = ?;
     * ```
     *
     * @param sortedIds sorted pet code ids
     * @param tenant    tenant
     */
    @Transactional
    public void sortPetCode(Tenant tenant, List<Long> sortedIds) {
        var sortMap = SortUtils.toSortMap(sortedIds);
        var codes = listPetCode(tenant);

        // id in the front of sortedIds has a greater sort value, which means it will be displayed first.
        // If id is not in sortedIds, it will be displayed last.
        var now = Instant.now().getEpochSecond();
        for (var code : codes) {
            var sort = sortMap.getOrDefault(code.getId().longValue(), 0);
            if (!Objects.equals(code.getSort(), sort)) {
                code.setSort(sort).setUpdateTime(now);
            }
        }

        dsl.batchUpdate(codes).execute();
    }

    /**
     * Delete a pet code. Pet code binding will also be deleted.
     * If the pet code is already deleted, won't do update.
     * <br>
     * SQL:
     * ```
     * update moe_pet_code set status = 2, update_time = ? where id = ?;
     * delete from moe_pet_pet_code_binding where pet_code_id = ?;
     * ```
     *
     * @param id     pet code id
     * @param tenant tenant
     */
    public void deletePetCode(Tenant tenant, long id) {
        var code = getPetCode(tenant, id);
        if (StatusUtils.isDeleted(code.getStatus())) {
            log.warn("pet code id {} is already deleted", code.getId());
            return;
        }
        code.setStatus(StatusEnum.DELETED.getValue())
                .setUpdateTime(Instant.now().getEpochSecond())
                .update();

        // binding 表没有 status，直接硬删除
        dsl.deleteFrom(MOE_PET_PET_CODE_BINDING)
                .where(MOE_PET_PET_CODE_BINDING.PET_CODE_ID.eq(code.getId()))
                .execute();
    }

    /**
     * Check if the code abbr (code number) is already used in the tenant.
     * <br>
     * SQL:
     * ```
     * select exists(
     * select 1
     * from moe_pet_code
     * where {tenant condition} and status = 1 and code_number = ? [and id != ?]
     * );
     * ```
     *
     * @param record record of pet code
     */
    private void checkAbbrUsed(MoePetCodeRecord record) {
        var tenant = TenantUtils.of(record.getCompanyId());

        var tenantCondition = tenantCondition(tenant);
        var abbrEq = MOE_PET_CODE.CODE_NUMBER.eq(record.getCodeNumber());
        var idNe = MOE_PET_CODE.ID.ne(record.getId());
        var statusCondition = MOE_PET_CODE.STATUS.eq(StatusEnum.NORMAL.getValue());

        var conditions = record.getId() != null
                ? DSL.and(tenantCondition, statusCondition, abbrEq, idNe)
                : DSL.and(tenantCondition, statusCondition, abbrEq);

        var exist = dsl.fetchExists(MOE_PET_CODE, conditions);
        if (exist) {
            // TODO: add new error code
            throw ExceptionUtil.bizException(CODE_PARAMS_ERROR, "Code abbreviation is already in use");
        }
    }

    /**
     * List pet code bindings by pet id
     *
     * @param petId pet id
     * @return pet code list, or empty list if not found
     */
    public List<MoePetPetCodeBindingRecord> listPetCodeBinding(long petId) {
        return dsl.selectFrom(MOE_PET_PET_CODE_BINDING)
                .where(MOE_PET_PET_CODE_BINDING.PET_ID.eq((int) petId))
                .orderBy(MOE_PET_PET_CODE_BINDING.ID.desc())
                .fetch();
    }

    public List<MoePetPetCodeBindingRecord> batchListPetCodeBinding(List<Long> petIds) {
        return dsl.selectFrom(MOE_PET_PET_CODE_BINDING)
                .where(MOE_PET_PET_CODE_BINDING.PET_ID.in(petIds))
                .orderBy(MOE_PET_PET_CODE_BINDING.ID.asc())
                .fetch();
    }

    /**
     * binding pet and code, insert or updated
     *
     * @param record binding record
     */
    public void bindingPetCode(MoePetPetCodeBindingRecord record) {
        dsl.insertInto(MOE_PET_PET_CODE_BINDING)
                .set(MOE_PET_PET_CODE_BINDING.PET_ID, record.getPetId())
                .set(MOE_PET_PET_CODE_BINDING.PET_CODE_ID, record.getPetCodeId())
                .set(MOE_PET_PET_CODE_BINDING.COMMENT, record.getComment())
                .set(MOE_PET_PET_CODE_BINDING.BINDING_TIME, record.getBindingTime())
                .onDuplicateKeyUpdate()
                .set(MOE_PET_PET_CODE_BINDING.COMMENT, record.getComment())
                .set(MOE_PET_PET_CODE_BINDING.BINDING_TIME, record.getBindingTime())
                .execute();
    }

    /**
     * this table not status, so deleted is hard delete
     * @param petId pet id
     * @param petCodeId pet code
     */
    public void unbindingPetCode(Integer petId, Integer petCodeId) {
        dsl.deleteFrom(MOE_PET_PET_CODE_BINDING)
                .where(MOE_PET_PET_CODE_BINDING.PET_ID.eq(petId))
                .and(MOE_PET_PET_CODE_BINDING.PET_CODE_ID.eq(petCodeId))
                .execute();
    }
}
