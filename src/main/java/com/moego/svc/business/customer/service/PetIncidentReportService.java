package com.moego.svc.business.customer.service;

import com.moego.idl.models.business_customer.v1.BusinessPetIncidentReportModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.util.JsonUtil;
import com.moego.svc.business.customer.converter.PetIncidentReportConverter;
import com.moego.svc.business.customer.dto.PetVaccineSnapshotDTO;
import com.moego.svc.business.customer.repo.PetIncidentReportPetMappingRepo;
import com.moego.svc.business.customer.repo.PetIncidentReportRepo;
import com.moego.svc.business.customer.repository.jooq.tables.records.PetIncidentReportPetMappingRecord;
import com.moego.svc.business.customer.repository.jooq.tables.records.PetIncidentReportRecord;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.JSON;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class PetIncidentReportService {

    private final PetIncidentReportRepo petIncidentReportRepo;

    private final PetIncidentReportPetMappingRepo petIncidentReportPetMappingRepo;

    private final PetIncidentReportConverter petIncidentReportConverter;

    private final PetIncidentTypeService petIncidentTypeService;

    private final PetVaccineRecordService petVaccineRecordService;

    public PetIncidentReportRecord mustGet(Long id) {
        if (id == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Pet incident report id is empty");
        }

        var record = petIncidentReportRepo.getPetIncidentReport(id);
        if (record == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Pet incident report not found");
        }
        return record;
    }

    public BusinessPetIncidentReportModel getPetIncidentReport(Long id, Long companyId) {
        var record = mustGet(id);
        if (companyId != null && !Objects.equals(record.getCompanyId(), companyId)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Pet incident report not found");
        }

        var petIncidentReportPetMappingRecords =
                petIncidentReportPetMappingRepo.getPetIncidentReportPetMappingByIncidentId(id);

        var petIds = petIncidentReportPetMappingRecords.stream()
                .map(PetIncidentReportPetMappingRecord::getPetId)
                .toList();

        return petIncidentReportConverter.toModel(record, petIds);
    }

    @Transactional
    public Long createPetIncidentReport(PetIncidentReportRecord record, List<Long> petIds) {
        if (CollectionUtils.isEmpty(petIds)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Pet ids is empty");
        }

        // todo 验证 pet ( 暂无 company level 的 pet service )
        petIncidentTypeService.getPetIncidentType(
                record.getIncidentTypeId(),
                Tenant.newBuilder().setCompanyId(record.getCompanyId()).build());

        petIncidentReportRepo.createPetIncidentReport(record);

        Map<Integer, List<PetVaccineSnapshotDTO>> petVaccineMap =
                petVaccineRecordService
                        .listPetVaccineSnapshot(
                                petIds, record.getIncidentTime().toLocalDate().toString())
                        .stream()
                        .collect(Collectors.groupingBy(PetVaccineSnapshotDTO::getPetId));

        // 创建 pet incident report pet mapping
        List<PetIncidentReportPetMappingRecord> petIncidentReportPetMappingRecords = new ArrayList<>();
        for (Long petId : petIds) {
            PetIncidentReportPetMappingRecord petIncidentReportPetMappingRecord =
                    new PetIncidentReportPetMappingRecord();
            petIncidentReportPetMappingRecord.setCompanyId(record.getCompanyId());
            petIncidentReportPetMappingRecord.setBusinessId(record.getBusinessId());
            petIncidentReportPetMappingRecord.setIncidentId(record.getId());
            petIncidentReportPetMappingRecord.setPetId(petId);
            petIncidentReportPetMappingRecord.setVaccineStatus(
                    JSON.json(JsonUtil.toJson(petVaccineMap.getOrDefault(petId.intValue(), List.of()))));
            petIncidentReportPetMappingRecords.add(petIncidentReportPetMappingRecord);
        }

        petIncidentReportPetMappingRepo.createPetIncidentReportPetMapping(petIncidentReportPetMappingRecords);

        return record.getId();
    }

    public List<BusinessPetIncidentReportModel> listPetIncidentReport(Long companyId, Long businessId, Long petId) {
        // 获取 pet 有的 incident report ids
        var petIncidentReportPetMappingRecords =
                petIncidentReportPetMappingRepo.getPetIncidentReportPetMappingByPetId(companyId, businessId, petId);
        var incidentIds = petIncidentReportPetMappingRecords.stream()
                .map(PetIncidentReportPetMappingRecord::getIncidentId)
                .toList();

        // 获取 incident report & 对应的 pet ids
        var petIncidentReportRecords =
                petIncidentReportRepo.getPetIncidentReportByIncidentIds(companyId, businessId, incidentIds);

        var petIncidentReportPetMap =
                petIncidentReportPetMappingRepo
                        .batchGetPetIncidentReportPetMapping(companyId, businessId, incidentIds)
                        .stream()
                        .collect(Collectors.groupingBy(
                                PetIncidentReportPetMappingRecord::getIncidentId,
                                Collectors.mapping(PetIncidentReportPetMappingRecord::getPetId, Collectors.toList())));

        return petIncidentReportConverter.toModels(petIncidentReportRecords, petIncidentReportPetMap);
    }

    @Transactional
    public void updatePetIncidentReport(PetIncidentReportRecord record, List<Long> petIds) {
        var petIncidentReportRecord = mustGet(record.getId());
        if (!Objects.equals(petIncidentReportRecord.getCompanyId(), record.getCompanyId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Company id not match");
        }

        // todo 验证 pet ( 暂无 company level 的 pet service )
        petIncidentTypeService.getPetIncidentType(
                record.getIncidentTypeId(),
                Tenant.newBuilder().setCompanyId(record.getCompanyId()).build());

        petIncidentReportRecord.setBusinessId(record.getBusinessId());
        petIncidentReportRecord.setIncidentTime(record.getIncidentTime());
        petIncidentReportRecord.setIncidentTypeId(record.getIncidentTypeId());
        petIncidentReportRecord.setDescription(record.getDescription());
        petIncidentReportRecord.setAttachmentFiles(record.getAttachmentFiles());
        petIncidentReportRecord.setIsPetInjured(record.getIsPetInjured());
        petIncidentReportRecord.setIsStaffInjured(record.getIsStaffInjured());
        petIncidentReportRecord.setIsVetVisited(record.getIsVetVisited());

        petIncidentReportRepo.updatePetIncidentReport(petIncidentReportRecord);
        petIncidentReportPetMappingRepo.deletePetIncidentReportPetMapping(record.getId());
        petIncidentReportPetMappingRepo.createPetIncidentReportPetMapping(buildPetIncidentReportPetMappingRecords(
                record.getCompanyId(), record.getBusinessId(), record.getId(), petIds));
    }

    @Transactional
    public void deletePetIncidentReport(Long companyId, Long incidentId) {
        var petIncidentReportRecord = mustGet(incidentId);
        if (!Objects.equals(petIncidentReportRecord.getCompanyId(), companyId)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Company id not match");
        }

        petIncidentReportRepo.deletePetIncidentReport(companyId, incidentId);
    }

    public List<PetIncidentReportPetMappingRecord> buildPetIncidentReportPetMappingRecords(
            Long companyId, Long businessId, Long incidentId, List<Long> petIds) {
        if (CollectionUtils.isEmpty(petIds)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Pet ids is empty");
        }

        List<PetIncidentReportPetMappingRecord> petIncidentReportPetMappingRecords = new ArrayList<>();
        for (Long petId : petIds) {
            PetIncidentReportPetMappingRecord petIncidentReportPetMappingRecord =
                    new PetIncidentReportPetMappingRecord();
            petIncidentReportPetMappingRecord.setCompanyId(companyId);
            petIncidentReportPetMappingRecord.setBusinessId(businessId);
            petIncidentReportPetMappingRecord.setIncidentId(incidentId);
            petIncidentReportPetMappingRecord.setPetId(petId);
            petIncidentReportPetMappingRecords.add(petIncidentReportPetMappingRecord);
        }
        return petIncidentReportPetMappingRecords;
    }

    public List<BusinessPetIncidentReportModel> batchGetPetIncidentReport(
            Long companyId, Long businessId, List<Long> petIds) {
        var petIncidentReportPetMappingRecords =
                petIncidentReportPetMappingRepo.batchGetPetIncidentReportPetMappingByPetIds(
                        companyId, businessId, petIds);

        var incidentIds = petIncidentReportPetMappingRecords.stream()
                .map(PetIncidentReportPetMappingRecord::getIncidentId)
                .toList();

        var petIncidentReportRecords =
                petIncidentReportRepo.getPetIncidentReportByIncidentIds(companyId, businessId, incidentIds);

        var petIncidentReportPetMap =
                petIncidentReportPetMappingRepo
                        .batchGetPetIncidentReportPetMapping(companyId, businessId, incidentIds)
                        .stream()
                        .collect(Collectors.groupingBy(
                                PetIncidentReportPetMappingRecord::getIncidentId,
                                Collectors.mapping(PetIncidentReportPetMappingRecord::getPetId, Collectors.toList())));

        return petIncidentReportConverter.toModels(petIncidentReportRecords, petIncidentReportPetMap);
    }
}
