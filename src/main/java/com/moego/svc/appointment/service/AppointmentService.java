package com.moego.svc.appointment.service;

import static com.moego.common.utils.CommonUtil.isNormal;
import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static com.moego.svc.appointment.constant.AppointmentStatusSet.ACTIVE_STATUS_VALUE_SET;
import static com.moego.svc.appointment.constant.AppointmentStatusSet.IN_PROGRESS_STATUS_VALUE_SET;
import static com.moego.svc.appointment.mapper.mysql.EvaluationServiceDetailDynamicSqlSupport.evaluationServiceDetail;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.appointmentDate;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.appointmentEndDate;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.appointmentEndTime;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.appointmentStartTime;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.bookOnlineStatus;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.businessId;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.checkInTime;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.checkOutTime;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.customerId;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.id;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.isBlock;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.isDeprecate;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.isPaid;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.isWaitingList;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.moeGroomingAppointment;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.noStartTime;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.serviceTypeInclude;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.status;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.updateTime;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.waitListStatus;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentMapper.selectList;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentMapper.updateSelectiveColumns;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingPetDetailDynamicSqlSupport.groomingId;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingPetDetailDynamicSqlSupport.moeGroomingPetDetail;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingServiceOperationDynamicSqlSupport.moeGroomingServiceOperation;
import static java.lang.Math.toIntExact;
import static org.mybatis.dynamic.sql.SqlBuilder.and;
import static org.mybatis.dynamic.sql.SqlBuilder.case_;
import static org.mybatis.dynamic.sql.SqlBuilder.count;
import static org.mybatis.dynamic.sql.SqlBuilder.countDistinct;
import static org.mybatis.dynamic.sql.SqlBuilder.equalTo;
import static org.mybatis.dynamic.sql.SqlBuilder.group;
import static org.mybatis.dynamic.sql.SqlBuilder.isBetween;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualToWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isFalse;
import static org.mybatis.dynamic.sql.SqlBuilder.isGreaterThan;
import static org.mybatis.dynamic.sql.SqlBuilder.isGreaterThanOrEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.isInWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isLessThan;
import static org.mybatis.dynamic.sql.SqlBuilder.isLessThanOrEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isNotEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isTrue;
import static org.mybatis.dynamic.sql.SqlBuilder.on;
import static org.mybatis.dynamic.sql.SqlBuilder.or;
import static org.mybatis.dynamic.sql.SqlBuilder.select;
import static org.mybatis.dynamic.sql.SqlBuilder.selectDistinct;
import static org.mybatis.dynamic.sql.SqlBuilder.sortColumn;

import com.moego.common.constant.CommonConstant;
import com.moego.common.dto.BusinessDateTimeDTO;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.AppointmentNoteType;
import com.moego.idl.models.appointment.v1.AppointmentPaymentStatus;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.appointment.v1.AppointmentUpdatedBy;
import com.moego.idl.models.appointment.v1.BlockTimeModel;
import com.moego.idl.models.appointment.v1.PetDetailDef;
import com.moego.idl.models.appointment.v1.PetDetailStatus;
import com.moego.idl.models.appointment.v1.WaitListStatus;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.service.appointment.v1.AppointmentDateType;
import com.moego.idl.service.appointment.v1.GetCustomerLastAppointmentRequest;
import com.moego.idl.service.appointment.v1.GetTimeOverlapAppointmentListRequest;
import com.moego.idl.service.appointment.v1.ListAppointmentForPetsRequest;
import com.moego.idl.service.appointment.v1.ListAppointmentsForCustomersRequest;
import com.moego.idl.service.appointment.v1.ListAppointmentsRequest;
import com.moego.idl.service.appointment.v1.ListBlockTimesRequest;
import com.moego.idl.service.organization.v1.CompanyServiceGrpc;
import com.moego.idl.service.organization.v1.GetCompanyPreferenceSettingRequest;
import com.moego.idl.utils.v2.OrderBy;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.util.Tx;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.grooming.dto.appointment.history.ChangeTimeLogDTO;
import com.moego.server.grooming.enums.AppointmentAction;
import com.moego.svc.activitylog.event.enums.Action;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLogRecorder;
import com.moego.svc.appointment.converter.AppointmentConverter;
import com.moego.svc.appointment.converter.DateConverter;
import com.moego.svc.appointment.domain.EvaluationServiceDetail;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import com.moego.svc.appointment.domain.MoeGroomingNote;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.dto.PetDetailDTO;
import com.moego.svc.appointment.listener.event.AppointmentEvent;
import com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport;
import com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentMapper;
import com.moego.svc.appointment.mapper.mysql.MoeGroomingPetDetailDynamicSqlSupport;
import com.moego.svc.appointment.mapper.mysql.MoeGroomingPetDetailMapper;
import com.moego.svc.appointment.service.remote.CompanyRemoteService;
import com.moego.svc.appointment.utils.CriteriaUtils;
import com.moego.svc.appointment.utils.PageInfo;
import com.moego.svc.appointment.utils.Pair;
import com.moego.svc.appointment.utils.PetDetailUtil;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.mybatis.dynamic.sql.AndOrCriteriaGroup;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.DerivedColumn;
import org.mybatis.dynamic.sql.SortSpecification;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.QueryExpressionDSL;
import org.mybatis.dynamic.sql.select.SelectModel;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @since 2024/1/25
 */
@Service
@RequiredArgsConstructor
public class AppointmentService {

    private final MoeGroomingAppointmentMapper appointmentMapper;
    private final PetDetailServiceProxy petDetailService;
    private final MoeGroomingPetDetailMapper petDetailMapper;
    private final EvaluationServiceDetailService petEvaluationService;
    private final NoteService noteService;
    private final CompanyRemoteService companyRemoteService;
    private final IBusinessBusinessClient businessClient;
    private final ApplicationEventPublisher publisher;
    private final PricingRuleRecordApplyService pricingRuleApplyService;
    private final CompanyServiceGrpc.CompanyServiceBlockingStub companyService;

    private static final Map<String, SqlColumn<?>> SORT_COLUMNS = Map.of(
            "appointmentDate", moeGroomingAppointment.appointmentDate,
            "appointmentStartTime", moeGroomingAppointment.appointmentStartTime);

    public List<Byte> getActiveStatusSet() {
        return ACTIVE_STATUS_VALUE_SET;
    }

    public List<Byte> getInProgressStatusSet() {
        return IN_PROGRESS_STATUS_VALUE_SET;
    }

    /**
     * Get appointment by id, not include deprecated appointment.
     *
     * @param id appointment id
     * @return appointment or null if not exist
     */
    @Nullable
    public MoeGroomingAppointment get(long id) {
        return appointmentMapper
                .selectOne(c -> c.where(moeGroomingAppointment.id, isEqualTo((int) id))
                        .and(moeGroomingAppointment.isDeprecate, isFalse()))
                .orElse(null);
    }

    /**
     * Get appointment by id, not include deprecated appointment.
     *
     * <p> Throw exception if not exist.
     *
     * @param id appointment id
     * @return appointment
     */
    @Nonnull
    public MoeGroomingAppointment mustGet(long id) {
        return Optional.ofNullable(get(id))
                .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "appointment not found: " + id));
    }

    public long insertSelective(MoeGroomingAppointment appointment) {
        appointmentMapper.insertSelective(appointment);

        var createdEntity = mustGet(appointment.getId());
        Tx.doAfterCommit(() -> publisher.publishEvent(new AppointmentEvent.Created(createdEntity)));

        return appointment.getId();
    }

    public int updateByAppointmentId(MoeGroomingAppointment appointment) {
        return appointmentMapper.update(completer -> updateSelectiveColumns(appointment, completer)
                .where(moeGroomingAppointment.companyId, isEqualTo(appointment.getCompanyId()))
                .and(moeGroomingAppointment.businessId, isEqualTo(appointment.getBusinessId()))
                .and(moeGroomingAppointment.id, isEqualTo(appointment.getId())));
    }

    /**
     * Select last appointment
     *
     * @param businessId business id
     * @param customerId customer id
     * @return last appointment
     */
    public MoeGroomingAppointment selectLastAppointment(Integer businessId, Integer customerId) {
        return appointmentMapper
                .selectOne(completer -> completer
                        .where(moeGroomingAppointment.businessId, isEqualTo(businessId))
                        .and(moeGroomingAppointment.customerId, isEqualTo(customerId))
                        .and(moeGroomingAppointment.isWaitingList, isEqualTo(CommonConstant.DISABLE))
                        .and(moeGroomingAppointment.isDeprecate, isFalse())
                        .and(moeGroomingAppointment.isBlock, isEqualTo(CommonConstant.DELETED))
                        .and(moeGroomingAppointment.status, isIn(ACTIVE_STATUS_VALUE_SET))
                        .orderBy(
                                moeGroomingAppointment.appointmentDate.descending(),
                                moeGroomingAppointment.appointmentEndTime.descending())
                        .limit(1))
                .orElse(null);
    }

    public MoeGroomingAppointment selectInProgressAppointment(
            Long companyId, Integer businessId, Integer customerId, Integer petId, ServiceItemType serviceItemType) {
        if (Objects.requireNonNull(serviceItemType) == ServiceItemType.EVALUATION) {
            QueryExpressionDSL<SelectModel>.QueryExpressionWhereBuilder builder = selectDistinct(
                            moeGroomingAppointment.id)
                    .from(moeGroomingAppointment, "appointment")
                    .join(
                            evaluationServiceDetail,
                            "evaluation_service_detail",
                            on(
                                    moeGroomingAppointment.id,
                                    equalTo(evaluationServiceDetail.appointmentId.withJavaType(Integer.class))))
                    .where(moeGroomingAppointment.companyId, isEqualTo(companyId))
                    .and(moeGroomingAppointment.businessId, isEqualTo(businessId))
                    .and(moeGroomingAppointment.customerId, isEqualTo(customerId))
                    .and(moeGroomingAppointment.status, isIn(getInProgressStatusSet()))
                    .and(moeGroomingAppointment.isBlock, isEqualTo(CommonConstant.DELETED))
                    .and(moeGroomingAppointment.isDeprecate, isFalse())
                    .and(moeGroomingAppointment.isWaitingList, isEqualTo(CommonConstant.DISABLE));
            if (petId != null) {
                builder.and(evaluationServiceDetail.petId, isEqualTo(petId.longValue()));
            }
            List<Integer> bitValueList = ServiceItemEnum.getBitValueListByServiceItem(ServiceItemEnum.EVALUATION);
            builder.and(moeGroomingAppointment.serviceTypeInclude, isIn(bitValueList));
            builder.limit(1);
            return appointmentMapper
                    .selectOne(builder.build().render(RenderingStrategies.MYBATIS3))
                    .orElse(null);
        } else {
            return null;
        }
    }

    @Nonnull
    public MoeGroomingAppointment getAppointment(@Nullable Long companyId, Long appointmentId) {
        return appointmentMapper
                .selectOne(c -> c.where(moeGroomingAppointment.companyId, isEqualToWhenPresent(companyId))
                        .and(moeGroomingAppointment.id, isEqualTo(appointmentId.intValue())))
                .orElseThrow(() -> bizException(Code.CODE_APPOINTMENT_NOT_FOUND));
    }

    public List<MoeGroomingAppointment> getAppointments(@Nullable Long companyId, List<Long> appointmentIds) {
        return appointmentMapper.select(completer -> completer
                .where(MoeGroomingAppointmentDynamicSqlSupport.companyId, isEqualToWhenPresent(companyId))
                .and(id, isIn(appointmentIds.stream().map(Long::intValue).toList())));
    }

    /**
     * Selective update appointment by id.
     *
     * @param appointment appointment
     * @return affected rows
     */
    public int update(MoeGroomingAppointment appointment) {
        if (!isNormal(appointment.getId())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "appointment id is required");
        }

        var before = get(appointment.getId());
        if (before == null) {
            return 0;
        }

        if (appointment.getUpdateTime() == null) {
            appointment.setUpdateTime(Instant.now().getEpochSecond());
        }

        int affectedRows = appointmentMapper.update(c -> updateSelectiveColumns(appointment, c)
                .where(moeGroomingAppointment.id, isEqualTo(appointment.getId()))
                .and(moeGroomingAppointment.updateTime, isEqualTo(before.getUpdateTime()))
                .and(moeGroomingAppointment.isDeprecate, isFalse()));

        if (affectedRows > 0) {
            ActivityLogRecorder.record(Action.UPDATE, ResourceType.APPOINTMENT, appointment.getId(), appointment);
            var after = get(appointment.getId());
            Tx.doAfterCommit(() -> publisher.publishEvent(new AppointmentEvent.Updated(before, after)));
        }

        return affectedRows;
    }

    public int update(List<AndOrCriteriaGroup> cond, UpdateDSL<UpdateModel> dsl, Long appointmentId) {
        if (!isNormal(appointmentId)) {
            throw bizException(Code.CODE_PARAMS_ERROR, "appointment id is required");
        }

        var before = get(appointmentId);
        if (before == null) {
            return 0;
        }

        int affectedRows = appointmentMapper.update(c -> dsl.where(cond).and(id, isEqualTo(appointmentId.intValue())));

        if (affectedRows > 0) {
            var after = get(appointmentId);
            Tx.doAfterCommit(() -> publisher.publishEvent(new AppointmentEvent.Updated(before, after)));
        }

        return affectedRows;
    }

    // TODO: 迁移完整的状态机逻辑
    public void batchCheckIn(Set<Long> appointmentIds) {
        if (CollectionUtils.isEmpty(appointmentIds)) {
            return;
        }

        // list all appointments
        var appointments = appointmentMapper.select(c -> c.where(
                        moeGroomingAppointment.id,
                        isIn(appointmentIds.stream().map(Long::intValue).toList()))
                .and(moeGroomingAppointment.status, isIn((byte) AppointmentStatus.CONFIRMED_VALUE, (byte)
                        AppointmentStatus.UNCONFIRMED_VALUE)));

        for (var appointment : appointments) {
            var updateBean = new MoeGroomingAppointment();
            updateBean.setId(appointment.getId());
            updateBean.setStatus((byte) AppointmentStatus.CHECKED_IN_VALUE);
            updateBean.setCheckInTime(Instant.now().getEpochSecond());
            updateBean.setStatusBeforeCheckin(appointment.getStatus());
            update(updateBean);
        }
    }

    public Map<Long, MoeGroomingAppointment> getCustomerLastAppointment(
            Long companyId,
            List<Long> customerIdList,
            AppointmentStatus appointmentStatus,
            GetCustomerLastAppointmentRequest.Filter filter) {
        if (CollectionUtils.isEmpty(customerIdList)) {
            return Map.of();
        }

        List<AndOrCriteriaGroup> andOrCriteriaGroups = buildFilterClause(companyId, filter);

        // SelectStatementProvider selectStatement = select(MoeGroomingAppointmentMapper.selectList)
        //         .from(
        //                 select(MoeGroomingAppointmentMapper.selectList)
        //                         .from(moeGroomingAppointment)
        //                         .where(MoeGroomingAppointmentDynamicSqlSupport.companyId, isEqualTo(companyId))
        //                         .and(isWaitingList, isEqualTo(CommonConstant.DISABLE))
        //                         .and(isDeprecate, isFalse())
        //                         .and(isBlock, isEqualTo(CommonConstant.DELETED))
        //                         .and(
        //                                 status,
        //                                 Objects.equals(
        //                                                 AppointmentStatus.APPOINTMENT_STATUS_UNSPECIFIED,
        //                                                 appointmentStatus)
        //                                         ? isNotEqualTo((byte) AppointmentStatus.CANCELED_VALUE)
        //                                         : isEqualTo((byte) appointmentStatus.getNumber()))
        //                         .and(
        //                                 customerId,
        //                                 isIn(customerIdList.stream()
        //                                         .map(Long::intValue)
        //                                         .toList()))
        //                         .and(
        //                                 appointmentEndDate,
        //                                 isLessThan(date),
        //                                 or(
        //                                         appointmentEndDate,
        //                                         isEqualTo(date),
        //                                         and(appointmentEndTime, isLessThanOrEqualTo(nowMinutes))))
        //                         .orderBy(appointmentEndDate.descending(), appointmentEndTime.descending())
        //                         .limit(999),
        //                 "temp")
        //         .groupBy(customerId)
        //         .build()
        //         .render(RenderingStrategies.MYBATIS3);

        // select *
        //     from (select *,
        //         row_number() over (partition by customer_id order by appointment_end_date DESC, appointment_end_time
        // DESC) as rn
        //         from moe_grooming_appointment
        //         where company_id = ...
        // and is_waiting_list = 0
        // and is_deprecate = false
        // and is_block = 2
        // and status != 4
        // and customer_id in (...)
        // and (appointment_end_date < '...' or
        //     (appointment_end_date = '...' and appointment_end_time <= ...))) temp
        // where temp.rn = 1;

        DerivedColumn<Integer> rowNum = DerivedColumn.of(
                "row_number() over (partition by customer_id order by appointment_end_date DESC, appointment_end_time DESC)");
        BasicColumn[] selectList = Stream.concat(
                        Stream.of(rowNum.as("rn")), Stream.of(MoeGroomingAppointmentMapper.selectList))
                .toArray(BasicColumn[]::new);

        SelectStatementProvider selectStatement = select(MoeGroomingAppointmentMapper.selectList)
                .from(
                        select(selectList)
                                .from(moeGroomingAppointment)
                                .where(MoeGroomingAppointmentDynamicSqlSupport.companyId, isEqualTo(companyId))
                                .and(isWaitingList, isEqualTo(CommonConstant.DISABLE))
                                .and(isDeprecate, isFalse())
                                .and(isBlock, isEqualTo(CommonConstant.DELETED))
                                .and(
                                        status,
                                        Objects.equals(
                                                        AppointmentStatus.APPOINTMENT_STATUS_UNSPECIFIED,
                                                        appointmentStatus)
                                                ? isNotEqualTo((byte) AppointmentStatus.CANCELED_VALUE)
                                                : isEqualTo((byte) appointmentStatus.getNumber()))
                                .and(
                                        customerId,
                                        isIn(customerIdList.stream()
                                                .map(Long::intValue)
                                                .toList()))
                                .and(andOrCriteriaGroups),
                        "temp")
                .where(DerivedColumn.of("rn"), isEqualTo(1))
                .build()
                .render(RenderingStrategies.MYBATIS3);

        return appointmentMapper.selectMany(selectStatement).stream()
                .collect(Collectors.toMap(appt -> Long.valueOf(appt.getCustomerId()), Function.identity()));
    }

    private List<AndOrCriteriaGroup> buildFilterClause(
            Long companyId, GetCustomerLastAppointmentRequest.Filter filter) {
        String timezoneName = companyRemoteService.getTimezoneName(companyId);

        String date = DateUtil.convertLocalDateToDateString(LocalDateTime.now(), timezoneName, "yyyy-MM-dd");
        Integer nowMinutes = DateUtil.getNowMinutes(timezoneName);

        List<AndOrCriteriaGroup> filters = new ArrayList<>();
        if (filter.hasStartTimeRange() || filter.hasEndTimeRange()) {
            var startTimeFilter = CriteriaUtils.buildDateTimeFilter(
                    appointmentDate, appointmentStartTime, timezoneName, filter.getStartTimeRange());
            filters.addAll(startTimeFilter);
            var endTimeFilter = CriteriaUtils.buildDateTimeFilter(
                    appointmentEndDate, appointmentEndTime, timezoneName, filter.getEndTimeRange());
            filters.addAll(endTimeFilter);
        } else {
            filters.add(and(
                    appointmentEndDate,
                    isLessThan(date),
                    or(appointmentEndDate, isEqualTo(date), and(appointmentEndTime, isLessThanOrEqualTo(nowMinutes)))));
        }

        if (!CollectionUtils.isEmpty(filter.getServiceItemTypesList())) {
            filters.add(
                    and(serviceTypeInclude, isIn(serviceItemTypesToBitValueList(filter.getServiceItemTypesList()))));
        } else {
            filters.add(and(
                    serviceTypeInclude,
                    isIn(serviceItemTypesToBitValueList(List.of(
                            ServiceItemType.GROOMING,
                            ServiceItemType.BOARDING,
                            ServiceItemType.DAYCARE,
                            ServiceItemType.DOG_WALKING)))));
        }

        if (filter.hasFilterNoStartTime() && filter.getFilterNoStartTime()) {
            filters.add(and(noStartTime, isFalse()));
        }

        if (filter.hasFilterBookingRequest() && filter.getFilterBookingRequest()) {
            filters.add(and(bookOnlineStatus, isEqualTo(CommonConstant.DISABLE)));
        }
        return filters;
    }

    public int refreshAppointmentDateTime(MoeGroomingAppointment appointment) {
        List<MoeGroomingPetDetail> petDetails = petDetailService.getPetDetailList(Long.valueOf(appointment.getId()));
        List<EvaluationServiceDetail> petEvaluations =
                petEvaluationService.getPetEvaluationList(Long.valueOf(appointment.getId()));
        Pair<LocalDateTime, LocalDateTime> period =
                petDetailService.calculatePeriod(appointment.getCompanyId(), petDetails, petEvaluations);
        var startDate = period.first().toLocalDate().toString();
        var endDate = period.second().toLocalDate().toString();
        if (DateUtil.countDaysBetween(startDate, endDate) > 61) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "The maximum number of months that can be queried is 2 months.");
        }
        MoeGroomingAppointment update = new MoeGroomingAppointment();
        update.setId(appointment.getId());
        update.setAppointmentDate(startDate);
        update.setAppointmentEndDate(endDate);
        update.setAppointmentStartTime(period.first().toLocalTime().toSecondOfDay() / 60);
        update.setAppointmentEndTime(period.second().toLocalTime().toSecondOfDay() / 60);
        update.setOldAppointmentDate(appointment.getAppointmentDate());
        update.setOldAppointmentStartTime(appointment.getAppointmentStartTime());
        update.setOldAppointmentEndTime(appointment.getAppointmentEndTime());
        update.setServiceTypeInclude(PetDetailUtil.calculateServiceTypeInclude(petDetails, petEvaluations));
        if (Objects.equals(appointment.getAppointmentDate(), update.getAppointmentDate())
                && Objects.equals(appointment.getAppointmentEndDate(), update.getAppointmentEndDate())
                && Objects.equals(appointment.getAppointmentStartTime(), update.getAppointmentStartTime())
                && Objects.equals(appointment.getAppointmentEndTime(), update.getAppointmentEndTime())
                && Objects.equals(appointment.getServiceTypeInclude(), update.getServiceTypeInclude())) {
            return 0;
        }

        // Publish reschedule event
        BusinessDateTimeDTO dateTime = businessClient.getBusinessDateTime(appointment.getBusinessId());
        ActivityLogRecorder.record(
                AppointmentAction.RESCHEDULE,
                ResourceType.APPOINTMENT,
                appointment.getId(),
                new ChangeTimeLogDTO(
                        PetDetailUtil.buildDateTime(
                                                appointment.getAppointmentDate(), appointment.getAppointmentStartTime())
                                        .atZone(ZoneId.of(dateTime.getTimezoneName()))
                                        .toInstant()
                                        .toEpochMilli()
                                / 1000,
                        PetDetailUtil.buildDateTime(update.getAppointmentDate(), update.getAppointmentStartTime())
                                        .atZone(ZoneId.of(dateTime.getTimezoneName()))
                                        .toInstant()
                                        .toEpochMilli()
                                / 1000,
                        AppointmentUpdatedBy.BY_BUSINESS));

        // update service type include
        update.setServiceTypeInclude(PetDetailUtil.calculateServiceTypeInclude(petDetails, petEvaluations));
        return update(update);
    }

    public List<MoeGroomingAppointment> listRepeatAppointment(Integer businessId, Integer repeatId) {
        return listAfterRepeatAppointment(businessId, repeatId, null);
    }

    public List<MoeGroomingAppointment> listAfterRepeatAppointment(Integer businessId, Integer repeatId, String date) {
        QueryExpressionDSL<SelectModel>.QueryExpressionWhereBuilder builder = select(
                        moeGroomingAppointment.allColumns())
                .from(moeGroomingAppointment)
                .where(MoeGroomingAppointmentDynamicSqlSupport.businessId, isEqualTo(businessId))
                .and(MoeGroomingAppointmentDynamicSqlSupport.repeatId, isEqualTo(repeatId))
                .and(isBlock, isEqualTo(CommonConstant.DELETED))
                .and(isWaitingList, isEqualTo(CommonConstant.DISABLE))
                .and(status, isIn(IN_PROGRESS_STATUS_VALUE_SET));
        if (date != null) {
            builder.and(appointmentDate, isGreaterThan(date));
        }
        SelectStatementProvider selectStatement = builder.build().render(RenderingStrategies.MYBATIS3);
        return appointmentMapper.selectMany(selectStatement);
    }

    public List<Integer> listUpcomingAppointment(
            Long companyId, @Nullable Integer businessId, String startDate, Integer startMinutes) {
        return appointmentMapper
                .selectMany(selectDistinct(moeGroomingAppointment.id)
                        .from(moeGroomingAppointment)
                        .where(moeGroomingAppointment.companyId, isEqualTo(companyId))
                        .and(moeGroomingAppointment.businessId, isEqualToWhenPresent(businessId))
                        .and(moeGroomingAppointment.status, isIn(getInProgressStatusSet()))
                        .and(moeGroomingAppointment.isBlock, isEqualTo(CommonConstant.DELETED))
                        .and(moeGroomingAppointment.isDeprecate, isFalse())
                        .and(waitListStatus, isNotEqualTo((byte) WaitListStatus.WAITLISTONLY.getNumber()))
                        .and(
                                moeGroomingAppointment.appointmentDate,
                                isGreaterThan(startDate),
                                or(
                                        moeGroomingAppointment.appointmentDate,
                                        isEqualTo(startDate),
                                        and(
                                                moeGroomingAppointment.appointmentEndTime,
                                                isGreaterThanOrEqualTo(startMinutes))))
                        .build()
                        .render(RenderingStrategies.MYBATIS3))
                .stream()
                .map(MoeGroomingAppointment::getId)
                .toList();
    }

    public List<Integer> listNotStartedAppointmentWithPetService(
            Long companyId, Integer businessId, List<Integer> serviceIds, Integer petId, String startDate) {
        QueryExpressionDSL<SelectModel>.QueryExpressionWhereBuilder builder = selectDistinct(moeGroomingAppointment.id)
                .from(moeGroomingAppointment, "appointment")
                .join(
                        moeGroomingPetDetail,
                        "pet_detail",
                        on(moeGroomingAppointment.id, equalTo(moeGroomingPetDetail.groomingId)))
                .where(moeGroomingAppointment.companyId, isEqualTo(companyId))
                .and(moeGroomingAppointment.businessId, isEqualToWhenPresent(businessId))
                .and(moeGroomingAppointment.status, isEqualTo((byte) AppointmentStatus.UNCONFIRMED_VALUE))
                .and(moeGroomingAppointment.isBlock, isEqualTo(CommonConstant.DELETED))
                .and(moeGroomingAppointment.isDeprecate, isFalse())
                .and(moeGroomingAppointment.isWaitingList, isEqualTo(CommonConstant.DISABLE))
                .and(moeGroomingAppointment.appointmentDate, isGreaterThan(startDate))
                .and(moeGroomingPetDetail.serviceId, isIn(serviceIds))
                .and(moeGroomingPetDetail.status, isEqualTo((byte) PetDetailStatus.NORMAL_VALUE));
        if (petId != null) {
            builder.and(moeGroomingPetDetail.petId, isEqualTo(petId));
        }
        return appointmentMapper.selectMany(builder.build().render(RenderingStrategies.MYBATIS3)).stream()
                .map(MoeGroomingAppointment::getId)
                .toList();
    }

    public List<Integer> listNotEndedAppointmentWithPetService(
            Long companyId, Integer businessId, Integer serviceId, Integer petId, String date, Integer minutes) {
        QueryExpressionDSL<SelectModel>.QueryExpressionWhereBuilder builder = selectDistinct(moeGroomingAppointment.id)
                .from(moeGroomingAppointment, "appointment")
                .join(
                        moeGroomingPetDetail,
                        "pet_detail",
                        on(moeGroomingAppointment.id, equalTo(moeGroomingPetDetail.groomingId)))
                .where(moeGroomingAppointment.companyId, isEqualTo(companyId))
                .and(moeGroomingAppointment.businessId, isEqualToWhenPresent(businessId))
                .and(moeGroomingAppointment.status, isEqualTo((byte) AppointmentStatus.UNCONFIRMED_VALUE))
                .and(moeGroomingAppointment.isBlock, isEqualTo(CommonConstant.DELETED))
                .and(moeGroomingAppointment.isDeprecate, isFalse())
                .and(moeGroomingAppointment.isWaitingList, isEqualTo(CommonConstant.DISABLE))
                .and(
                        moeGroomingAppointment.appointmentDate,
                        isGreaterThan(LocalDate.now().minusDays(60).toString()))
                .and(
                        moeGroomingAppointment.appointmentEndDate,
                        isGreaterThan(date),
                        or(
                                moeGroomingAppointment.appointmentEndDate,
                                isEqualTo(date),
                                and(moeGroomingAppointment.appointmentEndTime, isGreaterThanOrEqualTo(minutes))))
                .and(moeGroomingPetDetail.serviceId, isEqualTo(serviceId))
                .and(moeGroomingPetDetail.status, isEqualTo((byte) PetDetailStatus.NORMAL_VALUE));
        if (petId != null) {
            builder.and(moeGroomingPetDetail.petId, isEqualTo(petId));
        }
        return appointmentMapper.selectMany(builder.build().render(RenderingStrategies.MYBATIS3)).stream()
                .map(MoeGroomingAppointment::getId)
                .toList();
    }

    /**
     * FIXME: 60 天前的 appointment 不做统计。此方法仅用于 service 是否允许修改 require staff 配置场景。其他场景不可使用！
     */
    public long getAppointmentCountByService(Long companyId, Long serviceId, Long businessId) {
        SelectStatementProvider selectStatement = select(count(id))
                .from(moeGroomingAppointment, "ga")
                .join(moeGroomingPetDetail, "gpd")
                .on(id, equalTo(groomingId))
                .where(MoeGroomingAppointmentDynamicSqlSupport.companyId, isEqualTo(companyId))
                .and(isDeprecate, isFalse())
                .and(
                        MoeGroomingAppointmentDynamicSqlSupport.businessId,
                        isEqualToWhenPresent(Optional.ofNullable(businessId)
                                .map(Long::intValue)
                                .orElse(null)))
                .and(status, isNotEqualTo((byte) AppointmentStatus.CANCELED_VALUE))
                .and(isBlock, isEqualTo(CommonConstant.DELETED))
                .and(
                        appointmentDate,
                        isGreaterThan(LocalDate.now().minusDays(60).toString()))
                .and(MoeGroomingPetDetailDynamicSqlSupport.serviceId, isEqualTo(serviceId.intValue()))
                .and(MoeGroomingPetDetailDynamicSqlSupport.status, isEqualTo((byte) PetDetailStatus.NORMAL_VALUE))
                .build()
                .render(RenderingStrategies.MYBATIS3);

        return appointmentMapper.count(selectStatement);
    }

    public BigDecimal calculateServicePrice(
            long companyId,
            long businessId,
            List<PetDetailDef> petDetailDefs,
            Map<Long, Map<Long, CustomizedServiceView>> petServiceMap) {
        var petDetailDTOS = petDetailService.buildAllInOnePetDetailList(
                companyId, businessId, petDetailDefs, petServiceMap, Map.of());

        List<MoeGroomingPetDetail> petDetails =
                petDetailDTOS.stream().map(PetDetailDTO::getPetDetail).toList();
        var splitLodgings = petDetailDTOS.stream()
                .map(PetDetailDTO::getSplitLodgings)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .toList();

        Optional<BigDecimal> totalAmountUsingPricingRule = pricingRuleApplyService.getTotalAmountUsingPricingRule(
                companyId, businessId, petDetails, splitLodgings);
        BigDecimal petDetailsAmount =
                totalAmountUsingPricingRule.orElseGet(() -> PetDetailUtil.calculateAmount(petDetails));

        BigDecimal petEvaluationAmount = getEvaluationAmount(petDetailDefs);
        return petDetailsAmount.add(petEvaluationAmount);
    }

    private static BigDecimal getEvaluationAmount(List<PetDetailDef> petDetailDefs) {
        return petDetailDefs.stream()
                .map(PetDetailDef::getEvaluationsList)
                .flatMap(List::stream)
                .map(k -> BigDecimal.valueOf(k.getServicePrice()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public List<Integer> getAppointmentIdsByStartDateRange(
            Long companyId, Integer businessId, String startDateGte, String endDateLt, List<ServiceItemType> types) {
        QueryExpressionDSL<SelectModel>.QueryExpressionWhereBuilder builder = select(moeGroomingAppointment.id)
                .from(moeGroomingAppointment)
                .where(MoeGroomingAppointmentDynamicSqlSupport.companyId, isEqualTo(companyId))
                .and(MoeGroomingAppointmentDynamicSqlSupport.businessId, isEqualTo(businessId))
                .and(isBlock, isEqualTo(CommonConstant.DELETED))
                .and(isWaitingList, isEqualTo(CommonConstant.DISABLE))
                .and(status, isIn(ACTIVE_STATUS_VALUE_SET))
                .and(appointmentDate, isGreaterThanOrEqualTo(startDateGte))
                .and(appointmentDate, isLessThan(endDateLt));
        if (!CollectionUtils.isEmpty(types)) {
            List<Integer> bitValueList = types.stream()
                    .map(k -> ServiceItemEnum.fromServiceItem(k.getNumber()))
                    .filter(Objects::nonNull)
                    .map(ServiceItemEnum::getBitValueListByServiceItem)
                    .flatMap(List::stream)
                    .toList();
            builder.and(serviceTypeInclude, isIn(bitValueList));
        }
        SelectStatementProvider selectStatement = builder.build().render(RenderingStrategies.MYBATIS3);
        return appointmentMapper.selectMany(selectStatement).stream()
                .map(MoeGroomingAppointment::getId)
                .toList();
    }

    public List<MoeGroomingAppointment> getAppointmentsByDateRange(
            Long companyId,
            Integer businessId,
            LocalDate startDateGte,
            LocalDate endDateLte,
            List<ServiceItemType> types) {
        List<Integer> bitValueList = null;
        if (!CollectionUtils.isEmpty(types)) {
            bitValueList = types.stream()
                    .map(k -> ServiceItemEnum.fromServiceItem(k.getNumber()))
                    .filter(Objects::nonNull)
                    .map(ServiceItemEnum::getBitValueListByServiceItem)
                    .flatMap(List::stream)
                    .toList();
        }

        return appointmentMapper.selectMany(select(moeGroomingAppointment.allColumns())
                .from(moeGroomingAppointment)
                .where(moeGroomingAppointment.companyId, isEqualToWhenPresent(companyId))
                .and(
                        moeGroomingAppointment.businessId,
                        isEqualToWhenPresent(businessId == null ? null : toIntExact(businessId)))
                .and(serviceTypeInclude, isInWhenPresent(bitValueList))
                .and(
                        appointmentDate,
                        isBetween(startDateGte.minusDays(60).toString()).and(endDateLte.toString()))
                .and(
                        appointmentEndDate,
                        isBetween(startDateGte.toString())
                                .and(endDateLte.plusDays(60).toString()))
                .and(isDeprecate, isFalse())
                .and(status, isIn(getActiveStatusSet()))
                .and(waitListStatus, isNotEqualTo((byte) WaitListStatus.WAITLISTONLY.getNumber()))
                .build()
                .render(RenderingStrategies.MYBATIS3));
    }

    public List<Integer> serviceItemTypesToBitValueList(List<ServiceItemType> serviceItemTypesList) {
        List<Integer> serviceItemList =
                serviceItemTypesList.stream().map(ServiceItemType::getNumber).toList();
        return ServiceItemEnum.convertServiceItemListToBitValueList(serviceItemList);
    }

    public Pair<List<MoeGroomingAppointment>, PageInfo> listAppointments(
            Long companyId,
            List<Long> businessIds,
            ListAppointmentsRequest.Filter filter,
            List<OrderBy> orderBys,
            PageInfo pageInfo,
            ListAppointmentsRequest.PriorityOrderType priorityOrderType) {
        if (ObjectUtils.isEmpty(companyId) && ObjectUtils.isEmpty(businessIds)) {
            return Pair.of(List.of(), new PageInfo(pageInfo.pageNum(), pageInfo.pageSize(), 0));
        }
        if (filter.getCustomerIdsCount() != 1) {
            priorityOrderType = ListAppointmentsRequest.PriorityOrderType.UNSPECIFIED;
        }
        var clause = buildFilterClause(companyId, filter);

        var selectTableStatement = buildListAppointmentsSelectDSL(companyId, priorityOrderType);
        var countTableStatement = select(count(id)).from(moeGroomingAppointment, "appointment");

        boolean needJoinPetDetail = !filter.getStaffIdsList().isEmpty()
                || !filter.getPetIdsList().isEmpty()
                || !filter.getServiceIdsList().isEmpty();
        if (needJoinPetDetail) {
            selectTableStatement = selectTableStatement.join(
                    moeGroomingPetDetail,
                    "pet_detail",
                    on(moeGroomingAppointment.id, equalTo(moeGroomingPetDetail.groomingId)),
                    and(moeGroomingPetDetail.status, equalTo((byte) PetDetailStatus.NORMAL_VALUE)));
            countTableStatement = countTableStatement.join(
                    moeGroomingPetDetail,
                    "pet_detail",
                    on(moeGroomingAppointment.id, equalTo(moeGroomingPetDetail.groomingId)),
                    and(moeGroomingPetDetail.status, equalTo((byte) PetDetailStatus.NORMAL_VALUE)));
            if (filter.getIncludeServiceOperation()) {
                selectTableStatement = selectTableStatement.leftJoin(
                        moeGroomingServiceOperation,
                        "service_operation",
                        on(moeGroomingAppointment.id, equalTo(moeGroomingServiceOperation.groomingId)));
                countTableStatement = countTableStatement.leftJoin(
                        moeGroomingServiceOperation,
                        "service_operation",
                        on(moeGroomingAppointment.id, equalTo(moeGroomingServiceOperation.groomingId)));
            }
        }
        SelectStatementProvider selectStatement = selectTableStatement
                .where(
                        businessId,
                        isInWhenPresent(businessIds.stream().map(Long::intValue).toList()))
                .and(moeGroomingAppointment.companyId, isEqualToWhenPresent(companyId))
                .and(clause)
                .and(moeGroomingAppointment.isDeprecate, isFalse())
                .orderBy(buildSorts(
                        orderBys, !priorityOrderType.equals(ListAppointmentsRequest.PriorityOrderType.UNSPECIFIED)))
                .limit(pageInfo.pageSize())
                .offset((long) pageInfo.pageSize() * (pageInfo.pageNum() - 1))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        SelectStatementProvider countStatement = countTableStatement
                .where(
                        businessId,
                        isInWhenPresent(businessIds.stream().map(Long::intValue).toList()))
                .and(moeGroomingAppointment.companyId, isEqualToWhenPresent(companyId))
                .and(clause)
                .and(moeGroomingAppointment.isDeprecate, isFalse())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return new Pair<>(
                appointmentMapper.selectMany(selectStatement),
                new PageInfo(pageInfo.pageNum(), pageInfo.pageSize(), (int) appointmentMapper.count(countStatement)));
    }

    private QueryExpressionDSL<SelectModel> buildListAppointmentsSelectDSL(
            long companyId, ListAppointmentsRequest.PriorityOrderType priorityOrderType) {
        var l = new ArrayList<>(Arrays.stream(selectList).toList());
        if (priorityOrderType.equals(ListAppointmentsRequest.PriorityOrderType.UNEXPIRED_UNCONFIRMED)) {
            var timezoneName = companyRemoteService.getTimezoneName(companyId);
            var todayDate = DateUtil.convertLocalDateToDateString(
                    Instant.now().atZone(ZoneId.of(timezoneName)).toLocalDateTime(), timezoneName, "yyyy-MM-dd");
            l.add(case_().when(status, isEqualTo((byte) 1))
                    .and(appointmentDate, isGreaterThanOrEqualTo(todayDate))
                    .then(1)
                    .else_(0)
                    .end()
                    .as("priorityRank"));
        }
        return selectDistinct(l.toArray(new BasicColumn[0])).from(moeGroomingAppointment, "appointment");
    }

    private List<AndOrCriteriaGroup> buildFilterClause(Long companyId, ListAppointmentsRequest.Filter filter) {
        var timezoneName = companyRemoteService.getTimezoneName(companyId);
        var startTimeFilter = CriteriaUtils.buildDateTimeFilter(
                appointmentDate, appointmentStartTime, timezoneName, filter.getStartTimeRange());
        var endTimeFilter = CriteriaUtils.buildDateTimeFilter(
                appointmentEndDate, appointmentEndTime, timezoneName, filter.getEndTimeRange());
        List<AndOrCriteriaGroup> lastUpdatedTimeFilter = List.of();
        if (filter.hasLastUpdatedTimeRange()) {
            if (filter.getLastUpdatedTimeRange().hasStartTime()
                    && filter.getLastUpdatedTimeRange().hasEndTime()) {
                lastUpdatedTimeFilter = List.of(
                        and(
                                updateTime,
                                isGreaterThanOrEqualTo(filter.getLastUpdatedTimeRange()
                                        .getStartTime()
                                        .getSeconds())),
                        and(
                                updateTime,
                                isLessThan(filter.getLastUpdatedTimeRange()
                                        .getEndTime()
                                        .getSeconds())));
            } else if (filter.getLastUpdatedTimeRange().hasStartTime()) {
                lastUpdatedTimeFilter = List.of(and(
                        updateTime,
                        isGreaterThanOrEqualTo(
                                filter.getLastUpdatedTimeRange().getStartTime().getSeconds())));
            }
        }
        List<AndOrCriteriaGroup> statusFilter = CollectionUtils.isEmpty(filter.getStatusList())
                ? List.of()
                : List.of(and(
                        status,
                        isIn(filter.getStatusList().stream()
                                .map(AppointmentStatus::getNumber)
                                .map(Integer::byteValue)
                                .toList())));
        List<AndOrCriteriaGroup> filters = new ArrayList<>(
                List.of(and(startTimeFilter), and(endTimeFilter), and(lastUpdatedTimeFilter), and(statusFilter)));
        if (filter.hasFilterNoStartTime() && filter.getFilterNoStartTime()) {
            filters.add(and(noStartTime, isFalse()));
        }
        if (filter.hasFilterBookingRequest() && filter.getFilterBookingRequest()) {
            filters.add(and(bookOnlineStatus, isEqualTo(CommonConstant.DISABLE)));
        }
        filters.add(and(
                waitListStatus,
                isInWhenPresent(filter.getWaitListStatusesList().stream()
                        .map(WaitListStatus::getNumber)
                        .map(Integer::byteValue)
                        .toList())));
        filters.add(and(serviceTypeInclude, isInWhenPresent(filter.getServiceTypeIncludesList())));
        filters.add(and(
                customerId,
                isInWhenPresent(
                        filter.getCustomerIdsList().stream().map(Long::intValue).toList())));
        if (!filter.getStaffIdsList().isEmpty()) {
            if (!filter.getIncludeServiceOperation()) {
                filters.add(and(
                        moeGroomingPetDetail.staffId,
                        isIn(filter.getStaffIdsList().stream()
                                .map(Long::intValue)
                                .toList())));
            } else {
                filters.add(and(
                        moeGroomingPetDetail.staffId,
                        isIn(filter.getStaffIdsList().stream()
                                .map(Long::intValue)
                                .toList()),
                        or(
                                moeGroomingServiceOperation.staffId,
                                isIn(filter.getStaffIdsList().stream()
                                        .map(Long::intValue)
                                        .toList()))));
            }
        }
        if (!filter.getPetIdsList().isEmpty()) {
            filters.add(and(
                    moeGroomingPetDetail.petId,
                    isIn(filter.getPetIdsList().stream().map(Long::intValue).toList())));
        }
        if (!filter.getServiceIdsList().isEmpty()) {
            filters.add(and(
                    moeGroomingPetDetail.serviceId,
                    isIn(filter.getServiceIdsList().stream().map(Long::intValue).toList())));
        }
        if (filter.hasDateType()) {
            filters.add(and(buildDateTimeFilter(filter.getDateType(), timezoneName)));
        }
        if (filter.hasAppointmentDate()) {
            filters.add(and(buildDateTimeFilter(
                    moeGroomingAppointment.appointmentDate,
                    DateConverter.INSTANCE
                            .fromGoogleDate(filter.getAppointmentDate())
                            .toString())));
        }
        if (!filter.hasIncludeBlock() || !filter.getIncludeBlock()) {
            filters.add(and(isBlock, isEqualTo(CommonConstant.DELETED)));
        }
        if (filter.hasIsWaitingList()) {
            filters.add(and(
                    isWaitingList,
                    isEqualTo(filter.getIsWaitingList() ? CommonConstant.NORMAL : CommonConstant.DISABLE)));
        }
        if (filter.hasCheckInTimeRange()) {
            if (filter.getCheckInTimeRange().hasStartTime()) {
                filters.add(and(
                        checkInTime,
                        isGreaterThanOrEqualTo(
                                filter.getCheckInTimeRange().getStartTime().getSeconds())));
            }
            if (filter.getCheckInTimeRange().hasEndTime()) {
                filters.add(and(
                        checkInTime,
                        isLessThan(filter.getCheckInTimeRange().getEndTime().getSeconds())));
            }
        }
        if (filter.hasCheckOutTimeRange()) {
            if (filter.getCheckOutTimeRange().hasStartTime()) {
                filters.add(and(
                        checkOutTime,
                        isGreaterThanOrEqualTo(
                                filter.getCheckOutTimeRange().getStartTime().getSeconds())));
            }
            if (filter.getCheckOutTimeRange().hasEndTime()) {
                filters.add(and(
                        checkOutTime,
                        isLessThan(filter.getCheckOutTimeRange().getEndTime().getSeconds())));
            }
        }
        if (!filter.getPaymentStatusesList().isEmpty()) {
            filters.add(and(
                    isPaid,
                    isIn(filter.getPaymentStatusesList().stream()
                            .map(AppointmentPaymentStatus::getNumber)
                            .map(Integer::byteValue)
                            .toList())));
        }
        return filters;
    }

    private List<AndOrCriteriaGroup> buildDateTimeFilter(AppointmentDateType dateType, String timezoneName) {
        String currentDate = DateUtil.convertLocalDateToDateString(LocalDateTime.now(), timezoneName, "yyyy-MM-dd");
        Integer currentMinutes = DateUtil.getNowMinutes(timezoneName);

        List<AndOrCriteriaGroup> filters = new ArrayList<>();
        List<AndOrCriteriaGroup> subFilters = new ArrayList<>();
        switch (dateType) {
            case LAST:
                // (appointment_end_date = currentDate and appointment_end_time < currentMinute)
                subFilters.add(and(appointmentEndDate, isEqualTo(currentDate)));
                subFilters.add(and(appointmentEndTime, isLessThan(currentMinutes)));
                filters.add(and(subFilters));
                // or appointment_end_date < currentDate
                filters.add(or(appointmentEndDate, isLessThan(currentDate)));
                break;
            case TODAY:
                // appointment_date = {currentDate}
                filters.add(and(appointmentDate, isEqualTo(currentDate)));
                break;
            case NEXT:
                // appointment_date = {currentDate} and appointment_start_time > {currentMinutes}
                subFilters.add(and(appointmentDate, isEqualTo(currentDate)));
                subFilters.add(and(appointmentStartTime, isGreaterThan(currentMinutes)));
                filters.add(and(subFilters));
                // or (appointment_date > {currentDate})
                filters.add(or(appointmentDate, isGreaterThan(currentDate)));
                break;
            case NEXT_NOT_END:
                // appointment_end_date = {currentDate} and appointment_end_time >= {currentMinutes}
                subFilters.add(and(appointmentEndDate, isEqualTo(currentDate)));
                subFilters.add(and(appointmentEndTime, isGreaterThanOrEqualTo(currentMinutes)));
                filters.add(and(subFilters));
                // or (appointment_end_date > {currentDate})
                filters.add(or(appointmentEndDate, isGreaterThan(currentDate)));
                break;
            case NEXT_AFTER_TODAY:
                // appointment_end_date > {currentDate}
                filters.add(and(appointmentEndDate, isGreaterThan(currentDate)));
                break;
            default:
                break;
        }
        return filters;
    }

    private List<AndOrCriteriaGroup> buildDateTimeFilter(SqlColumn<String> dateColumn, String date) {
        List<AndOrCriteriaGroup> filters = new ArrayList<>();
        filters.add(and(dateColumn, isEqualTo(date)));
        return filters;
    }

    private List<SortSpecification> buildSorts(List<OrderBy> orderBys, boolean hasPriority) {
        List<SortSpecification> sorts = orderBys.stream()
                .map(orderBy -> {
                    SqlColumn<?> sortColumn = SORT_COLUMNS.get(orderBy.getFieldName());
                    if (sortColumn == null) {
                        throw bizException(Code.CODE_PARAMS_ERROR, "Invalid sort field: " + orderBy.getFieldName());
                    }
                    if (orderBy.getAsc()) {
                        return sortColumn;
                    }
                    return sortColumn.descending();
                })
                .collect(Collectors.toList());
        sorts.add(moeGroomingAppointment.id);
        List<SortSpecification> l = new ArrayList<>();
        if (hasPriority) {
            l.add(sortColumn("priorityRank").descending());
        }
        l.addAll(sorts);
        return l;
    }

    public List<BlockTimeModel> listBlockTimes(
            Long companyId, List<Long> businessIds, ListBlockTimesRequest.Filter filter) {
        var clause = buildFilterClause(companyId, filter);
        SelectStatementProvider selectStatement = select(MoeGroomingAppointmentMapper.selectList)
                .from(moeGroomingAppointment)
                .where(moeGroomingAppointment.companyId, isEqualTo(companyId))
                .and(
                        moeGroomingAppointment.businessId,
                        isIn(businessIds.stream().map(Long::intValue).toList()))
                .and(moeGroomingAppointment.isBlock, isEqualTo(CommonConstant.NORMAL))
                .and(moeGroomingAppointment.isDeprecate, isFalse())
                .and(moeGroomingAppointment.status, isEqualTo((byte) AppointmentStatus.UNCONFIRMED_VALUE))
                .and(clause)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        List<MoeGroomingAppointment> appointments = appointmentMapper.selectMany(selectStatement);
        if (CollectionUtils.isEmpty(appointments)) {
            return List.of();
        }
        var appointmentIds = appointments.stream()
                .map(MoeGroomingAppointment::getId)
                .map(Integer::longValue)
                .toList();
        // block time 的关联 staff 和 ticket comment
        var petDetailMap = petDetailService.getPetDetailList(appointmentIds).stream()
                .collect(Collectors.toMap(MoeGroomingPetDetail::getGroomingId, Function.identity(), (a, b) -> a));
        var noteMap = noteService.getNoteByAppointmentId(appointmentIds).stream()
                .filter(note -> Objects.equals(note.getType().intValue(), AppointmentNoteType.COMMENT_VALUE))
                .collect(Collectors.toMap(MoeGroomingNote::getGroomingId, Function.identity(), (a, b) -> a));
        return appointments.stream()
                .map(appointment -> {
                    var builder = AppointmentConverter.INSTANCE.toBlockTime(appointment).toBuilder();
                    Optional.ofNullable(petDetailMap.get(appointment.getId()))
                            .ifPresent(petDetail -> builder.setStaffId(petDetail.getStaffId()));
                    Optional.ofNullable(noteMap.get(appointment.getId()))
                            .ifPresent(note -> builder.setDescription(note.getNote()));
                    return builder.build();
                })
                .toList();
    }

    public Map<Long, List<Pair<MoeGroomingAppointment, List<MoeGroomingPetDetail>>>> listAppointmentsForPet(
            Long companyId, Set<Long> petIds, ListAppointmentForPetsRequest.Filter filter) {
        if (petIds.isEmpty()) {
            return new HashMap<>();
        }
        // list pet details
        List<MoeGroomingPetDetail> petDetails = petDetailMapper.selectMany(select(MoeGroomingPetDetailMapper.selectList)
                .from(moeGroomingPetDetail)
                .where(
                        MoeGroomingPetDetailDynamicSqlSupport.petId,
                        isIn(petIds.stream().map(Long::intValue).toList()))
                .and(MoeGroomingPetDetailDynamicSqlSupport.status, isEqualTo((byte) PetDetailStatus.NORMAL_VALUE))
                .and(
                        MoeGroomingPetDetailDynamicSqlSupport.serviceId,
                        isEqualToWhenPresent(filter.getServiceId() != 0 ? toIntExact(filter.getServiceId()) : null))
                .and(
                        MoeGroomingPetDetailDynamicSqlSupport.startDate,
                        isEqualTo((DateConverter.INSTANCE.fromGoogleDate(filter.getDate())).toString()))
                .build()
                .render(RenderingStrategies.MYBATIS3));
        if (petDetails.isEmpty()) {
            return petIds.stream().collect(Collectors.toMap(Function.identity(), k -> List.of()));
        }
        Map<Integer /* petId */, Set<Integer> /* appointment id list */> petIdToAppointmentIds = petDetails.stream()
                .collect(Collectors.groupingBy(
                        MoeGroomingPetDetail::getPetId,
                        Collectors.mapping(MoeGroomingPetDetail::getGroomingId, Collectors.toSet())));

        // list appointments
        List<MoeGroomingAppointment> appointments = appointmentMapper.selectMany(select(
                        MoeGroomingAppointmentMapper.selectList)
                .from(moeGroomingAppointment)
                .where(
                        id,
                        isIn(petDetails.stream()
                                .map(MoeGroomingPetDetail::getGroomingId)
                                .toList()))
                .and(MoeGroomingAppointmentDynamicSqlSupport.companyId, isEqualTo(companyId))
                .and(
                        businessId,
                        isEqualToWhenPresent(filter.hasBusinessId() ? toIntExact(filter.getBusinessId()) : null))
                .and(
                        status,
                        isInWhenPresent(filter.getStatusesList().stream()
                                .map(AppointmentStatus::getNumber)
                                .map(Integer::byteValue)
                                .toList()))
                .and(moeGroomingAppointment.isDeprecate, isFalse())
                .build()
                .render(RenderingStrategies.MYBATIS3));
        var appointmentMapById =
                appointments.stream().collect(Collectors.toMap(MoeGroomingAppointment::getId, Function.identity()));
        var petDetailMap = petDetails.stream().collect(Collectors.groupingBy(MoeGroomingPetDetail::getGroomingId));

        return petIds.stream()
                .collect(Collectors.toMap(
                        Function.identity(),
                        petId -> petIdToAppointmentIds.getOrDefault(petId.intValue(), Collections.emptySet()).stream()
                                .filter(appointmentMapById::containsKey)
                                .map(appointmentMapById::get)
                                .map(appointment ->
                                        Pair.of(appointment, petDetailMap.getOrDefault(appointment.getId(), List.of())))
                                .collect(Collectors.toList())));
    }

    private List<AndOrCriteriaGroup> buildFilterClause(Long companyId, ListBlockTimesRequest.Filter filter) {
        var timezoneName = companyRemoteService.getTimezoneName(companyId);
        var startTimeFilter = CriteriaUtils.buildDateTimeFilter(
                appointmentDate, appointmentStartTime, timezoneName, filter.getStartTimeRange());
        var endTimeFilter = CriteriaUtils.buildDateTimeFilter(
                appointmentEndDate, appointmentEndTime, timezoneName, filter.getEndTimeRange());
        return List.of(and(startTimeFilter), and(endTimeFilter));
    }

    public List<MoeGroomingAppointment> listAppointmentsForCustomer(
            long companyId, List<Long> customerIds, ListAppointmentsForCustomersRequest.Filter filter) {
        if (CollectionUtils.isEmpty(customerIds)) {
            return List.of();
        }

        var selectOneStatement = buildSelectStatement(companyId, customerIds, filter);
        var appointment = appointmentMapper.selectOne(selectOneStatement).orElse(null);
        if (appointment == null) {
            return List.of();
        }

        SelectStatementProvider selectManyStatement = buildQueryBuilder(companyId, customerIds, filter)
                .and(moeGroomingAppointment.appointmentDate, isEqualTo(appointment.getAppointmentDate()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return appointmentMapper.selectMany(selectManyStatement);
    }

    private SelectStatementProvider buildSelectStatement(
            long companyId, List<Long> customerIds, ListAppointmentsForCustomersRequest.Filter filter) {

        var queryBuilder = buildQueryBuilder(companyId, customerIds, filter);

        var timeZoneName = companyRemoteService.getTimezoneName(companyId);
        LocalDateTime now = LocalDateTime.now(ZoneId.of(timeZoneName));
        String currentDate = now.toLocalDate().toString();
        int currentTime = now.toLocalTime().toSecondOfDay() / 60;

        switch (filter.getDateType()) {
            case LAST -> queryBuilder
                    .and(
                            appointmentDate,
                            isLessThan(currentDate),
                            or(
                                    appointmentDate,
                                    isEqualTo(currentDate),
                                    and(appointmentEndTime, isLessThan(currentTime))))
                    .orderBy(appointmentDate.descending(), appointmentEndTime.descending());
            case TODAY -> queryBuilder
                    .and(appointmentDate, isEqualTo(currentDate))
                    .orderBy(appointmentDate, appointmentStartTime);
            case NEXT -> queryBuilder
                    .and(
                            appointmentDate,
                            isGreaterThan(currentDate),
                            or(
                                    appointmentDate,
                                    isEqualTo(currentDate),
                                    and(appointmentStartTime, isGreaterThan(currentTime))))
                    .orderBy(appointmentDate, appointmentStartTime);
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Invalid date type");
        }

        return queryBuilder.limit(1).build().render(RenderingStrategies.MYBATIS3);
    }

    private QueryExpressionDSL<SelectModel>.QueryExpressionWhereBuilder buildQueryBuilder(
            long companyId, List<Long> customerIds, ListAppointmentsForCustomersRequest.Filter filter) {
        return select(MoeGroomingAppointmentMapper.selectList)
                .from(moeGroomingAppointment)
                .where(moeGroomingAppointment.companyId, isEqualTo(companyId))
                .and(customerId, isIn(customerIds.stream().map(Long::intValue).toList()))
                .and(bookOnlineStatus, isEqualTo(CommonConstant.DISABLE))
                .and(isDeprecate, isFalse())
                .and(isBlock, isEqualTo(CommonConstant.DELETED))
                .and(isWaitingList, isEqualTo(CommonConstant.DISABLE))
                .and(
                        moeGroomingAppointment.status,
                        isInWhenPresent(filter.getStatusesList().stream()
                                .map(AppointmentStatus::getNumber)
                                .map(Integer::byteValue)
                                .toList()))
                .and(serviceTypeInclude, isInWhenPresent(filter.getServiceTypeIncludesList()));
    }

    /**
     * Batch get upcoming appointment count for each customer
     *
     * @param companyId
     * @param customerIds
     * @return customer_id -> total count
     */
    public Map<Long, Integer> batchGetTotalAppointmentCount(Long companyId, List<Long> customerIds) {
        if (CollectionUtils.isEmpty(customerIds)) {
            return Map.of();
        }

        // query total appointment count
        return appointmentMapper
                .selectManyMappedRows(select(moeGroomingAppointment.customerId, count().as("count"))
                        .from(moeGroomingAppointment)
                        .where(moeGroomingAppointment.companyId, isEqualTo(companyId))
                        .and(
                                moeGroomingAppointment.customerId,
                                isIn(customerIds.stream().map(Long::intValue).toList()))
                        .and(moeGroomingAppointment.isDeprecate, isFalse())
                        .and(moeGroomingAppointment.isBlock, isEqualTo(CommonConstant.DELETED))
                        .and(moeGroomingAppointment.bookOnlineStatus, isEqualTo(CommonConstant.DISABLE))
                        .groupBy(moeGroomingAppointment.customerId)
                        .build()
                        .render(RenderingStrategies.MYBATIS3))
                .stream()
                .collect(Collectors.toMap(
                        result -> ((Number) result.get("customer_id")).longValue(), // Key: customer_id
                        result -> ((Number) result.get("count")).intValue() // Value: count
                        ));
    }

    /**
     * Batch get upcoming appointment count for each evaluation
     *
     * @param companyId company id
     * @param evaluationIds evaluation ids
     * @return evaluation_id -> upcoming count
     */
    public Map<Long, Integer> batchGetUpcomingCountByEvaluationId(Long companyId, List<Long> evaluationIds) {
        // 查询公司时区
        String timezoneName = companyService
                .getCompanyPreferenceSetting(GetCompanyPreferenceSettingRequest.newBuilder()
                        .setCompanyId(companyId)
                        .build())
                .getPreferenceSetting()
                .getTimeZone()
                .getName();
        String date = DateUtil.convertLocalDateToDateString(LocalDateTime.now(), timezoneName, "yyyy-MM-dd");
        Integer nowMinutes = DateUtil.getNowMinutes(timezoneName);

        // 查询 upcoming 的 evaluation service detail
        List<EvaluationServiceDetail> evaluationDetails =
                petEvaluationService.getPetEvaluationList(companyId, date, nowMinutes, evaluationIds);

        // 3. 分组统计
        Map<Long, Integer> evaluationCountMap = evaluationDetails.stream()
                .filter(detail -> evaluationIds.contains(detail.getServiceId()))
                .collect(Collectors.groupingBy(
                        EvaluationServiceDetail::getServiceId,
                        Collectors.collectingAndThen(Collectors.counting(), Long::intValue)));

        // 确保所有请求的 evaluationIds 都在返回结果中，如果没有对应的预约则计数为0
        return evaluationIds.stream()
                .collect(Collectors.toMap(
                        Function.identity(),
                        evaluationId -> evaluationCountMap.getOrDefault(evaluationId, 0),
                        (o, n) -> o));
    }

    /**
     * Batch get upcoming appointment count for each customer
     *
     * @param companyId
     * @param customerIds
     * @return customer_id -> upcoming count
     */
    public Map<Long, Integer> batchGetUpcomingCountByCustomerId(Long companyId, List<Long> customerIds) {
        if (CollectionUtils.isEmpty(customerIds)) {
            return Map.of();
        }
        // query company timezone
        String timezoneName = companyService
                .getCompanyPreferenceSetting(GetCompanyPreferenceSettingRequest.newBuilder()
                        .setCompanyId(companyId)
                        .build())
                .getPreferenceSetting()
                .getTimeZone()
                .getName();
        String date = DateUtil.convertLocalDateToDateString(LocalDateTime.now(), timezoneName, "yyyy-MM-dd");
        Integer nowMinutes = DateUtil.getNowMinutes(timezoneName);
        // query upcoming appointment count
        return appointmentMapper
                .selectManyMappedRows(select(moeGroomingAppointment.customerId, count().as("count"))
                        .from(moeGroomingAppointment)
                        .where(moeGroomingAppointment.companyId, isEqualTo(companyId))
                        .and(
                                moeGroomingAppointment.customerId,
                                isIn(customerIds.stream().map(Long::intValue).toList()))
                        .and(moeGroomingAppointment.isDeprecate, isFalse())
                        .and(moeGroomingAppointment.isBlock, isEqualTo(CommonConstant.DELETED))
                        .and(moeGroomingAppointment.status, isIn(getActiveStatusSet()))
                        .and(moeGroomingAppointment.bookOnlineStatus, isEqualTo(CommonConstant.DISABLE))
                        .and(moeGroomingAppointment.isWaitingList, isEqualTo(CommonConstant.DISABLE))
                        .and(
                                moeGroomingAppointment.appointmentDate,
                                isGreaterThan(date),
                                or(
                                        moeGroomingAppointment.appointmentDate,
                                        isEqualTo(date),
                                        and(
                                                moeGroomingAppointment.appointmentEndTime,
                                                isGreaterThanOrEqualTo(nowMinutes))))
                        .groupBy(moeGroomingAppointment.customerId)
                        .build()
                        .render(RenderingStrategies.MYBATIS3))
                .stream()
                .collect(Collectors.toMap(
                        result -> ((Number) result.get("customer_id")).longValue(), // Key: customer_id
                        result -> ((Number) result.get("count")).intValue() // Value: count
                        ));
    }

    /**
     * Count appointment for pets.
     *
     * @param petIds pet ids
     * @return pet_id -> total count
     */
    public Map<Long, Integer> countAppointmentForPets(Collection<Long> petIds) {
        if (ObjectUtils.isEmpty(petIds)) {
            return Map.of();
        }

        /*
         select moe_grooming_pet_detail.pet_id, count(distinct moe_grooming_pet_detail.grooming_id) as cnt
         from moe_grooming_pet_detail join moe_grooming_appointment on moe_grooming_pet_detail.grooming_id = moe_grooming_appointment.id
         where moe_grooming_pet_detail.pet_id in (?)
           and moe_grooming_pet_detail.status = ?
           and moe_grooming_appointment.status in (?,?,?,?,?)
           and moe_grooming_appointment.is_deprecate = ?
         group by moe_grooming_pet_detail.pet_id
        */
        var ssp = select(
                        moeGroomingPetDetail.petId,
                        countDistinct(moeGroomingPetDetail.groomingId).as("cnt"))
                .from(moeGroomingPetDetail)
                .join(moeGroomingAppointment)
                .on(moeGroomingPetDetail.groomingId, equalTo(moeGroomingAppointment.id))
                .where(
                        moeGroomingPetDetail.petId,
                        isIn(petIds.stream().map(Long::intValue).collect(Collectors.toSet())))
                .and(moeGroomingPetDetail.status, isEqualTo((byte) PetDetailStatus.NORMAL_VALUE))
                .and(moeGroomingAppointment.status, isIn(ACTIVE_STATUS_VALUE_SET))
                .and(moeGroomingAppointment.isDeprecate, isFalse())
                .groupBy(moeGroomingPetDetail.petId)
                .build()
                .render(RenderingStrategies.MYBATIS3);

        var petIdToCount = petDetailMapper.selectManyMappedRows(ssp).stream()
                .collect(Collectors.toMap(
                        r -> ((Number) r.get("pet_id")).longValue(), r -> ((Number) r.get("cnt")).intValue()));

        return petIds.stream()
                .collect(Collectors.toMap(
                        Function.identity(), petId -> petIdToCount.getOrDefault(petId, 0), (o, n) -> o));
    }

    /**
     * Delete appointment by ids.
     *
     * @param companyId company id, optional
     * @param businessId business id, optional
     * @param appointmentIds appointment ids
     * @return affected rows
     */
    public int deleteAppointments(
            @Nullable Long companyId, @Nullable Integer businessId, Collection<Integer> appointmentIds) {
        if (ObjectUtils.isEmpty(appointmentIds)) {
            return 0;
        }

        return appointmentMapper.update(c -> c.set(moeGroomingAppointment.isDeprecate)
                .equalTo(true)
                .where(moeGroomingAppointment.id, isIn(appointmentIds))
                .and(moeGroomingAppointment.companyId, isEqualToWhenPresent(companyId))
                .and(moeGroomingAppointment.businessId, isEqualToWhenPresent(businessId))
                .and(moeGroomingAppointment.isDeprecate, isFalse()));
    }

    /**
     * Restore appointments by ids.
     *
     * @param companyId company id
     * @param businessId business id
     * @param appointmentIds appointment ids
     * @return affected rows
     */
    public int restoreAppointments(
            @Nullable Long companyId, @Nullable Integer businessId, Collection<Integer> appointmentIds) {
        if (ObjectUtils.isEmpty(appointmentIds)) {
            return 0;
        }

        return appointmentMapper.update(c -> c.set(moeGroomingAppointment.isDeprecate)
                .equalTo(false)
                .where(moeGroomingAppointment.id, isIn(appointmentIds))
                .and(moeGroomingAppointment.companyId, isEqualToWhenPresent(companyId))
                .and(moeGroomingAppointment.businessId, isEqualToWhenPresent(businessId))
                .and(moeGroomingAppointment.isDeprecate, isTrue()));
    }

    public MoeGroomingAppointment getAppointment(Long appointmentId) {
        return appointmentMapper
                .selectOne(c -> c.where(moeGroomingAppointment.id, isEqualTo(appointmentId.intValue())))
                .orElseThrow(() -> bizException(Code.CODE_APPOINTMENT_NOT_FOUND));
    }

    public Map<Long, List<AppointmentModel>> getTimeOverlapAppointmentList(
            long companyId,
            List<Long> customerIdList,
            List<Long> petIdsList,
            GetTimeOverlapAppointmentListRequest.Filter filter) {
        if (CollectionUtils.isEmpty(customerIdList)) {
            return Map.of();
        }
        var timezoneName = companyRemoteService.getTimezoneName(companyId);

        var startEndDate = CriteriaUtils.buildStartEndDate(filter.getDateRange(), timezoneName);
        var startDate = startEndDate.first();
        var endDate = startEndDate.second();

        // list appointments
        List<MoeGroomingAppointment> appointments =
                appointmentMapper.select(c -> c.where(moeGroomingAppointment.companyId, isEqualTo(companyId))
                        .and(
                                moeGroomingAppointment.customerId,
                                isIn(customerIdList.stream().map(Long::intValue).toList()))
                        .and(moeGroomingAppointment.status, isNotEqualTo((byte) AppointmentStatus.CANCELED_VALUE))
                        .and(moeGroomingAppointment.isDeprecate, isFalse())
                        .and(moeGroomingAppointment.isBlock, isEqualTo(CommonConstant.DELETED))
                        .and(moeGroomingAppointment.isWaitingList, isEqualTo(CommonConstant.DISABLE))
                        .and(moeGroomingAppointment.bookOnlineStatus, isEqualTo(CommonConstant.DISABLE))
                        .and(group(
                                moeGroomingAppointment.appointmentDate,
                                isBetween(startDate.toString()).and(endDate.toString()),
                                or(
                                        moeGroomingAppointment.appointmentEndDate,
                                        isBetween(startDate.toString()).and(endDate.toString())))));

        if (appointments.isEmpty()) {
            return Map.of();
        }

        // list appointment id
        List<Integer> appointmentIds =
                appointments.stream().map(MoeGroomingAppointment::getId).toList();

        // list pet details
        List<MoeGroomingPetDetail> petDetails = petDetailMapper.select(c -> c.where(
                        moeGroomingPetDetail.groomingId, isIn(appointmentIds))
                .and(moeGroomingPetDetail.status, isEqualTo((byte) PetDetailStatus.NORMAL_VALUE))
                .and(
                        moeGroomingPetDetail.petId,
                        isInWhenPresent(petIdsList.stream().map(Long::intValue).toList())));

        if (petDetails.isEmpty()) {
            return Map.of();
        }

        Map<Integer, MoeGroomingAppointment> appointmentMap =
                appointments.stream().collect(Collectors.toMap(MoeGroomingAppointment::getId, Function.identity()));

        Map<Long, Set<AppointmentModel>> resultSets = new HashMap<>();
        for (MoeGroomingPetDetail petDetail : petDetails) {
            MoeGroomingAppointment appointment = appointmentMap.get(petDetail.getGroomingId());
            if (appointment != null) {
                Long petId = Long.valueOf(petDetail.getPetId());
                AppointmentModel appointmentModel = AppointmentConverter.INSTANCE.toModel(appointment);
                resultSets.computeIfAbsent(petId, k -> new HashSet<>()).add(appointmentModel);
            }
        }

        Map<Long, List<AppointmentModel>> result = new HashMap<>();
        resultSets.forEach((petId, appointmentSet) -> result.put(petId, new ArrayList<>(appointmentSet)));

        return result;
    }

    public List<MoeGroomingAppointment> listUpcommingAppointmentForCustomer(Long companyId, Long customerId) {
        String timezoneName = companyRemoteService.getTimezoneName(companyId);

        return appointmentMapper.select(c -> c.where(moeGroomingAppointment.companyId, isEqualTo(companyId))
                .and(moeGroomingAppointment.customerId, isEqualTo(customerId.intValue()))
                .and(moeGroomingAppointment.status, isIn(getActiveStatusSet()))
                .and(moeGroomingAppointment.isDeprecate, isFalse())
                .and(moeGroomingAppointment.isBlock, isEqualTo(CommonConstant.DELETED))
                .and(moeGroomingAppointment.bookOnlineStatus, isEqualTo(CommonConstant.DISABLE))
                .and(moeGroomingAppointment.isWaitingList, isEqualTo(CommonConstant.DISABLE))
                .and(
                        moeGroomingAppointment.appointmentDate,
                        isGreaterThan(LocalDate.now(ZoneId.of(timezoneName)).toString())));
    }
}
