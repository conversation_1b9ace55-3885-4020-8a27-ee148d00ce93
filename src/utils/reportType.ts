import { type FinanceActionName } from './reportData/finance';
import { type GroomingReportActionName } from './reportData/grooming/grooming';
import { type PaymentActionName } from './reportData/payment';

export enum ReportEvent {
  // old event
  SetUserInfo = 'setUserInfo',
  SubscriptionPurchase = 'subscriptionPurchase',
  Login = 'login',
  Logout = 'logOut',
  ActionTrack = 'actionTrack',
}

export enum ReportActionName {
  GroomingReportOnboard = 'groomingReport-onboard',
  GroomingReportSaveReport = 'groomingReport-saveReport',
  GroomingReportSubmitReport = 'groomingReport-submitReport',
  GroomingReportSendReport = 'groomingReport-sendReport',
  GroomingReportPetConditionEdit = 'groomingReport-petConditionEdit',
  GroomingReportPreviewReport = 'groomingReport-previewReport',
  GroomingReportEditTemplate = 'groomingReport-editTemplate',
  GroomingReportUpdateTemplate = 'groomingReport-updateTemplate',
  GroomingReportCheckItOut = 'groomingReport-checkItOut',

  // daily report
  CheckDailyReportAtOverview = 'check-daily-report-at-overview',
  CheckDailyReportAtAppointment = 'check-daily-report-at-appointment',
  SendDailyReport = 'send-daily-report',

  // playgroup
  PlaygroupDayViewDrag = 'playgroup-day-view-drag',
  PlaygroupWeekViewDrag = 'playgroup-week-view-drag',
  PlaygroupDragSuccess = 'playgroup-drag-success',
  PetSwitchPlaygroup = 'pet-switch-playgroup',
  PlaygroupCheckAppointment = 'playgroup-check-appointment',

  // old event name is below
  CalendarStaffNameClick = 'calendarStaffName-click',

  MultiStaffAck = 'multiStaff-ack',
  MultiStaffAssign = 'multiStaff-assign',

  ObAbandonListAddClientName = 'obAbandonList-Add Client(name)',
  payrollSelectFixedRate = 'payroll-selectFixedRate',
  payrollSelectTieredRate = 'payroll-selectTieredRate',

  MobileFeeAck = 'mobileFee-ack',
  MobileFeeCreate = 'mobileFee-create',
  MobileFeeManualEdit = 'mobileFee-manualEdit',
  MobileFeeDelete = 'mobileFee-manualDelete',

  ObAbandonListContact = 'obAbandonList-contact',
  ObAbandonListSendMassText = 'obAbandonList-sendMassText',
  ObAbandonListSendMassEmail = 'obAbandonList-sendMassEmail',
  ObAbandonRecoveryMessageUpdate = 'obAbandon-recoveryMessageUpdate',

  NewCalendarLearnMore = 'newCalendar-learnMore',
  NewCalendarCheckItOut = 'newCalendar-checkItOut',
  NewCalendarSwitch = 'newCalendar-switch',
  OnboardingModalOpen = 'onboardingModal-Open',
  SuccessModalOpen = 'successModal-Open',

  CalendarSwitch2To3 = 'switch2To3',
  CalendarSwitch1To3 = 'switch1To3',
  CalendarSwitchBack2 = 'switchBack2',
  CalendarSwitchBack1 = 'switchBack1',
  CalendarSwitchStatus = 'switchStatus',
  CalendarSendMessage = 'sendMessage',
  CalendarEditServiceExplore = 'editServiceExplore',
  CalendarEditServiceSave = 'editServiceSave',
  CalendarExploreMoreOptions = 'exploreMoreOptions',
  CalendarAdvanceEdit = 'advanceEdit',
  CalendarAccessCommentsNotes = 'accessCommentsNotes',
  FeedingAndMedication = 'feedingAndMedication',
  CalendarPetNoteViewAll = 'petNoteViewAll',
  CalendarPetNoteViewEdit = 'petNoteViewEdit',
  CalendarClientNoteViewAll = 'clientNoteViewAll',
  CalendarClientNoteEdit = 'clientNoteEdit',
  CalendarQuickAddAdvancedEdit = 'CalendarQuickAddAdvancedEdit',
  CalendarSmartScheduling = 'CalendarSmartScheduling',
  AdvancedEditSmartScheduling = 'AdvancedEditSmartScheduling',

  // Tip Settings
  MoeGoPayInvoiceAddTip = 'invoice-addTip',
  MoeGoPayTipSignature = 'takePayment-signature',
  MoeGoPayTipWithoutSignature = 'takePayment-withoutSignature',
  MoeGoPayTippingNoTip = 'tipping-noTip',
  MoeGoPayTippingCustom = 'tipping-custom',
  MoeGoPayTippingRate = 'tipping-rate',

  // Desktop new repeat flow
  RepeatQuickAddPreview = 'quickAdd-preview',
  RepeatQuickAddEditRepeatRule = 'quickAdd-editRepeatRule',
  RepeatApptDetailPreview = 'apptDetail-preview',
  RepeatAdvancedEditEditRepeatRule = 'advancedEdit-editRepeatRule',
  RepeatAdvancedEditPreview = 'advancedEdit-preview',
  RepeatSmartSchedulingOptimize = 'repeat-smartScheduling-optimize',
  RepeatBookNow = 'repeat-bookNow',

  // smart waitlist
  WaitlistViewExplore = 'waitlistView-explore',
  WaitlistCalendarViewExplore = 'waitlistCalendarView-explore',
  WaitlistDetailAvailableOpen = 'waitlistDetail-available-open',
  WaitlistDetailUnavailableOpen = 'waitlistDetail-unavailable-open',
  WaitlistDetailBookNow = 'waitlistDetail-booknow',
  WaitlistDetailReschedule = 'waitlistDetail-reschedule',
  CancelRecommendation = 'cancelRecommendation-viewDetail',
  AppointmentToWaitlist = 'appointmentToWaitlist-click',

  // 产品内 sign up flow
  InProductSignUp = 'inProduct-signUp',
  InProductLogIn = 'inProduct-logIn',
  InProductCreateCompany = 'inProduct-createCompany',
  InProductSelectQuestion = 'inProduct-selectQuestion',
  inProductSelectPlan = 'inProduct-selectPlan',

  // 产品内 - upgrade flow
  InProductPurchasePlanCompanyInfo = 'inProduct-purchase-planCompanyInfo',
  InProductPurchaseSelectContractPeriod = 'inProduct-purchase-selectContractPeriod',
  InProductPurchaseSelectHardware = 'inProduct-purchase-selectHardware',
  InProductPurchasePaymentMethod = 'inProduct-purchase-paymentMethod',
  InProductPurchaseSignContract = 'inProduct-purchase-signContract',
  InProductPurchaseUpgradePlan = 'inProduct-purchase-upgradePlan',

  // evaluation
  evaluationApptLodgingAllocation = 'evaluationAppointment-lodgingAllocation',
  evaluationApptStaffAssign = 'evaluationAppointment-staffAssign',
  evaluationSetPeriodValid = 'evaluation-set-period-valid',
  evaluationSetPeriodMonth = 'evaluation-set-period-month',
  checkEvaluationHistory = 'evaluation-check-history',
  combinedEvaluationAdd = 'combinedEvaluation-add',
  combinedEvaluationRemove = 'combinedEvaluation-remove',

  // appt card
  markAsReadyClick = 'apptCard-markAsReadyClick',
  addSplitLodgingClick = 'apptCard-addSplitLodgingClick',

  // workflow
  sideBarWorkflowClick = 'sideBarworkflow-click',
  createWorkflowClick = 'createWorkflow-click',
  listPageTemplateClick = 'listPagetemplate-click',
  templateClick = 'template-click',
  templateViewAllClick = 'templateViewall-click',
  settingButtonClick = 'settingButton-click',
  statusClick = 'status-click',
  settingSave = 'settingSave',
  workflowSaveClick = 'workflow-save-click',
  workflowActivateClick = 'workflow-activate-click',
  workflowDeactivateClick = 'workflow-deactivate-click',

  // calling
  markIsResolvedClick = 'callActivity-markIsResolvedClick',
  markAllResolvedClick = 'callActivity-markAllResolvedClick',
  // membership
  createMembership = 'membership-createMembership',
  editMembershipFromList = 'membership-editMembershipFromList',
  editMembershipFromDetail = 'membership-editMembershipFromDetail',
  sellMembershipBusinessSide = 'membership-sellMembershipBusinessSide',
  sellMembershipMessageLink = 'membership-sellMembershipMessageLink',
  purchaseMembershipBusinessSideNow = 'membership-purchaseMembershipBusinessSideNow',
  purchaseMembershipBusinessSideFuture = 'membership-purchaseMembershipBusinessSideFuture',
  applyMembershipSellProduct = 'membership-applyMembershipSellProduct',
  manualAdjustCredit = 'storeCredit-manualAdjustCredit',
  manualAdjustCreditConfirm = 'storeCredit-manualAdjustCreditConfirm',
  useCreditInAppointment = 'storeCredit-useCreditInAppointment',
  checkKeepChangeAsCredit = 'storeCredit-checkKeepChangeAsCredit',
  sellProductWithGeneralPrice = 'sell-product-with-general-price',
  sellProductAddMembership = 'sell-product-add-membership',
  sellProductApplyMembership = 'sell-product-apply-membership',
  sellProductWithMembershipPrice = 'sell-product-with-membership-price',
  sellMembershipPackageListError = 'sell-membership-package-list-error',

  // Multiple Pets
  createMultiplePets = 'create-multiple-pets',
  createSinglePetInMultipleDrawer = 'create-single-pet-in-multiple-drawer',
  evaluationCreateMultiplePets = 'evaluation-create-multiple-pets',
  evaluationCreateSinglePet = 'evaluation-create-single-pet',

  // daycare / grooming appointment add boarding service
  changeSingleToMultiDaysAppointment = 'change-single-to-multi-days-appointment',

  // Package
  startSellPackageBusinessSide = 'startSellPackageBusinessSide',
  sellPackageBusinessSide = 'sellPackageBusinessSide',

  // Appointment
  apptCheckInHomePage = 'apptCheckIn-homePage',
  apptCheckInHomePageFinish = 'apptCheckIn-homePageFinish',
  apptCheckInDrawerCTA = 'apptCheckIn-drawerCTA',
  apptCheckInDrawerDropdown = 'apptCheckIn-drawerDropdown',
  apptCheckInQuickCheckIn = 'apptCheckIn-quickCheckIn',
  apptCheckInQuickCheckInModalView = 'apptCheckIn-quickCheckInModalView',
  apptCheckInAlertsView = 'apptCheckIn-alertsView',
  apptDrawerBookNowClick = 'apptDrawerBookNow-click',
  apptAdvancedEditBookNowClick = 'apptAdvancedEditBookNow-click',
  earlyCheckInConfirmClick = 'earlyCheckIn-confirmClick',
  earlyCheckInCancelClick = 'earlyCheckIn-cancelClick',
  apptEditServicePrice = 'apptEdit-servicePrice',
  apptEditServiceDuration = 'apptEdit-serviceDuration',

  // lodging view
  lodgingViewOpenApptDetail = 'lodgingView-openApptDetail',
  lodgingViewOpenQuickAddAppt = 'lodgingView-openQuickAddAppt',
  lodgingViewDragToReschedule = 'lodgingView-dragToReschedule',
  lodgingViewDragToChangeLodging = 'lodgingView-dragToChangeLodging',
  lodgingViewFilterLodgingStatus = 'lodgingView-filterLodgingStatus',
  lodgingViewFilterLodgingTypes = 'lodgingView-filterLodgingTypes',
  lodgingViewChangeDate = 'lodgingView-changeDate',
  lodgingViewChangeWeekType = 'lodgingView-changeWeekType',
  lodgingViewCollapseAll = 'lodgingView-collapseAll',
  lodgingViewLodgingTypeCollapse = 'lodgingView-lodgingTypeCollapse',
  lodgingViewView = 'lodgingView-view',
  lodgingViewLeave = 'lodgingView-leave',
  lodgingViewDrag = 'lodgingView-drag',
  lodgingViewPetAvatarPreview = 'lodgingView-petAvatarPreview',

  // homepage view
  homepageHeaderIconReportClick = 'homepageHeaderIcon-reportClick',
  homepageHeaderIconPrintCardClick = 'homepageHeaderIcon-printCardClick',
  homepageHeaderIconSummaryClick = 'homepageHeaderIcon-summaryClick',
  homepageHeaderIconWaitlistClick = 'homepageHeaderIcon-waitlistClick',
  homepageHeaderIconTasksClick = 'homepageHeaderIcon-tasksClick',

  homepageView = 'homepage-view',
  homepageLeave = 'homepage-leave',
  homepageDateClick = 'homepage-dateClick',
  homepageLocationSwitchClick = 'homepage-locationSwitchClick',
  homepageHeaderSearch = 'homepageHeader-search',
  homepageHeaderFilterClick = 'homepageHeader-filterClick',
  homepageTableHeaderSortClick = 'homepageTableHeader-sortClick',
  homepageTableHeaderApptStatusFilterClick = 'homepageTableHeader-apptStatusFilterClick',
  homepageTableHeaderReportCardFilterClick = 'homepageTableHeader-reportCardFilterClick',
  homepageTabClick = 'homepage-tabClick',
  homepageItemApptClick = 'homepageItem-apptClick',
  homepageItemInfoIconPreview = 'homepageItem-infoIconPreview',
  homepageItemAddOnPreview = 'homepageItem-addOnPreview',
  homepageTablePaginationClick = 'homepageTable-paginationClick',

  homepageItemClientNameClick = 'homepageItem-clientNameClick',
  homepageItemIconMembershipPreview = 'homepageItem-iconMembershipPreview',
  homepageItemIconCardOnFilePreview = 'homepageItem-iconCardOnFilePreview',
  homepageItemIconPackagePreview = 'homepageItem-iconPackagePreview',
  homepageItemIconAlertNotePreview = 'homepageItem-iconAlertNotePreview',
  homepageItemIconUnpaidPreview = 'homepageItem-iconUnpaidPreview',
  homepageItemIconAgreementPreview = 'homepageItem-iconAgreementPreview',

  petInfoComponentPetNameClick = 'petInfoComponent-petNameClick',
  petInfoComponentAvatarPreview = 'petInfoComponent-avatarPreview',
  petInfoComponentIncidentPreview = 'petInfoComponent-incidentPreview',
  petInfoComponentIncidentsViewAll = 'petInfoComponent-incidentsViewAll',
  petInfoComponentVaccinesPreview = 'petInfoComponent-vaccinesPreview',
  petInfoComponentVaccinesUpdate = 'petInfoComponent-vaccinesUpdate',
  petInfoComponentNotesPreview = 'petInfoComponent-notesPreview',
  petInfoComponentNotesViewAll = 'petInfoComponent-notesViewAll',
  petInfoComponentNotesViewEdit = 'petInfoComponent-notesViewEdit',
  petInfoComponentCodesPreview = 'petInfoComponent-codesPreview',

  // Pricing rule
  pricingRulePageView = 'pricingRule-pageView',
  pricingRuleSettingPageView = 'pricingRule-settingPageView',
  pricingRuleDiscountSetting = 'pricingRule-discountSetting',
  pricingRuleDiscountSettingPreview = 'pricingRule-discountSettingPreview',
  pricingRuleSettingAction = 'pricingRule-settingAction',

  // printCard
  printCardTypeChange = 'print-card-type-change',
  printCardView = 'printCard-view',
  printCardLeave = 'printCard-leave',
  printCardDateClick = 'printCard-dateClick',
  printCardCheckInPetsOnlyClick = 'printCard-checkInPetsOnlyClick',
  printCardPrintClick = 'printCard-printClick',

  // merge client
  MergeClientInClientList = 'clickMergeClientInClientList',
  PreviewInDuplicateList = 'clickPreviewInDuplicateList',
  MergeInPreviewDrawer = 'clickMergeInPreviewDrawer',
  ConfirmInWindow = 'clickConfirmInConfirmWindow',
  MergeRulesInDuplicateListPage = 'clickMergeRulesInDuplicateListPage',

  // appt
  apptActionClick = 'appt-actionClick',
  apptBookAgainClick = 'appt-bookAgainClick',
  apptCheckConflictAlert = 'appt-checkConflictAlert',

  apptDrawerView = 'apptDrawer-view',
  apptDrawerLeave = 'apptDrawer-leave',
  apptDrawerFeeding = 'apptDrawer-feeding',
  apptDrawerMedication = 'apptDrawer-medication',
  apptDrawerSwitchToFeeding = 'switch-to-feeding-in-apptdrawer',
  apptDrawerSwitchToMedication = 'switch-to-medication-in-apptdrawer',
  apptDrawerSameLodgingAsClick = 'apptDrawer-sameLodgingAsClick',
  apptDrawerOnlyShowApplicableLodgingsClick = 'apptDrawer-onlyShowApplicableLodgingsClick',
  apptDrawerOnlyShowApplicableServicesClick = 'apptDrawer-onlyShowApplicableServicesClick',
  apptDrawerNavTabView = 'apptDrawer-navTabView',
  apptDrawerReportCardClick = 'apptDrawer-reportCardClick',

  // quick add appt flow
  quickAddApptFlow = 'quickAddAppt-flow',

  // online booking request accept flow for legacy modal
  obRequestLegacyTableView = 'obRequest-legacy-table-view',
  obRequestLegacyModalView = 'obRequest-legacy-modal-view',

  // online booking request accept flow view
  obRequestTableView = 'obRequest-table-view',
  obRequestModalView = 'obRequest-modal-view',

  // online booking request user actions
  obRequestInfoUpdateClick = 'obRequest-infoUpdate-click',
  obRequestAcceptClick = 'obRequest-accept-click',
  obRequestDeclineClick = 'obRequest-decline-click',
  obRequestScheduleClick = 'obRequest-schedule-click',
  obRequestToWaitlistClick = 'obRequest-toWaitlist-click',
  obRequestAcceptConfirm = 'obRequest-acceptConfirm',

  // 2fa phone number verification
  twofaPhoneNumberSetupClick = '2faPhoneNumberSetup-click',
  twofaPhoneNumberSetupSendCodeClick = '2faPhoneNumberSetupSendCode-click',
  twofaPhoneNumberSetupConfirmClick = '2faPhoneNumberSetupConfirm-click',
  twofaPhoneNumberChangeClick = '2faPhoneNumberChange-click',
  twofaPhoneNumberChangeSendCodeClick = '2faPhoneNumberChangeSendCode-click',
  twofaPhoneNumberChangeConfirmClick = '2faPhoneNumberChangeConfirm-click',
  twofaLoginRequireVerificationCodeClick = '2faLoginRequireVerificationCode-click',
  twofaLoginByVerificationCodeClick = '2faLoginByVerificationCode-click',
}

export type ReportActionNameType = ReportActionName | FinanceActionName | PaymentActionName | GroomingReportActionName;

export enum ReportClarityActionName {
  NewCalendar = 'newCalendar',
}
