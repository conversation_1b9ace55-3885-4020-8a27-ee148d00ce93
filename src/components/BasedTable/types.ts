import {
  type ColumnSort,
  type RowExpansionCommon,
  type RowSelection,
  type RowSelectionDetail,
  type RowSelectionState,
  type SortingState,
} from '@moego/ui';

export interface GroupExpansion<T> extends RowExpansionCommon<T> {
  type: 'group';
  OnlyOneExpanded?: boolean;
  groupLoading?: boolean;
  renderGroup?: (id: string, value: string) => React.ReactNode;
}

export type TableRowData<T> = {
  type: 'row' | 'group' | 'root';
  id: string;
  parentId?: string;
  groupValue: string;
  isSelected: boolean;
  isIndeterminate: boolean;
  isExpanded: boolean;
  children: TableRowData<T>[];
  original?: T;
};

export interface TableGroupData<T> {
  groupId: string;
  groupValue: string;
  items: T[];
}

export type TableRowSelection<T> = Pick<RowSelection<T>, 'selectedRowIds'> & {
  // group 列与 row 列的 id 对应关系
  selectGroupRowMap?: Record<string, string[]>;
  // group 级别的 checkbox 被选中，不论 group 下的 item 是否全选
  selectedGroupIds?: RowSelectionState | RowSelectionDetail['selectedList'];
  // group 级别的 checkbox 被选中，group 下的 item 必须全选
  selectedAllGroupIds?: RowSelectionState | RowSelectionDetail['selectedList'];
  onChange: (
    rowSelection: RowSelectionState,
    detail: RowSelectionDetail & { selectedGroupIds?: RowSelectionState; selectedAllGroupIds?: RowSelectionState },
  ) => void;
  onGroupChange?: (selectedGroupIds: RowSelectionState) => void;
  onGroupAllChange?: (selectedAllGroupIds: RowSelectionState) => void;
};

export interface BasedTableProps<T> {
  data: T[];
  /**
   * Group 的结构不太好用 data 来表达，所以新增了一个结构。
   *
   * 现在只支持展示一个层级的 group。
   */
  groupData?: TableGroupData<T>[];
  columns: BasedTableColumnProps<T>[];
  onSortingChange?: (sorting: ColumnSort) => void;
  expandable?: (record: T) => boolean;
  expandedRowRender?: (record: T) => React.ReactNode;
  rowKey?: (record: T) => string;
  className?: string;
  onRowClick?: (record: T) => void;
  isVirtual?: boolean;
  /**
   * 参考组件库的设置。但是并非所有的配置可用，按需实现需要的配置
   *
   * Selection 只在 Group mode 测试过，Tree 模式下可能不会正常工作。
   *
   * 在 Group 下，selectedId 只会记录正在 table 行数据，Group 行的 groupId 不需要记录，会动态计算。
   */
  rowSelection?: TableRowSelection<T>;
  /**
   * 参考组件库的设置。但是并非所有的配置可用，按需实现需要的配置
   */
  rowExpansion?: GroupExpansion<T>;

  stickyHeader?: boolean;

  /**
   * 没有数据的时候展示的区域
   */
  noDataArea?: () => React.ReactNode;

  containerClassName?: string;
  scrollYContainer?: HTMLDivElement | null;
  isLoading?: boolean;
  showHeaderWhenEmpty?: boolean;
  renderExpandedDesc?: (isExpanded: boolean) => React.ReactNode;
}

export interface BasedTableColumnProps<T> {
  prop: keyof T | 'handler';
  title?: string;
  id?: string;
  render?: (data: T, index: number) => React.ReactNode;
  renderHeader?: (column: BasedTableColumnProps<T>, index: number, data?: T) => React.ReactNode;
  width?: number;
  minWidth?: number;
  maxWidth?: number;
  stickyRight?: boolean;
  alignRight?: boolean;
  customClassNames?: string;
  enableSorting?: boolean;
  onSortingChange?: (sorting: SortingState) => void;
  titleClassName?: string;
}

export enum ModalType {
  Add = 'add',
  Edit = 'edit',
}
