import { MinorChevronDownOutlined, MinorChevronRightOutlined, MinorChevronUpOutlined } from '@moego/icons-react';
import { Checkbox, SpinIcon, cn } from '@moego/ui';
import { type Virtualizer } from '@tanstack/react-virtual';
import React, { useEffect } from 'react';
import { Condition } from '../../components/Condition';
import { Switch } from '../../components/SwitchCase';
import { memo } from '../../utils/react';
import { commonStyle, ensureRowState, rowStateMapToArray, setNewRowState } from './BasedTable.utils';
import { type BasedTableColumnProps, type GroupExpansion, type TableRowData } from './types';

export interface BasedTableRowProps<T> {
  rowKey: string;
  isExpanded: boolean;
  columns: BasedTableColumnProps<T>[];
  isExpandable: boolean;
  /**
   * 是否渲染额外的可折叠列
   */
  hasExpandableColum?: boolean;
  handleToggleExpandedRowKeys: (key: string) => void;
  dataList?: TableRowData<T>[];
  data: TableRowData<T>;
  expandedRowRender?: (item: T) => React.ReactNode;
  onClick?: (item: T) => void;
  isSelectable?: boolean;
  handleToggleSelectRow?: (key: string, value: boolean) => void;
  rowExpansion?: GroupExpansion<T>;
  virtualIndex?: number;
  virtualizer?: Virtualizer<HTMLDivElement, Element>;
  isLoading?: boolean;
  renderExpandedDesc?: (isExpanded: boolean) => React.ReactNode;
}

export const BasedTableRow = memo(function BasedTableRow<T>(props: BasedTableRowProps<T>) {
  const {
    rowKey,
    isExpanded,
    columns,
    isExpandable,
    hasExpandableColum,
    handleToggleExpandedRowKeys,
    dataList,
    data,
    expandedRowRender,
    onClick,
    isSelectable,
    handleToggleSelectRow,
    rowExpansion,
    virtualIndex,
    virtualizer,
    isLoading,
    renderExpandedDesc,
  } = props;
  const icon = !isExpanded ? <MinorChevronDownOutlined /> : <MinorChevronUpOutlined />;

  const isGroup = data.type === 'group';

  const groupColspan = columns.length;
  const groupValue = isGroup ? data.groupValue : '';
  const rowData = isGroup ? undefined : data.original;

  useEffect(() => {
    if (!isLoading) {
      // 当 group 展开时，并且之前的状态为全选或全不选，则遍历刚请求到的列表下的数据，更新selected状态
      if (data.type !== 'row' && data.isExpanded && !data.isIndeterminate) {
        handleToggleSelectRow?.(data.id, data.isSelected);
      }
    }
  }, [isLoading, data.isExpanded, data.isSelected]);

  return (
    <React.Fragment key={rowKey}>
      <tr
        className={cn(
          'moe-group',
          isExpanded && isExpandable ? 'table-expand-relative-row' : 'table-row',
          {
            'hover:moe-bg-neutral-sunken-light': !isGroup,
            'moe-bg-neutral-sunken-0': isGroup,
            'moe-bg-brand-subtle': data.isSelected && !isGroup,
          },
          'moe-cursor-pointer',
        )}
        data-index={virtualIndex}
        ref={virtualizer?.measureElement}
        onClick={async () => {
          if (data.type === 'row') {
            rowData && onClick?.(rowData);
          } else {
            const expandIdMap = ensureRowState(rowExpansion?.expandedRowIds);
            const isExpanded = expandIdMap[data.id];
            const currentExpanded = !isExpanded;
            const newIdMap = setNewRowState(expandIdMap, { [data.id]: currentExpanded });

            if (currentExpanded && rowExpansion?.OnlyOneExpanded && dataList?.length) {
              dataList.forEach((item) => {
                if (item.id !== data.id && item.type === 'group') {
                  newIdMap[item.id] = false;
                }
              });
            }

            await rowExpansion?.onChange?.(newIdMap, {
              expansionType: 'row',
              isExpanded: currentExpanded,
              expandedList: rowStateMapToArray(newIdMap),
              unexpandedList: [],
              rowId: data.id,
            });
          }
        }}
      >
        <Condition if={hasExpandableColum}>
          <td style={commonStyle}>
            <div className="moe-flex moe-justify-center moe-select-none">
              <Switch>
                <Switch.Case if={data.isExpanded && rowExpansion?.groupLoading}>
                  <SpinIcon />
                </Switch.Case>
                <Switch.Case else={isGroup}>
                  <MinorChevronRightOutlined
                    className={cn({
                      'moe-rotate-90': data.isExpanded,
                    })}
                  />
                </Switch.Case>
              </Switch>
            </div>
          </td>
        </Condition>

        <Condition if={isSelectable}>
          <td style={commonStyle} className="moe-select-none">
            <div
              className="moe-flex moe-justify-center moe-px-spacing-xs"
              onClick={(e) => {
                e.stopPropagation();
              }}
            >
              <Checkbox
                isSelected={data.isSelected}
                isIndeterminate={data.isIndeterminate}
                onChange={(check) => {
                  handleToggleSelectRow?.(rowKey, check);
                }}
              />
            </div>
          </td>
        </Condition>
        <Switch>
          <Switch.Case if={isGroup}>
            <td colSpan={groupColspan} className="!moe-px-spacing-s">
              {rowExpansion?.renderGroup?.(data.id, groupValue) ?? groupValue}
            </td>
          </Switch.Case>
          <Switch.Case else>
            {columns.map((column, index) => {
              const showExpandedButton = isExpandable && index === 0;
              const isStickyRight = !!column.stickyRight;
              const isAlignRight = !!column.alignRight;
              return (
                <td
                  key={index}
                  className={cn('moe-text-regular-short moe-text-primary !moe-px-spacing-s', column?.customClassNames, {
                    'moe-sticky moe-right-0': isStickyRight,
                    '!moe-align-middle': !isExpandable,
                  })}
                  style={{
                    width: column.width,
                    minWidth: column.minWidth ?? column.width,
                    maxWidth: column.maxWidth,
                  }}
                >
                  <div
                    className={cn({
                      'moe-flex moe-justify-end': isAlignRight,
                    })}
                  >
                    {column.render && rowData ? column.render(rowData, index) : rowData?.[column.prop as keyof T]}
                    <Condition if={showExpandedButton}>
                      <span
                        className="moe-text-sm-20 moe-font-regular moe-text-tertiary moe-cursor-pointer moe-mt-[12px] moe-inline-flex moe-items-center"
                        onClick={(e) => {
                          e.stopPropagation();
                          e.preventDefault();
                          handleToggleExpandedRowKeys(rowKey);
                        }}
                      >
                        {renderExpandedDesc
                          ? renderExpandedDesc(isExpanded)
                          : isExpanded
                            ? 'Collapse'
                            : 'View variants'}
                        {icon}
                      </span>
                    </Condition>
                  </div>
                </td>
              );
            })}
          </Switch.Case>
        </Switch>
      </tr>
      <Condition if={isExpandable && expandedRowRender && isExpanded}>
        <tr
          className="table-expanded-row"
          onClick={() => {
            rowData && onClick?.(rowData);
          }}
        >
          <td colSpan={columns.length - 1}>{rowData && expandedRowRender?.(rowData)}</td>
          {/* 占位，不然背景色出不来 */}
          <td colSpan={1}></td>
        </tr>
      </Condition>
    </React.Fragment>
  );
});
