import {
  Markup,
  type Option,
  LegacySelect as Select,
  type LegacySelectProps as SelectProps,
  Text,
  cn,
} from '@moego/ui';
import { type SelectInstance } from '@moego/ui/dist/esm/components/LegacySelect/ReactSelect';
import { useDispatch } from 'amos';
import React from 'react';
import SvgIconMultipleStaffSvg from '../../../assets/svg/icon-multiple-staff.svg';
import { ApptTestIds } from '../../../config/testIds/apptDrawer';
import { getAccountCompanyList } from '../../../store/company/company.actions';
import { isNormal } from '../../../store/utils/identifier';
import { useBool } from '../../../utils/hooks/useBool';
import { memoForwardRef } from '../../../utils/react';
import { Condition } from '../../Condition';
import { SvgIcon } from '../../Icon/Icon';
import { PricingUpgradeModal } from '../../Pricing/PricingUpgradeModal';
import { usePricingEnableUpgrade } from '../../Pricing/pricing.hooks';
import { useMultiStaffPermission } from '../hooks/useMultiStaffPermission';
import { type ServiceVariation, useServiceApplicableStaffList } from '../hooks/useServiceApplicableStaffList';
import { useStaffOptions } from '../hooks/useStaffOptions';
import { MultipleStaffId } from '../utils/multipleStaffId';
import { SelectMultiStaffFooter } from './SelectMultiStaffFooter';

export interface SelectMultiStaffProps
  extends Pick<
    SelectProps<{ value: number; label: string }>,
    | 'label'
    | 'placeholder'
    | 'errorMessage'
    | 'errorType'
    | 'isInvalid'
    | 'isRequired'
    | 'isDisabled'
    | 'isClearable'
    | 'classNames'
  > {
  className?: string;
  loading?: boolean;
  value?: number;
  onChange?: (v: number | undefined, serviceVariation?: ServiceVariation) => void;
  serviceId?: number;
  petId?: string;
  filterStaffIds?: number[];
  showMultiStaff?: boolean;
  showPriceDuration?: boolean;
  showMultiStaffOption?: boolean;
  clientId?: number;
}

export const SelectMultiStaff = memoForwardRef<SelectInstance<Option<number>, false>, SelectMultiStaffProps>(
  function SelectStaff(props, ref) {
    const {
      className,
      classNames,
      value,
      petId,
      serviceId,
      onChange,
      loading,
      filterStaffIds,
      showMultiStaff = true,
      showPriceDuration = false,
      placeholder = 'Select staff',
      label = 'Staff',
      showMultiStaffOption: showMultiStaffOptionProp = true,
      clientId,
      ...restSelectProps
    } = props;
    const isMultiple = value === MultipleStaffId;
    const dispatch = useDispatch();
    const { access, ...restProps } = usePricingEnableUpgrade('operation');

    const isOpen = useBool();
    const onlyAvailableStaff = useBool(true);
    const pricingUpgradeModalVisible = useBool();
    const { hasMultiStaffPermission, showUpgradeForMultiStaffTip } = useMultiStaffPermission();

    const {
      loading: isLoading,
      availableStaffList,
      unavailableStaffList,
      getServiceVariation,
    } = useServiceApplicableStaffList({
      petId,
      serviceId,
      filterStaffIds,
      selectedStaffId: value,
      onlyAvailableStaff: onlyAvailableStaff.value,
      enableServiceAvailableStaff: showPriceDuration,
      clientId,
    });

    const options = useStaffOptions({
      availableStaffList,
      unavailableStaffList,
      showDesc: showPriceDuration,
    });

    const enableMultiple =
      showMultiStaff && hasMultiStaffPermission && (options.length > 1 || value === MultipleStaffId);
    const showMultiStaffOption = (enableMultiple || showUpgradeForMultiStaffTip) && showMultiStaffOptionProp;

    return (
      <div data-testid={ApptTestIds.ApptEditPetServiceSelectStaffBtn}>
        <Select
          {...restSelectProps}
          classNames={{
            ...classNames,
            groupHeading: 'empty:!moe-hidden',
          }}
          ref={ref}
          label={label}
          placeholder={placeholder}
          showGroupDivider={false}
          isLoading={loading || isLoading}
          isOpen={isOpen.value}
          onOpenChange={isOpen.as}
          value={value}
          onChange={(value) => {
            if (isNormal(value) || restSelectProps.isClearable) {
              const serviceVariation = getServiceVariation(value);
              onChange?.(value, serviceVariation);
            }
          }}
          className={cn(className)}
          options={options}
          formatOptionLabel={(item: Option<number>) => {
            if (item) {
              const { value, label } = item;
              return value === MultipleStaffId ? 'Multi-staff' : label;
            }
            return '';
          }}
          prefix={isMultiple ? <SvgIcon src={SvgIconMultipleStaffSvg} size={18} /> : null}
          renderItem={(item) => {
            const { data, isSelected } = item;
            const OptionText = isSelected ? Markup : Text;
            return (
              <div
                className="moe-flex-1 moe-flex moe-items-center moe-justify-between"
                data-testid={ApptTestIds.ApptEditPetServiceSelectStaffOptionBtn}
              >
                <OptionText as="span" variant="small" className="moe-text-primary">
                  {data.label}
                </OptionText>
                {data.description}
              </div>
            );
          }}
          footer={
            // can not use condition here, because will render divider when footer is truly
            showMultiStaffOption || showPriceDuration ? (
              <SelectMultiStaffFooter
                showMultiStaffOption={showMultiStaffOption}
                showUpgradeForMultiStaffTip={showUpgradeForMultiStaffTip}
                onClick={(multipleStaffId) => {
                  onChange?.(multipleStaffId);
                  isOpen.close();
                }}
                showPriceDuration={showPriceDuration}
                onlyAvailableStaff={onlyAvailableStaff.value}
                onCheckOnlyAvailableStaff={onlyAvailableStaff.as}
                onUpgrade={pricingUpgradeModalVisible.open}
              />
            ) : null
          }
        />
        <Condition if={!access}>
          <PricingUpgradeModal
            visible={pricingUpgradeModalVisible.value}
            onClose={async () => {
              pricingUpgradeModalVisible.close();
              await dispatch(getAccountCompanyList());
            }}
            {...restProps}
          />
        </Condition>
      </div>
    );
  },
);
