import { CommonServiceView, CustomizedServiceByPet } from '@moego/api-web/moego/api/offering/v1/service_api';

type CustomizedServiceCategoryView = CommonServiceView & {
  categoryName: string;
};

export type GetApplicablePetServiceResultMulti =
  | {
      categoryList: CustomizedServiceCategoryView[];
      commonCategories: CustomizedServiceCategoryView[];
      petServices: CustomizedServiceByPet[];
    }
  | undefined;

export interface MixedService {
  categoryId: number;
  categoryName: string;
  serviceId: number;
  serviceType: number;
  applicable: boolean;
  serviceName?: string;
  service: CommonServiceView;
}

export interface CategoryItem {
  categoryId: number;
  categoryName: string;
  services: MixedService[];
}
