import { ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Checkbox, Spin, Tabs, cn } from '@moego/ui';
import { useStore } from 'amos';
import React, { memo, useEffect, useMemo } from 'react';
import { useSetState } from 'react-use';
import { ADCommonTestIds, ApptTestIds } from '../../config/testIds/apptDrawer';
import { serviceMapBox } from '../../store/service/service.boxes';
import { useBool } from '../../utils/hooks/useBool';
import { useControllableValue } from '../../utils/hooks/useControlledValue';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../utils/hooks/useSerialCallback';
import { Condition } from '../Condition';
import { ApplicableCategory, type ApplicableCategoryProps } from './components/ApplicableCategory';
import { ApplicableGroupWrapper } from './components/ApplicableGroupWrapper';
import { ApplicableServicesEmpty } from './components/ApplicableServicesEmpty';
import { useApplicablePetServices } from './hooks/useApplicablePetServices';
import { type ServiceEntry } from './types/serviceEntry';
import { getDefaultService } from './utils/getDefaultService';
import { ServiceTypeOptionsEnum } from './utils/options';

export interface TabItem {
  key: ServiceType;
  label: string;
  tooltip?: string;
  isSingleSelect?: boolean;
}

export type ServiceApplicablePickerProps = {
  className?: string;
  serviceItemType?: ServiceItemType;
  selectedLodgingUnitId?: string;
  prevSelectedServiceIds?: number[];
  value?: ServiceEntry[];
  tabs?: TabItem[];
  petIds: string[];
  clientId?: number;
  onChange?: (services: ServiceEntry[]) => void;
  onServiceChange?: (nextServices: ServiceEntry[], targetService: { service: ServiceEntry; checked: boolean }) => void;
};

export const defaultTabs: TabItem[] = ServiceTypeOptionsEnum.values.map((key) => ({
  key,
  label: ServiceTypeOptionsEnum.mapLabels[key],
}));

export const ServiceApplicablePicker = memo((props: ServiceApplicablePickerProps) => {
  const store = useStore();
  const {
    serviceItemType = ServiceItemType.GROOMING,
    selectedLodgingUnitId,
    prevSelectedServiceIds,
    tabs = defaultTabs,
    className,
    onServiceChange,
    petIds,
    clientId,
  } = props;

  const [selectedServices, setSelectedServices] = useControllableValue<ServiceEntry[]>(props, {
    defaultValue: [],
  });

  const [state, setState] = useSetState({
    activeKey: tabs[0]?.key,
  });
  const onlyAvailable = useBool(true);
  const { activeKey } = state;

  const selectedServiceIds = useMemo(
    () => selectedServices.filter((s) => s.serviceType === ServiceType.SERVICE).map((s) => s.serviceId),
    [selectedServices],
  );

  const currentMainServiceId = useMemo(() => {
    const tab = tabs.find((tab) => tab.key === activeKey);
    if (tab?.isSingleSelect) {
      return selectedServiceIds[0];
    }
    return undefined;
  }, [selectedServiceIds, activeKey, tabs]);

  const { loading, getServiceInfo, getRenderListInfo, applicableAddonIds, applicableServiceIds } =
    useApplicablePetServices({
      onlyAvailable: onlyAvailable.value,
      selectedServiceIds: selectedServiceIds?.map((s) => String(s)) || [],
      serviceItemType,
      serviceType: activeKey,
      selectedLodgingUnitId,
      petIds,
      customerId: String(clientId),
    });

  const currentApplicableIds = activeKey === ServiceType.ADDON ? applicableAddonIds : applicableServiceIds;
  const applicableServiceIdsFilter = useLatestCallback((service: ServiceEntry[]) => {
    // 过滤当前tab下的不可选service
    return service.filter((item) => item.serviceType !== activeKey || currentApplicableIds.includes(item.serviceId));
  });

  const onToggleService = useSerialCallback(async (serviceId: number, serviceType: ServiceType, checked: boolean) => {
    const { isSingleSelect } = tabs.find((tab) => tab.key === serviceType)!;
    const service = store.select(serviceMapBox.mustGetItem(serviceId));
    const { name: serviceName, requireDedicatedStaff } = service;
    const newVal = getDefaultService({
      ...getServiceInfo(serviceId),
      serviceId,
      serviceName,
      serviceType,
      requireDedicatedStaff,
      bundleServiceIds: service.bundleServiceIds,
      serviceItemType: service.serviceItemType,
    });

    const nextServices = selectedServices.slice();
    if (isSingleSelect && serviceType === ServiceType.SERVICE) {
      const index = nextServices.findIndex((item) => item.serviceType === ServiceType.SERVICE);
      index > -1 && nextServices.splice(index, 1);
      nextServices.push(newVal);
    } else {
      const index = nextServices.findIndex((item) => item.serviceId === serviceId);
      if (index > -1) {
        nextServices.splice(index, 1);
      } else {
        nextServices.push(newVal);
      }
    }
    // 需过滤当前tab下不可选的service
    const nextServicesFilter = applicableServiceIdsFilter(nextServices);
    setSelectedServices(nextServicesFilter);
    onServiceChange?.(nextServicesFilter, { service: newVal, checked });
  });

  // 如果切换了show applicable services，需要过滤不可选的service
  useEffect(() => {
    if (!loading) {
      setSelectedServices(applicableServiceIdsFilter);
    }
  }, [onlyAvailable.value, loading]);

  const getDisabled = useLatestCallback((id: number) => prevSelectedServiceIds?.includes(id));
  const getChecked = useLatestCallback(
    (id: number) => prevSelectedServiceIds?.includes(id) || selectedServices.some((s) => s.serviceId === id),
  );

  useEffect(() => {
    return () => {
      if (petIds.length) {
        // 如果 pets 有值，则不进行清空 service
        return;
      }
      // 清空service，并且切换到service tab
      setSelectedServices([]);
      setState({ activeKey: tabs[0]?.key });
    };
  }, [serviceItemType, petIds.length]);

  return (
    <div className={cn('moe-relative moe-mt-m moe-flex-1', className)}>
      <Tabs
        classNames={{
          base: 'moe-h-full',
          panel: 'moe-flex-1',
        }}
        selectedKey={String(activeKey)}
        onChange={(activeKey) => setState({ activeKey: Number(activeKey) })}
      >
        {tabs.map(({ key: serviceType, label, tooltip, isSingleSelect }) => {
          const { isEmpty, lastActiveIds, list } = getRenderListInfo(serviceType);
          const itemType: ApplicableCategoryProps['itemType'] = isSingleSelect ? 'Radio' : 'Checkbox';

          return (
            <Tabs.Item
              key={serviceType}
              label={label}
              isDisabled={Boolean(tooltip)}
              tooltip={tooltip ? tooltip : undefined}
              data-testid={
                serviceType === ServiceType.ADDON
                  ? ADCommonTestIds.ApptPetServiceAddOnsTabBtn
                  : ADCommonTestIds.ApptPetServiceServicesTabBtn
              }
            >
              <Condition if={!loading && isEmpty}>
                <ApplicableServicesEmpty serviceItemType={serviceItemType} serviceType={serviceType} />
              </Condition>
              <Spin isLoading={loading} className="moe-h-[100px]">
                <div className="moe-flex moe-flex-col moe-gap-m">
                  <ApplicableGroupWrapper type={itemType} value={String(currentMainServiceId)}>
                    <Condition if={lastActiveIds.length > 0 && (petIds?.length ?? 0) <= 1}>
                      <ApplicableCategory
                        key={-1}
                        itemType={itemType}
                        title="Last appointment"
                        petIds={petIds}
                        serviceIds={lastActiveIds}
                        disabled={getDisabled}
                        checked={getChecked}
                        onToggleService={(id, checked) => onToggleService(id, serviceType, checked)}
                      />
                    </Condition>

                    {list.map((category) => {
                      const { categoryId, categoryName, services } = category;
                      const serviceIds = services.map((i) => i.serviceId);

                      return (
                        <ApplicableCategory
                          petIds={petIds}
                          key={categoryId}
                          itemType={itemType}
                          title={categoryName}
                          serviceIds={serviceIds}
                          disabled={getDisabled}
                          checked={getChecked}
                          onToggleService={(id, checked) => onToggleService(id, serviceType, checked)}
                        />
                      );
                    })}
                  </ApplicableGroupWrapper>
                </div>
              </Spin>
            </Tabs.Item>
          );
        })}
      </Tabs>
      {/* 这里需要放到 tabs 组件下面，这样不会被盖住，不破坏布局的情况下，先这样处理 */}
      <div className="moe-absolute moe-right-0 moe-top-xs">
        <Checkbox
          isSelected={onlyAvailable.value}
          onChange={onlyAvailable.as}
          data-testid={ApptTestIds.ApptPetServiceOnlyApplicableServiceBtn}
        >
          Only show applicable {ServiceTypeOptionsEnum.mapLabels[activeKey]?.toLowerCase()}
        </Checkbox>
      </div>
    </div>
  );
});

ServiceApplicablePicker.displayName = 'ServiceApplicablePicker';
