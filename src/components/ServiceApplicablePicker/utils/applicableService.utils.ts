import { curry } from 'lodash';
import { CategoryItem, GetApplicablePetServiceResultMulti, MixedService } from '../types/applicableService';
import { ServiceType } from '../../../store/service/category.boxes';
import { EnumValues } from '@moego/finance-utils';

export const reduceByCategory = (acc: CategoryItem[], cur: MixedService) => {
  const { categoryId, categoryName } = cur;
  const hasCategory = acc.find((item) => item.categoryId === categoryId);
  if (hasCategory) {
    hasCategory.services.push(cur);
  } else {
    acc.push({
      categoryId,
      categoryName,
      services: [cur],
    });
  }
  return acc;
};

const mapApplicable = curry((applicable: boolean, item: CategoryItem) => {
  const nextItem: CategoryItem = {
    ...item,
    services: item.services.filter((i) => (applicable ? i.applicable : true)),
  };
  return nextItem;
});

const filterLessOneServices = (category: CategoryItem) => category.services.length > 0;

export function groupByCategory(value: GetApplicablePetServiceResultMulti) {
  const list = value?.categoryList || [];
  const categories = list.map((item) => {
    const { id, type, categoryId, categoryName } = item;

    return {
      categoryId: Number(categoryId),
      categoryName,
      serviceId: Number(id),
      serviceType: type,
      applicable: true,
      service: item,
    } as MixedService;
  });
  const serviceCategories = categories
    .filter((i) => i.serviceType === ServiceType.Service)
    .reduce(reduceByCategory, [] as CategoryItem[]);
  const addonCategories = categories
    .filter((i) => i.serviceType === ServiceType.Addon)
    .reduce(reduceByCategory, [] as CategoryItem[]);

  return {
    serviceCategories,
    addonCategories,
    categories,
  };
}

export function getApplicableIdsByCategory(categories: CategoryItem[]) {
  return categories
    .map((category) => {
      return category.services.map((service) => service.serviceId);
    })
    .flat();
}

export function getApplicableByCategory(categories: CategoryItem[], applicable: boolean) {
  return categories.map(mapApplicable(applicable)).filter(filterLessOneServices);
}

export function getApplicableIdsByValue(
  value: GetApplicablePetServiceResultMulti,
  serviceType: EnumValues<typeof ServiceType>,
  applicable: boolean,
) {
  const { serviceCategories, addonCategories } = groupByCategory(value);
  if (serviceType === ServiceType.Service) {
    return getApplicableIdsByCategory(getApplicableByCategory(serviceCategories, applicable));
  }
  return getApplicableIdsByCategory(getApplicableByCategory(addonCategories, applicable));
}
