import { type GetApplicableServiceListParams } from '@moego/api-web/moego/api/offering/v1/service_api';
import { type PartialKeys } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import { useMemo, useState } from 'react';
import { getAppointmentService } from '../../../container/Appt/store/appt.api';
import { getAllBusinessBasicServiceInfoList } from '../../../store/service/actions/public/service.actions';
import { selectBusinessServiceCategories } from '../../../store/service/category.selectors';
import { type ReturnTypeAmosAction } from '../../../types/common';
import { useCancelableCallback } from '../../../utils/hooks/useCancelableCallback';
import { MixedService } from '../types/applicableService';
import { reduceByCategory } from '../utils/applicableService.utils';

export interface UseApplicableServiceParams
  extends PartialKeys<GetApplicableServiceListParams, 'onlyAvailable' | 'inactive'> {}

export const useApplicableService = (params: UseApplicableServiceParams) => {
  const {
    serviceType,
    selectedServiceIds,
    onlyAvailable = true,
    inactive = false,
    selectedServiceItemType,
    businessId,
  } = params;
  const [applicableServiceList, setApplicableServiceList] = useState<
    Awaited<ReturnTypeAmosAction<typeof getAppointmentService>>
  >([]);
  const dispatch = useDispatch();
  const [businessServiceCategoriesList] = useSelector(selectBusinessServiceCategories(serviceType));

  const selectOptions = useMemo(() => {
    const categories = applicableServiceList.map((item) => {
      const { id, type, categoryId, categoryName, name } = item;
      return {
        categoryId: Number(categoryId),
        categoryName,
        serviceId: Number(id),
        serviceType: type,
        serviceName: name,
        applicable: true,
      } as MixedService;
    });
    const addonCategories = categories.reduce(reduceByCategory, []);

    return addonCategories.map((item) => {
      return {
        label: item.categoryName || 'UNCATEGORIZED',
        options: item.services.map((service) => ({
          label: service.serviceName!,
          value: String(service.serviceId),
        })),
      };
    });
  }, [applicableServiceList]);

  const getApplicableServiceList = useCancelableCallback(async (signal) => {
    const [res] = await Promise.all([
      dispatch(
        getAppointmentService(
          {
            serviceType,
            onlyAvailable,
            selectedServiceIds,
            businessId,
            inactive,
            selectedServiceItemType,
            petIds: [],
          },
          signal,
        ),
      ),
      !businessServiceCategoriesList.size && dispatch(getAllBusinessBasicServiceInfoList({ inactive: false })),
    ]);
    setApplicableServiceList(res);
  });

  return {
    getApplicableServiceList,
    selectOptions,
    isLoading: getApplicableServiceList.isBusy(),
    applicableServiceList,
  };
};
