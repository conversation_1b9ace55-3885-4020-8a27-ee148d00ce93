import { useSelector } from 'amos';
import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { useLatestCallback } from '@moego/finance-utils';
import { applicableServiceMapBox } from '../../../container/Appt/store/appt.boxes';
import { serviceMapBox } from '../../../store/service/service.boxes';
import { GrowthBookFeatureList } from '../../../utils/growthBook/growthBook.config';
import { getOverriddenListRange } from '../../../store/service/serviceOverride.utils';
import { isNormal } from '../../../store/utils/identifier';

export function useGetApptServicePrice() {
  const [applicableServiceMap, serviceMap] = useSelector(applicableServiceMapBox, serviceMapBox);
  const isEnableVaryPricingByZone = useFeatureIsOn(GrowthBookFeatureList.EnableVaryPricingByZone);

  const getApptServicePrice = useLatestCallback((serviceId: number) => {
    if (isEnableVaryPricingByZone) {
      return applicableServiceMap.mustGetItem(serviceId.toString()).price;
    }
    return serviceMap.mustGetItem(serviceId).price;
  });

  const getApptServiceStaffOverrideList = useLatestCallback((serviceId: number) => {
    if (isEnableVaryPricingByZone) {
      return applicableServiceMap.mustGetItem(serviceId.toString()).staffOverrideList;
    }
    return serviceMap.mustGetItem(serviceId).staffOverrideList;
  });

  const getApptServiceStaffOverrideVariation = useLatestCallback((serviceId: number, staffId: number) => {
    const applicableService = applicableServiceMap.mustGetItem(serviceId.toString());
    if (isEnableVaryPricingByZone && applicableService.staffOverrideList.length) {
      return applicableService.getStaffOverrideVariation(staffId);
    }
    return serviceMap.mustGetItem(serviceId).getStaffOverrideVariation(staffId);
  });

  const getPricingByZonesServiceOverriddenPrice = useLatestCallback((serviceId: number, staffId?: number) => {
    const specificStaffOverrideList = getApptServiceStaffOverrideList(serviceId);
    const specificPrice = getApptServicePrice(serviceId);
    const priceRange = getOverriddenListRange(specificStaffOverrideList, 'price', specificPrice);

    if (!isNormal(staffId)) {
      const { isRanged: isPriceRanged, min: minPrice, max: maxPrice } = priceRange;

      return {
        price: isPriceRanged ? [minPrice, maxPrice] : null,
      };
    }

    return {
      price: applicableServiceMap.mustGetItem(serviceId.toString()).getStaffOverrideVariation(staffId)?.price,
    };
  });

  return {
    getApptServicePrice,
    getApptServiceStaffOverrideList,
    getApptServiceStaffOverrideVariation,
    getPricingByZonesServiceOverriddenPrice,
    isEnableVaryPricingByZone,
  };
}
