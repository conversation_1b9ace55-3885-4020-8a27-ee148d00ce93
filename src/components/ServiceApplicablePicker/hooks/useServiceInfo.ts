import {
  ServiceOverrideType,
  type ServicePriceUnit,
  ServiceScopeType,
} from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useSelector } from 'amos';
import { useCallback, useMemo } from 'react';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { PetSavedPriceRecord, petSavedPriceMapBox } from '../../../store/pet/petSavedPrice.boxes';
import { serviceMapBox } from '../../../store/service/service.boxes';
import { ID_ANONYMOUS, isNormal } from '../../../store/utils/identifier';
import { withPl } from '../../../utils/calculator';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { type UseServiceInfoOptions } from '../types/options';
import { useServiceVariation } from './useServiceVariationByStaff';
import { useGetApptServicePrice } from './useGetApptServicePrice';

export interface ServicePriceDurationInfo {
  servicePrice: number;
  serviceTime: number;
  scopeTypePrice: ServiceScopeType;
  scopeTypeTime: ServiceScopeType;
  priceOverrideType: ServiceOverrideType;
  durationOverrideType: ServiceOverrideType;
  priceUnit: ServicePriceUnit;
}

export interface GetServicePriceDurationInfoParams {
  petId: number;
  serviceId: number;
  staffId?: number;
}

const { CLIENT, STAFF, UNSPECIFIED } = ServiceOverrideType;

export function useImperativeServicePriceDurationInfo() {
  const [serviceMap] = useSelector(serviceMapBox);
  const getSaveInfo = useSavedInfo();
  const { getApptServiceStaffOverrideVariation, getApptServicePrice } = useGetApptServicePrice();

  const getServicePriceDurationInfo = useLatestCallback(
    ({ petId, serviceId, staffId = ID_ANONYMOUS }: GetServicePriceDurationInfoParams): ServicePriceDurationInfo => {
      const { isSavedDuration, isSavedPrice, savedService } = getSaveInfo(petId, serviceId);
      const service = serviceMap.mustGetItem(serviceId);
      const serviceStaffVariation = getApptServiceStaffOverrideVariation(serviceId, staffId);
      const servicePrice = getApptServicePrice(serviceId);
      return {
        servicePrice: isSavedPrice ? savedService.price : (serviceStaffVariation?.price ?? servicePrice),
        serviceTime: isSavedDuration ? savedService.duration : (serviceStaffVariation?.duration ?? service.duration),
        priceOverrideType: isSavedPrice ? CLIENT : serviceStaffVariation?.price ? STAFF : UNSPECIFIED,
        durationOverrideType: isSavedDuration ? CLIENT : serviceStaffVariation?.duration ? STAFF : UNSPECIFIED,
        scopeTypePrice: isSavedPrice ? ServiceScopeType.ONLY_THIS : ServiceScopeType.DO_NOT_SAVE,
        scopeTypeTime: isSavedDuration ? ServiceScopeType.ONLY_THIS : ServiceScopeType.DO_NOT_SAVE,
        priceUnit: service.priceUnit,
      };
    },
  );

  return getServicePriceDurationInfo;
}

export function useServiceInfo(options: UseServiceInfoOptions) {
  const { serviceId, staffId, isDisableStaffOverride, petIds } = options;

  const [business, service] = useSelector(selectCurrentBusiness, serviceMapBox.mustGetItem(serviceId));
  const getSaveInfo = useSavedInfo();
  const { getApptServicePrice, getPricingByZonesServiceOverriddenPrice, isEnableVaryPricingByZone } =
    useGetApptServicePrice();
  const servicePrice = getApptServicePrice(serviceId);
  const { price: pricingByZonesStaffOverridePrice } = getPricingByZonesServiceOverriddenPrice(serviceId, staffId);

  const { isSavedDuration, isSavedPrice, savedService } = useMemo(() => {
    if (petIds.length) {
      const result = petIds.map((id) => getSaveInfo(Number(id), serviceId));
      const resultIndex = result.findIndex((i) => i.isSavedDuration || i.isSavedPrice);

      return {
        isSavedDuration: result.some((i) => i.isSavedDuration),
        isSavedPrice: result.some((i) => i.isSavedPrice),
        savedService: result[resultIndex]?.savedService,
      };
    }
    return {} as ReturnType<typeof getSaveInfo>;
  }, [serviceId, getSaveInfo, petIds.length]);

  const {
    price: serviceOverriddenPrice,
    duration: serviceOverriddenDuration,
    hasStaffPrice,
    hasStaffDuration,
  } = useServiceVariation({
    serviceId,
    staffId,
  });
  const specificStaffOverridePrice = isEnableVaryPricingByZone
    ? pricingByZonesStaffOverridePrice
    : serviceOverriddenPrice;

  const staffOverridePrice = !isDisableStaffOverride ? specificStaffOverridePrice : null;
  const staffOverrideDuration = !isDisableStaffOverride ? serviceOverriddenDuration : null;

  const price = isSavedPrice ? savedService.price : (staffOverridePrice ?? servicePrice);
  const duration = isSavedDuration ? savedService.duration : (staffOverrideDuration ?? service.duration);
  const maxDuration = service.maxDuration;

  return {
    service,
    serviceName: service.name,
    formattedPrice: Array.isArray(price)
      ? `${business.formatAmount(price[0])}-${business.formatAmount(price[1])}`
      : business.formatAmount(price),
    formattedDuration: Array.isArray(duration)
      ? `${duration[0]}-${withPl(duration[1], 'min')}`
      : withPl(duration, 'min'),
    formattedMaxDuration: maxDuration ? withPl(Math.floor(maxDuration / 60), 'hour') : '',

    priceOverrideType: isSavedPrice ? CLIENT : hasStaffPrice ? STAFF : UNSPECIFIED,

    durationOverrideType: isSavedDuration ? CLIENT : hasStaffDuration ? STAFF : UNSPECIFIED,
  };
}

export const useSavedInfo = () => {
  const [petSavedServiceMap] = useSelector(petSavedPriceMapBox);

  return useCallback(
    (petId: number, serviceId: number) => {
      const savedService = petSavedServiceMap.mustGetItem(PetSavedPriceRecord.ownId(petId, serviceId));
      const isSavedService = isNormal(savedService.serviceId);
      const isSavedPrice = isSavedService && !!savedService.isSavePrice;
      const isSavedDuration = isSavedService && !!savedService.isSaveDuration;

      return {
        isSavedDuration,
        isSavedPrice,
        savedService,
      };
    },
    [petSavedServiceMap],
  );
};
