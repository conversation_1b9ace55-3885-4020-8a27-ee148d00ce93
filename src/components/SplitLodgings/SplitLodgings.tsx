import { MinorErrorFilled, MinorPlusOutlined } from '@moego/icons-react';
import { Button, Text, cn, type useForm, useFormState, useWatch } from '@moego/ui';
import dayjs, { isDayjs } from 'dayjs';
import { isNil, isUndefined } from 'lodash';
import React, { Fragment, memo, useMemo } from 'react';
import { useUpdateEffect } from 'react-use';
import { ADPetsServicesTestIds } from '../../config/testIds/apptDrawer';
import { DATE_FORMAT_EXCHANGE } from '../../utils/DateTimeUtil';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';
import { ReportActionName } from '../../utils/reportType';
import { reportData } from '../../utils/tracker';
import { Condition } from '../Condition';
import { SplitLodgingItem } from './components/SplitLodgingItem';
import { useFormatServiceSplitLodgings } from './hooks/useFormatServiceSplitLodgings';
import { useGetDisableRange } from './hooks/useGetDisableRange';
import { useGetIsApplicableLodging } from './hooks/useGetIsApplicableLodging';
import { useSyncSplitLodgingsToFormValue } from './hooks/useSyncSplitLodgingsToFormValue';
import { useUpdateSplitLodgings } from './hooks/useUpdateSplitLodgings';
import {
  type ISplitLodgingItemChangePayload,
  SplitLodgingError,
  type SplitLodgingItemValue,
  type SplitLodgingServiceRange,
} from './utils/SplitLodgings.types';
import { syncServiceDateTimeUpdate } from './utils/SplitLodgings.utils';
import { useSelector } from 'amos';
import { selectService } from '../../store/service/service.selectors';

export interface SplitLodgingsProps {
  form?: ReturnType<typeof useForm>;
  formKey?: string;
  range: SplitLodgingServiceRange;
  petId: number | string;
  serviceId: string | number;
  isDisabled?: boolean;
  onChange: (value: SplitLodgingItemValue[]) => void;
}

export const SplitLodgings = memo<SplitLodgingsProps>((props) => {
  const { range, petId, serviceId, isDisabled, form, formKey, onChange } = props;
  const formPrefix = formKey ? `${formKey}.` : '';
  const [service] = useSelector(selectService(Number(serviceId)));
  const getDisabledRange = useGetDisableRange(range);
  const formatServiceSplitLodgings = useFormatServiceSplitLodgings();
  const syncSplitLodgingsToFormValue = useSyncSplitLodgingsToFormValue(form, formPrefix);
  const [splitLodgings = [], lodgingId, lodgingName] = useWatch({
    control: form?.control,
    name: [`${formPrefix}splitLodgings`, `${formPrefix}lodgingId`, `${formPrefix}lodgingName`],
  });
  const value = formatServiceSplitLodgings({
    lodgingId,
    lodgingName,
    servicePrice: service.price,
    splitLodgings: (splitLodgings ?? []).map((item: any) => ({
      ...item,
      endDate: isNil(item.endDate) ? null : dayjs(item.endDate).format(DATE_FORMAT_EXCHANGE),
      endTime: isDayjs(item.endTime) ? item.endTime.getMinutes() : isUndefined(item.endTime) ? null : item.endTime,
      startDate: isNil(item.startDate) ? null : dayjs(item.startDate).format(DATE_FORMAT_EXCHANGE),
      startTime: isDayjs(item.startTime)
        ? item.startTime.getMinutes()
        : isUndefined(item.startTime)
          ? null
          : item.startTime,
    })),
  });

  const getIsApplicableLodging = useGetIsApplicableLodging({
    petId: Number(petId),
    serviceId: Number(serviceId),
  });
  const { addSplitLodging, deleteSplitLodging, changeSplitLodging } = useUpdateSplitLodgings({
    serviceRange: range,
    serviceId: Number(serviceId),
  });
  const { errors } = useFormState({ control: form?.control });
  const splitLodgingError = errors[SplitLodgingError];

  const isDisableAddMore = useMemo(() => {
    if (value.length <= 1) {
      return false;
    }
    const lastLodging = value[value.length - 1];
    if (!isNil(lastLodging?.endDate)) {
      return dayjs(lastLodging.endDate).isSame(dayjs(range.endDate), 'day');
    }
    return false;
  }, [value, range]);

  const isServiceTimeLessThanOrEqualToOneDay = useMemo(() => {
    if (!range.startDate || !range.endDate) {
      return false;
    }
    const startDate = dayjs(range.startDate);
    const endDate = dayjs(range.endDate);
    const diffDays = endDate.diff(startDate, 'day');
    return diffDays <= 1;
  }, [range.startDate, range.endDate]);

  const changeAndClearFormError = useLatestCallback((nextValue: SplitLodgingItemValue[]) => {
    form?.clearErrors(SplitLodgingError);
    onChange?.(nextValue);
  });

  const addHandler = useLatestCallback(() => {
    const newValue = addSplitLodging(value);
    changeAndClearFormError(newValue);
    reportData(ReportActionName.addSplitLodgingClick);
  });

  const changeHandler = useLatestCallback(async (payload: ISplitLodgingItemChangePayload) => {
    const newValue = await changeSplitLodging(value, payload, range);
    changeAndClearFormError(newValue);
  });

  const deleteHandler = useLatestCallback((index: number) => {
    const newValue = deleteSplitLodging(value, index);
    changeAndClearFormError(newValue);
  });

  useUpdateEffect(() => {
    if (
      !isNil(range.startDate) &&
      !isNil(range.endDate) &&
      !isUndefined(range.startTime) &&
      !isUndefined(range.endTime)
    ) {
      const newValue = syncServiceDateTimeUpdate(value, range as Required<SplitLodgingServiceRange>);
      changeAndClearFormError(newValue);
      syncSplitLodgingsToFormValue(newValue);
    }
  }, [range.startDate, range.startTime, range.endDate, range.endTime]);
  return (
    <Fragment>
      <div
        className={cn(
          'moe-bg-neutral-sunken-0 moe-rounded-8px-100',
          'moe-w-full moe-flex moe-flex-col moe-items-start moe-gap-y-8px-200 moe-p-8px-200',
        )}
      >
        <div className="moe-w-full">
          {value.length > 0 &&
            value.map((item, index) => (
              <SplitLodgingItem
                value={item}
                index={index}
                form={form}
                formPrefix={formPrefix}
                total={value.length}
                petId={props.petId}
                serviceId={props.serviceId}
                isDisabled={isDisabled}
                key={[index, item.lodgingId].join('-')}
                serviceRange={range}
                onChange={changeHandler}
                onDelete={deleteHandler}
                getDisabledRange={(date) => getDisabledRange({ index, items: value, date })}
                getIsApplicableLodging={getIsApplicableLodging}
              />
            ))}
        </div>
        <Condition if={!isDisableAddMore && !isServiceTimeLessThanOrEqualToOneDay}>
          <Button
            size="s"
            variant="tertiary"
            onPress={addHandler}
            isDisabled={isDisabled}
            icon={<MinorPlusOutlined />}
            data-testid={ADPetsServicesTestIds.AddNewSplitLodgingBtn}
          >
            Add another lodging
          </Button>
        </Condition>
      </div>
      <Condition if={splitLodgingError}>
        <Text variant="small" as="div" className="moe-flex moe-items-center moe-text-danger">
          <MinorErrorFilled className="moe-mr-xxs" />
          {splitLodgingError?.message}
        </Text>
      </Condition>
    </Fragment>
  );
});
