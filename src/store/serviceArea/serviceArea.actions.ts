import { action } from 'amos';
import { cloneDeep } from 'lodash';
import { ServiceAreaType } from '../../components/ServiceArea/ServiceArea.options';
import { toastApi } from '../../components/Toast/Toast';
import { http } from '../../middleware/api';
import { type OpenApiDefinitions, type OpenApiModels } from '../../openApi/schema';
import { latLngObjToPair, latLngPairToObj } from '../../utils/geo';
import { selectBusiness } from '../business/business.selectors';
import {
  type ServiceAreaModel,
  businessServiceAreaListBox,
  nearbyCustomerListBox,
  nearbyCustomerMapBox,
  serviceAreaMapBox,
} from './serviceArea.boxes';

export const getBusinessServiceArea = action(async (dispatch, select, businessId: number) => {
  const business = select(selectBusiness(businessId));
  const isMobileGrooming = business.isMobileGrooming();

  if (!isMobileGrooming) return;

  const res = await http.open('GET/business/staff/v2/certainArea/business', {
    businessId: `${businessId}`,
  });
  const list = res.result.map((item) => {
    return {
      ...item,
      serviceAreaType: Number(item.serviceAreaType),
      polygon: item.polygon.map(latLngPairToObj),
    };
  });
  dispatch([
    serviceAreaMapBox.mergeItems(list),
    businessServiceAreaListBox.setList(
      businessId,
      list.map((area) => area.areaId),
    ),
  ]);
  return list;
});

export const addServiceArea = action(
  async (dispatch, _select, input: Partial<ServiceAreaModel>, businessId: number) => {
    const { polygon, ...rest } = input;
    const polygonParams = polygon && { polygon: polygon.map(latLngObjToPair) };

    const r = await http.open('PUT/business/staff/v2/certainArea', {
      businessId: `${businessId}`,
      certainAreas: [
        { ...rest, ...polygonParams },
      ] as unknown as OpenApiDefinitions['business']['com.moego.server.business.web.vo.CertainArea'][],
    });

    const result = r.result[0];
    !result.success && toastApi.error(result.message);

    result.success &&
      dispatch([
        serviceAreaMapBox.mergeItems([
          {
            ...rest,
            polygon: cloneDeep(polygon),
            areaId: result.areaId,
          },
        ]),
        businessServiceAreaListBox.pushList(businessId, result.areaId),
      ]);

    return result;
  },
);

export const deleteServiceArea = action(async (dispatch, _select, areaId: number, businessId: number) => {
  const res = await http.open('DELETE/business/staff/certainArea', { areaId, businessId: `${businessId}` });
  dispatch(businessServiceAreaListBox.deleteItem(businessId, areaId));
  dispatch(serviceAreaMapBox.deleteItem(areaId));
  return res;
});

export const updateServiceArea = action(async (dispatch, _select, input: ServiceAreaModel, businessId: number) => {
  const { areaId, polygon, serviceAreaType, zipcodes, ...rest } = input;
  const isPolygonType = serviceAreaType === ServiceAreaType.Polygon;

  const extraParams = isPolygonType ? { polygon: polygon.map(latLngObjToPair) } : { zipcodes: zipcodes };
  const resetParams = isPolygonType ? { zipcodes: [] } : { polygon: [] };

  const res = await http.open('PUT/business/staff/v2/certainArea', {
    businessId: `${businessId}`,
    certainAreas: [
      { serviceAreaType, areaId, ...rest, ...extraParams, ...resetParams },
    ] as unknown as OpenApiDefinitions['business']['com.moego.server.business.web.vo.CertainArea'][],
  });

  const result = res.result[0];
  !result.success && toastApi.error(result.message);
  result.success && dispatch(serviceAreaMapBox.mergeItem(areaId!, cloneDeep({ ...input, ...resetParams })));

  return result;
});

export type GetCustomersNearbyReqParamsModel = OpenApiModels['POST/customer/nearby']['Req'];
export const getNearbyCustomerList = action(
  async (dispatch, _select, input: GetCustomersNearbyReqParamsModel, businessId: number) => {
    const r = await http.open('POST/customer/nearby', input);
    dispatch([
      nearbyCustomerMapBox.mergeItems(r.data),
      nearbyCustomerListBox.setList(
        businessId,
        r.data.map((c) => c.customerId),
      ),
    ]);
  },
);

export const getPlaceIdByZipcodeAction = action(
  async (_dispatch, _select, input: { zipCodes: string }, signal?: AbortSignal) => {
    // @ts-expect-error effect in fact
    return await http.open('GET/grooming/bookOnline/zipcode/details', { zipCodes: input.zipCodes }, { signal });
  },
);

export const getZipcodeList = action(async (_dispatch, _select, input: { prefix: string }) => {
  return await http.open('GET/grooming/bookOnline/zipcode/search', { prefix: input.prefix });
});
