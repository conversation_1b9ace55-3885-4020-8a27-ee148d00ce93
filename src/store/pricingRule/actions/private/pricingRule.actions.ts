import {
  type CalculatePricingRuleParams,
  type CheckConfigurationParams,
  type DeletePricingRuleParams,
  type GetPricingRuleParams,
  type ListAssociatedServicesParams,
  type ListPricingRulesParams,
  type PreviewPricingRuleParams,
  type UpsertPricingRuleParams,
} from '@moego/api-web/moego/api/offering/v2/pricing_rule_api';
import { type DiscountSettingDef } from '@moego/api-web/moego/models/offering/v2/pricing_rule_defs';
import { action } from 'amos';
import { PricingRuleServiceV2Client } from '../../../../middleware/clients';
import { currentCompanyIdBox } from '../../../company/company.boxes';
import {
  PricingRuleAssociatedServices,
  companyPricingDiscountSettingMapBox,
  companyPricingRuleListBox,
  companyPricingRuleMapBox,
  pricingRuleAssociatedServicesRecordMap,
  pricingRuleOverviewBox,
} from '../../pricingRule.boxes';
import { RequestParams } from '@moego/http-client';
import { AlertDialog } from '@moego/ui';

export const getPricingDiscountSetting = action(async (dispatch, select) => {
  const companyId = select(currentCompanyIdBox);
  const result = await PricingRuleServiceV2Client.getDiscountSetting({});
  dispatch(companyPricingDiscountSettingMapBox.mergeItem(companyId, result.setting));
  return result;
});

export const updatePricingDiscountSetting = action(async (dispatch, select, setting: DiscountSettingDef) => {
  const companyId = select(currentCompanyIdBox);
  const result = await PricingRuleServiceV2Client.updateDiscountSetting({ setting });
  dispatch(companyPricingDiscountSettingMapBox.mergeItem(companyId, result.setting));
  return result;
});

export const calculatePricingRule = action(async (dispatch, select, params: CalculatePricingRuleParams) => {
  const result = await PricingRuleServiceV2Client.calculatePricingRule(params);
  return result;
});

export const previewPricingRule = action(async (dispatch, select, params: PreviewPricingRuleParams) => {
  const result = await PricingRuleServiceV2Client.previewPricingRule(params);
  return result;
});

export const listPricingRules = action(async (dispatch, select, params: ListPricingRulesParams) => {
  const companyId = select(currentCompanyIdBox);
  const state = select(companyPricingRuleListBox.mustGetItem(companyId));
  const filter = state.getFilter(params);
  dispatch(companyPricingRuleListBox.updateItem(companyId, (v) => v.applyStart(params)));
  try {
    const result = await PricingRuleServiceV2Client.listPricingRules(params);
    dispatch(companyPricingRuleMapBox.mergeItems(result.pricingRules));
    dispatch(
      companyPricingRuleListBox.updateItem(companyId, (v) => {
        return v.applySuccess(
          result.pricingRules.map((item) => item.id),
          result.pagination.total,
          filter.pagination.pageNum ?? 1,
          filter.clear,
        );
      }),
    );

    return result;
  } catch {
    dispatch(
      companyPricingRuleListBox.updateItem(companyId, (v) => {
        return v.applyFail(params.pagination.pageNum ?? 1);
      }),
    );
  }
});

export const listAssociatedServices = action(async (dispatch, select, input: ListAssociatedServicesParams) => {
  const companyId = select(currentCompanyIdBox);
  const result = await PricingRuleServiceV2Client.listAssociatedServices(input);
  const ownKey = PricingRuleAssociatedServices.ownKey(companyId, input.excludePricingRuleId);
  dispatch(
    pricingRuleAssociatedServicesRecordMap.mergeItem(ownKey, {
      ...result,
      key: ownKey,
    }),
  );
  return result;
});

export const checkConfiguration = action(async (dispatch, select, input: CheckConfigurationParams) => {
  const { isValid, errorMessage } = await PricingRuleServiceV2Client.checkConfiguration(input);
  if (errorMessage) {
    AlertDialog.open({
      title: 'Rule cannot be activated',
      content: errorMessage,
      showCancelButton: false,
      confirmText: 'Got it',
      className: 'moe-w-[540px]',
    });
  }
  return { isValid };
});

export const upsertPricingRule = action(
  async (dispatch, select, input: UpsertPricingRuleParams, requestParams?: Partial<RequestParams>) => {
    const { pricingRule } = await PricingRuleServiceV2Client.upsertPricingRule(input, requestParams);
    return pricingRule;
  },
);

export const getPricingRule = action(async (dispatch, select, input: GetPricingRuleParams) => {
  const result = await PricingRuleServiceV2Client.getPricingRule(input);
  dispatch(companyPricingRuleMapBox.mergeItem(input.id, result.pricingRule));
  return result;
});

export const deletePricingRule = action(async (dispatch, select, input: DeletePricingRuleParams) => {
  const result = await PricingRuleServiceV2Client.deletePricingRule(input);
  const companyId = select(currentCompanyIdBox);
  dispatch([
    companyPricingRuleListBox.updateItem(companyId, (v) => {
      return v.deleteItem(input.id);
    }),
    companyPricingRuleMapBox.deleteItem(input.id),
  ]);
  return result;
});

export const getPricingRuleOverview = action(async (dispatch, select) => {
  const companyId = select(currentCompanyIdBox);
  const result = await PricingRuleServiceV2Client.getPricingRuleOverview({});
  dispatch(pricingRuleOverviewBox.mergeItem(companyId, { ...result, companyId: Number(companyId) }));
  return result;
});
