import { type GetApplicableServiceListParams } from '@moego/api-web/moego/api/offering/v1/service_api';
import { ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { action } from 'amos';
import { getAppointmentService } from '../../container/Appt/store/appt.api';
import { http } from '../../middleware/api';
import { currentBusinessIdBox } from '../business/business.boxes';
import { type PetServiceWithCategory } from '../createTicket/createTicket.types';
import { petLastAddOnsServiceListBox, petLastMainServiceListBox, petLastServicesMapBox } from './petServices.boxes';

export const getPetLastServices = action(async (dispatch, select, petId: number) => {
  const r = await http.open('GET/grooming/pet/last/service', { petId });
  const { lastAddOnsIds, lastServiceIds } = r.data;
  dispatch([
    petLastMainServiceListBox.setList(petId, lastServiceIds),
    petLastAddOnsServiceListBox.setList(petId, lastAddOnsIds),
    petLastServicesMapBox.mergeItems(
      r.data.serviceList.map(({ serviceId, servicePrice, serviceTime }) => {
        return {
          serviceId,
          duration: serviceTime,
          price: servicePrice,
        };
      }),
    ),
  ]);
});

export interface GetApplicableGroomingServicesAddonsParams {
  petId: number;
  clientId?: number;
}

// 代替原来的 grooming/applicable/sorted/service 接口，由于getAppointmentService不支持查多种serviceType 和 区分available，需要查询多次
// 没有让后端在getAppointmentService 中支持是因为 advanced edit 后续会下线，这里以最小成本修改即可
export const getPetApplicableServicesAddons = action(
  async (dispatch, select, input: GetApplicableGroomingServicesAddonsParams) => {
    const { petId, clientId } = input;
    const businessId = select(currentBusinessIdBox).toString();
    const params: Omit<GetApplicableServiceListParams, 'serviceType' | 'onlyAvailable'> = {
      businessId,
      petId: petId?.toString(),
      inactive: false,
      serviceItemType: ServiceItemType.GROOMING,
      selectedServiceIds: [],
      petIds: [],
      ...(clientId && { customerId: String(clientId) }),
    };
    const [onlyAvailableServices, services, onlyAvailableAddons, addons] = await Promise.all([
      dispatch(getAppointmentService({ ...params, serviceType: ServiceType.SERVICE, onlyAvailable: true })),
      dispatch(getAppointmentService({ ...params, serviceType: ServiceType.SERVICE, onlyAvailable: false })),
      dispatch(getAppointmentService({ ...params, serviceType: ServiceType.ADDON, onlyAvailable: true })),
      dispatch(getAppointmentService({ ...params, serviceType: ServiceType.ADDON, onlyAvailable: false })),
    ]);

    const createAvailabilityMap = (items: typeof services) => {
      return new Set(items.map((item) => item.id));
    };

    const mapItems = (items: typeof services, availableSet: Set<string>): PetServiceWithCategory[] => {
      return items.map((item) => ({
        ...item,
        id: Number(item.id),
        isAvailable: availableSet.has(item.id),
        categoryId: Number(item.categoryId),
      }));
    };

    const availableServicesSet = createAvailabilityMap(onlyAvailableServices);
    const availableAddonsSet = createAvailabilityMap(onlyAvailableAddons);

    const servicesList = mapItems(services, availableServicesSet);
    const addonsList = mapItems(addons, availableAddonsSet);

    const categoryServiceAddonList = [servicesList, addonsList].flat();
    return categoryServiceAddonList;
  },
);
