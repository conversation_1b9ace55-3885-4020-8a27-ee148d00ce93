import {
  type GetApplicableServiceListParams,
  type ListServicesParams,
} from '@moego/api-web/moego/api/offering/v1/service_api';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { type Mutation, action } from 'amos';
import { isNil } from 'lodash';
import { http } from '../../../../middleware/api';
import { ServiceManagementClient } from '../../../../middleware/clients';
import { type OpenApiModels } from '../../../../openApi/schema';
import { currentAccountIdBox } from '../../../account/account.boxes';
import { currentBusinessIdBox } from '../../../business/business.boxes';
import { createServiceObservableAction } from '../../../observableServices/observableServices';
import { type PartialRequired } from '../../../utils/RecordMap';
import { type EnumValues } from '../../../utils/createEnum';
import { isNormal } from '../../../utils/identifier';
import { selectSceneCareType } from '../../../careType/careType.selectors';
import {
  AddonItemType,
  ServiceCategoryRecord,
  ServiceType,
  businessServiceCategoryListBox,
  serviceCategoryMapBox,
} from '../../category.boxes';
import { Scene } from '../../scene.enum';
import {
  ServiceActive,
  ServiceRecord,
  categoryServiceInactiveListBox,
  categoryServiceListBox,
  serviceMapBox,
} from '../../service.boxes';
import {
  mergeCompanyServiceActions,
  transformBusinessCategoryService,
  transformCategoryService,
} from '../../service.utils';
import {
  type GetAllBusinessServiceInfoParams,
  type GetAllCompanyServiceInfoParams,
  type GetCompanyServiceInfoParams,
} from '../private/service.actions';
import { applicableServiceMapBox } from '../../../../container/Appt/store/appt.boxes';

/**
 * @deprecated 创建一个 service，目前只在 advanced edit 页面里用到，后续应该会被废弃
 * 如果是新的 service 添加，请使用 createService
 */

export const deprecatedAddService = action(
  async (dispatch, select, input: OpenApiModels['POST/grooming/company/service']['Req'], businessId: number) => {
    let newService: typeof input & { serviceId?: number } = input;

    // 这个分支，目前只会在 advanced edit 里用到，主要是旧组件但是需要支持新接口
    // 这里跟 mobile 上的 service 添加逻辑是一致的
    // 添加的 service 默认是 all location 的
    const r = await http.open('POST/grooming/company/service', {
      ...input,
      price: isNil(input.price) ? 0 : input.price,
      isAllLocation: 1,
      singleLocationId: businessId,
    });
    newService = { ...input, ...r };

    if (newService.serviceId) {
      dispatch([
        serviceMapBox.setItem(newService.serviceId, newService),
        categoryServiceListBox.unshiftList(
          ServiceRecord.ownKey(businessId, `${input.type}-${ServiceItemType.GROOMING}`, input.categoryId),
          newService.serviceId,
        ),
      ]);
    }

    return newService as typeof input & { serviceId: number };
  },
); /**
 * 传入 serviceId list, 返回传入的 service id 对应的 biz 级别的 service 基本信息
 * 只在极少数场景使用
 */

export const getServiceDetailList = action(async (_, __, input: OpenApiModels['POST/grooming/service/info']['Req']) => {
  const res = await http.open('POST/grooming/service/info', input);
  return res;
});

//---------- 下面基本上是获取 service list 的 action，一般的消费场景关注底下的 actions 即可 ----------
/**
 * 获取当前 business 级别基本的 service list
 * 不区分 service item type，传 service type 就可以获取 grooming、boarding、daycare 或者 addon 的 service list
 */

export const getBusinessBasicServiceInfoList = action(
  async (
    dispatch,
    select,
    input: { serviceType: EnumValues<typeof ServiceType>; inactive?: boolean },
    signal?: AbortSignal,
  ) => {
    const businessId = select(currentBusinessIdBox);
    const { serviceType, inactive = false } = input;
    const res = await ServiceManagementClient.getApplicableServiceList(
      {
        businessId: String(businessId),
        serviceType,
        inactive,
        onlyAvailable: false,
        selectedServiceIds: [],
        petIds: [],
      },
      { signal },
    );

    const actions: Mutation<any>[] = [];

    const categoryList = res.categoryList.map((item) => transformBusinessCategoryService(item));

    categoryList.forEach((c) => {
      const { categoryId } = c;

      if (isNormal(categoryId)) {
        actions.push(serviceCategoryMapBox.mergeItem(categoryId, c));
      }

      c.services.forEach((s) => {
        const { serviceItemType, inactive } = s;
        const type = `${serviceType}-${serviceItemType}`;

        if (isNormal(categoryId)) {
          actions.push(
            businessServiceCategoryListBox.pushList(ServiceCategoryRecord.ownKey(businessId, type), categoryId),
          );
        }

        if (inactive) {
          actions.push(
            categoryServiceInactiveListBox.pushList(
              ServiceRecord.ownKey(businessId, type, ServiceActive.Inactive),
              s.serviceId,
            ),
          );
        } else {
          actions.push(
            categoryServiceListBox.pushList(ServiceRecord.ownKey(businessId, type, categoryId), s.serviceId),
          );
        }

        actions.push(
          serviceMapBox.mergeItem(s.serviceId, {
            ...s,
            inactive: s.inactive ? ServiceActive.Inactive : ServiceActive.Active,
            serviceId: s.serviceId,
            categoryId: s.categoryId,
            taxId: s.taxId,
          }),
        );
      });
    });
    actions.push(applicableServiceMapBox.mergeItems(res.categoryList.flatMap((c) => c.services)));

    dispatch(actions);

    return categoryList;
  },
);
/** 获取所有 biz 级别的基本 service 信息，包括 active 和 inactive 的 */

export const getAllBusinessBasicServiceInfoList = createServiceObservableAction(
  'getAllBusinessBasicServiceInfoList',
  async (dispatch, _, input?: GetAllBusinessServiceInfoParams, signal?: AbortSignal) => {
    await Promise.all([
      dispatch(
        getBusinessBasicServiceInfoList(
          {
            serviceType: ServiceType.Service,
            inactive: input?.inactive,
          },
          signal,
        ),
      ),
      dispatch(
        getBusinessBasicServiceInfoList(
          {
            serviceType: ServiceType.Addon,
            inactive: input?.inactive,
          },
          signal,
        ),
      ),
    ]);
  },
);
/**
 * 获取当前 company 级别全部的 service list（grooming\boarding\daycare）
 *
 * 包含所有 service 相关信息
 *
 * 推荐只在 settings 页面使用这个接口，其他场景推荐使用 getAllCompanyBasicServiceInfoList
 *
 * @param withAddon 是否获取 addon 的信息
 *
 */

export const getAllCompanyFullServiceInfoList = action(
  async (
    dispatch,
    select,
    { inactive = !!ServiceActive.Active, withAddon }: GetAllCompanyServiceInfoParams,
    signal?: AbortSignal,
  ) => {
    const availableCareTypes = select(selectSceneCareType(Scene.EnableAddons));
    return await Promise.all([
      ...availableCareTypes.map((serviceItemType) =>
        dispatch(
          getCompanyFullServiceInfoList(
            {
              serviceType: ServiceType.Service,
              serviceItemType,
              inactive,
            },
            signal,
          ),
        ),
      ),
      withAddon &&
        dispatch(
          getCompanyFullServiceInfoList(
            {
              serviceType: ServiceType.Addon,
              serviceItemType: AddonItemType.serviceAddon,
              inactive,
            },
            signal,
          ),
        ),
    ]);
  },
);
/**
 * 获取当前 company 级别全部的 service list（grooming\boarding\daycare）
 *
 * 包含基本的 service 相关信息
 *
 * 推荐大多数场景需要拉取 company 级别的 service 的时候使用
 *
 * @param withAddon 是否获取 addon 的信息
 *
 */

export const getAllCompanyBasicServiceInfoList = action(
  async (
    dispatch,
    select,
    { inactive = !!ServiceActive.Active, withAddon }: GetAllCompanyServiceInfoParams,
    signal?: AbortSignal,
  ) => {
    return await Promise.all([
      ...[ServiceItemType.GROOMING, ServiceItemType.DAYCARE, ServiceItemType.BOARDING].map((serviceItemType) =>
        dispatch(
          getCompanyBasicServiceInfoList(
            {
              serviceType: ServiceType.Service,
              serviceItemType,
              inactive,
            },
            signal,
          ),
        ),
      ),
      withAddon &&
        dispatch(
          getCompanyBasicServiceInfoList(
            {
              serviceType: ServiceType.Addon,
              serviceItemType: AddonItemType.serviceAddon,
              inactive,
            },
            signal,
          ),
        ),
    ]);
  },
);
/**
 * 获取当前 company 级别某种 type 的 service list
 * 包含所有 service 相关信息，尤其是：
 *
 * 1. location override 相关信息
 * 2. weight filter\breed filter\coat filter 等相关信息
 *
 * 推荐只在需要完整 service 信息的场景下使用这个接口，否则可以使用 getCompanyBasicServiceInfoList
 */

export const getCompanyFullServiceInfoList = createServiceObservableAction(
  'getCompanyFullServiceInfoList',
  async (dispatch, select, input: GetCompanyServiceInfoParams, signal?: AbortSignal) => {
    const {
      serviceType,
      serviceItemType = ServiceItemType.GROOMING,
      inactive = !!ServiceActive.Active,
      businessIds,
      pagination,
    } = input;
    const accountId: number = select(currentAccountIdBox);
    const ids = businessIds?.map((id) => id.toString());
    const res = await ServiceManagementClient.getServiceList(
      {
        serviceType,
        serviceItemType,
        inactive,
        pagination: pagination,
        businessIds: ids || [],
        staffIds: [],
      },
      {
        signal,
      },
    );
    const transformedCategoryList = res.categoryList.map((item) => transformCategoryService(item));
    const type = `${serviceType}-${serviceItemType}`;
    const serviceList = transformedCategoryList.map((c) => c.services).flat();

    const actions = mergeCompanyServiceActions({
      inactive: !!inactive,
      accountId,
      type,
      transformedCategoryList,
    });

    dispatch(actions);
    return { serviceList, total: res?.pagination?.total };
  },
);
/**
 * 获取当前 company 级别的简略信息的 service list
 *
 * 不包括：
 * 1. location override 相关信息
 * 2. weight filter\breed filter\coat filter 等相关信息
 *
 * 推荐在只需要 service name、price、duration 等基本信息的场景下使用这个接口
 *
 *
 * 如果需要上述额外信息的话，可以使用 getCompanyFullServiceInfoList
 *
 */

export const getCompanyBasicServiceInfoList = action(
  async (
    dispatch,
    select,
    input: PartialRequired<GetApplicableServiceListParams, 'serviceType' | 'serviceItemType'>,
    signal?: AbortSignal,
  ) => {
    const {
      serviceType,
      serviceItemType = ServiceItemType.GROOMING,
      pagination,
      inactive,
      keyword,
      onlyAvailable = false,
      selectedServiceIds = [],
    } = input;
    const res = await ServiceManagementClient.getApplicableServiceList(
      {
        serviceType,
        serviceItemType,
        onlyAvailable,
        keyword,
        selectedServiceIds,
        pagination: pagination,
        inactive: !!inactive,
        petIds: [],
      },
      {
        signal,
      },
    );

    const transformedCategoryList = res.categoryList.map((item) => transformBusinessCategoryService(item));
    const type = `${serviceType}-${serviceItemType || ServiceItemType.GROOMING}`;
    const accountId = select(currentAccountIdBox);

    const serviceList = transformedCategoryList.map((c) => c.services).flat();

    const actions = mergeCompanyServiceActions({
      inactive: !!inactive,
      accountId,
      type,
      transformedCategoryList,
    });
    actions.push(applicableServiceMapBox.mergeItems(res.categoryList.flatMap((c) => c.services)));

    dispatch(actions);

    return { serviceList, total: res?.pagination?.total };
  },
); /**
 * 部分场景下获取带 pagination 的 daycare service list
 */

export const getDaycareServiceList = action(
  async (dispatch, select, input: ListServicesParams, signal?: AbortSignal) => {
    return await ServiceManagementClient.listServices(
      {
        ...input,
        serviceType: ServiceType.Service,
        serviceItemType: ServiceItemType.DAYCARE,
        inactive: false,
      },
      { signal },
    );
  },
);
