import { PetDetailDateType } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';
import {
  ServiceItemType,
  ServiceOverrideType,
  ServiceType,
} from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useSerialCallback } from '@moego/finance-utils';
import { Tooltip } from '@moego/ui';
import { useSelector } from 'amos';
import { Divider } from 'antd';
import classNames from 'classnames';
import React, { memo, useMemo } from 'react';
import { Condition } from '../../../../components/Condition';
import { PickService } from '../../../../components/ServiceApplicablePicker/components/PickService/PickService';
import { type ServicePriceDurationInfo } from '../../../../components/ServiceApplicablePicker/hooks/useServiceInfo';
import { type ServiceEntry } from '../../../../components/ServiceApplicablePicker/types/serviceEntry';
import { type ServiceOperationEntry } from '../../../../components/ServiceApplicablePicker/types/serviceOperation';
import { getDefaultService } from '../../../../components/ServiceApplicablePicker/utils/getDefaultService';
import { isMultipleStaffService } from '../../../../components/ServiceApplicablePicker/utils/isMultipleStaffService';
import {
  ServiceStaffPicker,
  type ServiceStaffPickerProps,
} from '../../../../components/ServiceStaffPicker/ServiceStaffPicker';
import { MultipleStaffId } from '../../../../components/ServiceStaffPicker/utils/multipleStaffId';
import { type ApptInfoPetServiceInfo } from '../../../../store/calendarLatest/calendar.types';
import { ScopeType } from '../../../../store/grooming/grooming.boxes';
import { serviceMapBox } from '../../../../store/service/service.boxes';
import { ID_ANONYMOUS, isNormal } from '../../../../store/utils/identifier';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { ReportActionName } from '../../../../utils/reportType';
import { reportData } from '../../../../utils/tracker';
import { splitPriceEqually } from '../../../CreateTicket/hooks/ticketSplitPriceEqual';
import { useSyncOperationPrice } from '../../../CreateTicket/hooks/useSyncOperationPrice';
import { useBundleService } from '../../hooks/useBundleService';
import { useStaffShowPriceDurationForService } from '../../hooks/useStaffShowPriceDurationForService';
import { EditOperationWorkMode } from './EditOperationWorkMode';
import { calcServiceDuration, initServiceOperation } from './EditPetServiceList.utils';
import { EditServiceOperation } from './EditServiceOperation';
import { SavedOptionList, SavedOptions } from './SavedOptions';
import { ServicePrice } from './ServicePrice';
import { ServiceTime } from './ServiceTime';
import { ServiceTitle } from './ServiceTitle';
import { ServiceVariationByStaffOnBoarding } from './ServiceVariationByStaffOnBoarding';
import { useCheckServicePrice } from './hooks/useCheckEditValidate';
import { useResetOpName } from './hooks/useInitOpName';
import { useMemorizeLastOperation } from './hooks/useMemorizeLastOperation';
import { useServiceStaff } from './hooks/useServiceStaff';
import { selectIsEnableSlotCalender } from '../../../../store/calendarLatest/calendar.selectors';

export interface EditPetServiceProps
  extends Pick<ServiceStaffPickerProps, 'starStaffId' | 'onChangeStarStaff' | 'showMultiStaff'> {
  onLoadingChange?: (value: boolean) => void;
  appointmentId?: number;
  className?: string;
  value: ApptInfoPetServiceInfo;
  onChange?: (value: ApptInfoPetServiceInfo, payload?: { bundleSalesAddonServices?: ServiceEntry[] }) => void;
  onDelete?: () => void;
  petId: number;
  clientId: number;
  disabledServices?: number[];
  deletable?: boolean;
  disabledSavedPriceAndDuration?: boolean;
  disabledTabs?: ServiceType[];
}

export const EditPetService = memo<EditPetServiceProps>((props) => {
  const {
    className,
    value,
    onDelete,
    petId,
    clientId,
    onChange,
    disabledServices,
    appointmentId,
    deletable,
    starStaffId,
    disabledSavedPriceAndDuration = false,
    disabledTabs,
    showMultiStaff,
    onLoadingChange,
    onChangeStarStaff,
  } = props;
  const appointmentDate = value.startDate!;
  const [serviceMap, isEnableSlotCalender] = useSelector(serviceMapBox, selectIsEnableSlotCalender);
  const isMultiStaff = isMultipleStaffService(value);
  const checkServicePrice = useCheckServicePrice();
  const memorizeLastOperation = useMemorizeLastOperation();
  const resetOpName = useResetOpName();
  const priceCheck = useMemo(() => checkServicePrice(value), [value, checkServicePrice]);

  const [service] = useSelector(serviceMapBox.mustGetItem(value.serviceId ?? ID_ANONYMOUS));
  const { syncOperationPrice } = useSyncOperationPrice();
  const { getBundleService, getIsNeedBundleService } = useBundleService({
    clientId,
  });
  const isCreatedOrder = isNormal(value?.orderLineItemId);

  const onChangeService = useSerialCallback(async (serviceId: number, extraServiceInf: ServicePriceDurationInfo) => {
    const { name: nextServiceName, type: serviceType } = serviceMap.mustGetItem(serviceId);
    const service = getDefaultService({
      ...value,
      ...extraServiceInf,
      serviceId,
      serviceName: nextServiceName,
      serviceType,
    });
    const originServiceName = serviceMap.mustGetItem(value?.serviceId ?? ID_ANONYMOUS).name;
    // reset operation price
    // reset operation duration
    function getOperationList(targetService: ServiceEntry) {
      return splitPriceEqually<ServiceOperationEntry>(targetService.servicePrice, targetService.operationList!)
        .map((op) => ({ ...op, duration: targetService.serviceTime }))
        .map((op, index) => {
          return resetOpName(op, index, originServiceName, targetService.serviceName);
        });
    }
    const nextService: ServiceEntry = {
      ...service,
      operationList: getOperationList(service),
    };
    const nextServiceTime = calcServiceDuration(nextService);
    const needBundleService = getIsNeedBundleService(serviceId, ServiceItemType.GROOMING);

    onChange?.({
      ...nextService,
      serviceTime: nextServiceTime,
    });

    if (!needBundleService) {
      onLoadingChange?.(false);
      return;
    }

    const bundleSalesAddonServices: ServiceEntry[] = await getBundleService({
      serviceItemType: ServiceItemType.GROOMING,
      serviceId,
      petIds: [petId],
      staffId: value?.staffId,
    });

    onChange?.(
      {
        ...nextService,
        serviceTime: nextServiceTime,
      },
      {
        bundleSalesAddonServices: bundleSalesAddonServices.map((v) => ({
          ...v,
          operationList: getOperationList(v),
          startTime: value.startTime,
          startDate: value.startDate,
          staffId: service.staffId,
          dateType: PetDetailDateType.PET_DETAIL_DATE_DATE_POINT,
        })),
      },
    );
    onLoadingChange?.(false);
  });

  const onChangeStaff = useServiceStaff({
    service: value,
    onChange,
    serviceStartTime: value?.startTime || 0,
  });

  const { showPriceDuration, serviceStaffPickerClassNames } = useStaffShowPriceDurationForService(value?.serviceId);

  const onChangePrice = useLatestCallback((price?: number) => {
    if (value?.servicePrice === price) {
      return;
    }
    const hasOperation = isMultipleStaffService(value);
    const nextOperationList = hasOperation
      ? splitPriceEqually<ServiceOperationEntry>(price ?? 0, value!.operationList!)
      : [];

    const nextService = getDefaultService({
      ...value,
      servicePrice: price,
      isPriceModified: true,
      operationList: syncOperationPrice(nextOperationList, price),
    });
    const validateSavedOption = !!SavedOptionList.find((i) => i.value === nextService.scopeTypePrice);
    // !validateSavedOption : 未勾选SavedOption
    // !value?.isPriceModified : "本次"Edit未修改过价格
    if (!validateSavedOption || !value?.isPriceModified) {
      nextService.scopeTypePrice = ScopeType.NotSaved;
      nextService.priceOverrideType = ServiceOverrideType.UNSPECIFIED;
    }
    reportData(ReportActionName.apptEditServicePrice, {
      serviceName: nextService.serviceName,
      serviceItemType: nextService.serviceItemType,
      serviceType: nextService.serviceType,
      isExistAppt: isNormal(appointmentId),
    });
    onChange?.(nextService);
  });

  const onChangeOperationList = useLatestCallback((obj: Partial<ApptInfoPetServiceInfo>) => {
    const nextService = getDefaultService({ ...value, ...obj });
    const { staffId, operationList } = nextService;
    const isMultiStaff = isMultipleStaffService(nextService);
    const nextServiceTime = calcServiceDuration(nextService);
    // 如果是多人，且没有main staff，则自动选择第一个staff
    if (isMultiStaff) {
      const staffNoInOperation = !operationList!.find((op) => op.staffId === staffId);
      if (staffNoInOperation) {
        const firstHasStaffOp = operationList.find((op) => isNormal(op.staffId));
        nextService.staffId = firstHasStaffOp ? firstHasStaffOp.staffId : MultipleStaffId;
      }
    }
    const finalService: ServiceEntry = {
      ...nextService,
      serviceTime: nextServiceTime,
      operationList: isMultiStaff ? syncOperationPrice(operationList!, nextService.servicePrice) : [],
    };
    onChange?.(finalService);
    memorizeLastOperation(finalService);
  });

  const onChangeMainStaff = useLatestCallback((staffId: number) => {
    const nextService = getDefaultService({ ...value, staffId });
    onChange?.(nextService);
  });

  const onAddOperation = useLatestCallback(() => {
    const op = initServiceOperation({ operationName: service.name, priceRatio: 0 });
    const currentOpList = value?.operationList ?? [];
    const nextOperationList = [...currentOpList, op];
    onChangeOperationList({ operationList: nextOperationList });
  });

  const onChangeServiceTime = useLatestCallback((serviceTime?: number) => {
    if (value?.serviceTime === serviceTime) {
      return;
    }
    const nextService = getDefaultService({
      ...value,
      serviceTime,
      isDurationModified: true,
    });
    const validateSavedOption = !!SavedOptionList.find((i) => i.value === nextService.scopeTypeTime);
    if (!validateSavedOption || !value?.isDurationModified) {
      nextService.scopeTypeTime = ScopeType.NotSaved;
      nextService.durationOverrideType = ServiceOverrideType.UNSPECIFIED;
    }
    reportData(ReportActionName.apptEditServiceDuration, {
      serviceName: nextService.serviceName,
      serviceItemType: nextService.serviceItemType,
      serviceType: nextService.serviceType,
      isExistAppt: isNormal(appointmentId),
    });
    onChange?.(nextService);
  });

  const onChangeServiceFields = useLatestCallback((obj: Partial<ApptInfoPetServiceInfo>) => {
    const nextService = getDefaultService({ ...value, ...obj });
    onChange?.(nextService);
  });

  return (
    <div className={classNames(className, 'moe-flex moe-flex-col moe-gap-y-[16px]')}>
      <ServiceTitle value={value} onDelete={onDelete} deletable={deletable} />
      <Tooltip content={isCreatedOrder ? 'This item has been paid, you can not edit it anymore.' : undefined}>
        <div>
          <PickService
            petId={petId}
            clientId={clientId}
            info={value}
            serviceId={isNormal(value?.serviceId) ? value.serviceId : undefined}
            disabledServices={disabledServices}
            serviceItemType={value.serviceItemType ?? ServiceItemType.GROOMING}
            serviceType={value?.serviceType || ServiceType.SERVICE}
            onChange={onChangeService}
            serviceIds={disabledServices}
            disabledTabs={disabledTabs}
          />
        </div>
      </Tooltip>
      <div className="moe-flex moe-items-start moe-gap-x-[8px]">
        <ServiceStaffPicker
          clientId={clientId}
          showMultiStaffOption={!isEnableSlotCalender}
          className="moe-w-[200px]"
          classNames={serviceStaffPickerClassNames}
          showPriceDuration={showPriceDuration}
          appointmentId={appointmentId}
          appointmentDate={appointmentDate}
          serviceStartTime={value?.startTime}
          serviceTime={value?.serviceTime}
          value={value?.staffId}
          petId={petId.toString()}
          serviceId={value?.serviceId}
          showMultiStaff={showMultiStaff}
          isEnableOperations={isMultipleStaffService(value)}
          onChange={(v, serviceVariation) => {
            // 这里应该是一定会有值的，但是仍然兼容一下类型，也不强制断言了
            onChangeStaff(v || ID_ANONYMOUS, serviceVariation);
          }}
          starStaffId={starStaffId}
          onChangeStarStaff={onChangeStarStaff}
        />
        <div className="moe-flex-1 moe-min-w-0">
          <ServicePrice
            isDisabled={isCreatedOrder}
            serviceId={value?.serviceId}
            value={value?.servicePrice}
            onChange={onChangePrice}
          />
          <Condition if={!priceCheck.validate}>
            <div className="moe-max-w-[130px] moe-ml-[-50px] moe-mt-[4px] moe-text-[14px] moe-text-[#FAAD14] moe-leading-[18px] moe-whitespace-nowrap">
              {priceCheck.errorMsg}
            </div>
          </Condition>
        </div>
        <div className="moe-flex-1 moe-min-w-0">
          <ServiceTime
            disabled={isMultiStaff || isCreatedOrder}
            value={value?.serviceTime}
            onChange={onChangeServiceTime}
          />
        </div>
      </div>
      <Condition if={value?.isPriceModified && !disabledSavedPriceAndDuration}>
        {() => (
          <SavedOptions
            type="price"
            appointmentDate={appointmentDate}
            checked={value?.isSavePrice}
            onSwitch={(checked) => {
              onChangeServiceFields({
                isSavePrice: checked,
                scopeTypePrice: checked ? ScopeType.ThisAndFollowing : ScopeType.NotSaved,
                priceOverrideType: checked ? ServiceOverrideType.CLIENT : ServiceOverrideType.UNSPECIFIED,
              });
            }}
            value={value?.scopeTypePrice}
            onChange={(value) => {
              onChangeServiceFields({
                isSavePrice: true,
                scopeTypePrice: value,
                priceOverrideType: ServiceOverrideType.CLIENT,
              });
            }}
          />
        )}
      </Condition>
      <Condition if={value?.isDurationModified && !disabledSavedPriceAndDuration}>
        {() => (
          <SavedOptions
            type="duration"
            appointmentDate={appointmentDate}
            checked={value?.isSaveDuration}
            onSwitch={(checked) => {
              onChangeServiceFields({
                isSaveDuration: checked,
                scopeTypeTime: checked ? ScopeType.ThisAndFollowing : ScopeType.NotSaved,
                durationOverrideType: checked ? ServiceOverrideType.CLIENT : ServiceOverrideType.UNSPECIFIED,
              });
            }}
            value={value?.scopeTypeTime}
            onChange={(value) => {
              onChangeServiceFields({
                isSaveDuration: true,
                scopeTypeTime: value,
                durationOverrideType: ServiceOverrideType.CLIENT,
              });
            }}
          />
        )}
      </Condition>
      <Condition
        if={
          isNormal(value?.staffId) &&
          value.serviceType === ServiceType.SERVICE &&
          (value?.isPriceModified || value?.isDurationModified)
        }
      >
        <ServiceVariationByStaffOnBoarding serviceId={value?.serviceId} />
      </Condition>
      <Condition if={isMultiStaff}>
        {() => (
          <div className="moe-bg-[#F7F8FA] moe-rounded-[8px]">
            <EditOperationWorkMode
              className="moe-p-[16px]"
              value={value!.workMode}
              onChange={(workMode) => onChangeOperationList({ workMode, isDurationModified: true })}
            />
            <Divider className="!moe-m-0 !moe-border-[#E6E6E6]" />
            <EditServiceOperation
              appointmentDate={appointmentDate}
              appointmentId={appointmentId}
              serviceStartTime={value?.startTime!}
              workMode={value!.workMode}
              mainStaffId={value?.staffId}
              value={value!.operationList}
              onChange={(operationList, service) => onChangeOperationList({ ...service, operationList })}
              onChangeMainStaff={onChangeMainStaff}
              servicePrice={value?.servicePrice}
              serviceId={value?.serviceId}
              onAddStaff={onAddOperation}
            />
          </div>
        )}
      </Condition>
    </div>
  );
});
