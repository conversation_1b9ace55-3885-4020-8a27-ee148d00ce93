import { ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useDispatch, useSelector, useStore } from 'amos';
import { isNil, omitBy, pick, uniqBy } from 'lodash';
import { useRef, useState } from 'react';
import { getDefaultService } from '../../../../../components/ServiceApplicablePicker/utils/getDefaultService';
import { isMultipleStaffService } from '../../../../../components/ServiceApplicablePicker/utils/isMultipleStaffService';
import {
  type ApptInfoClientPetInfo,
  type ApptInfoPetServiceInfo,
} from '../../../../../store/calendarLatest/calendar.types';
import { currentStaffIdBox } from '../../../../../store/staff/staff.boxes';
import { ID_ANONYMOUS, isNormal } from '../../../../../store/utils/identifier';
import { getIsBundleService, useBundleService } from '../../../hooks/useBundleService';
import { useCanAutoMerge } from '../../../hooks/useCanAutoMerge';
import { setServiceForPet } from '../../../store/appt.actions';
import { ApptServiceRecord, apptServiceMapBox } from '../../../store/appt.boxes';
import { matchApptServiceScene } from '../../../store/appt.options';
import { selectMainServiceInAppt } from '../../../store/appt.selectors';
import { type ApptPetEvaluationInfo, ApptServiceScene, CreateApptId } from '../../../store/appt.types';
import { type EditServiceItemProps } from '../../EditServiceItem/EditServiceItem.type';
import { type AddMoreProps } from './AddMore';

export interface EditPetServiceListHybridProps extends Pick<EditServiceItemProps, 'starStaffId' | 'onChangeStarStaff'> {
  className?: string;
  loading?: boolean;
  clientId: number;
  appointmentId?: number;
  deleteLoading?: boolean;
  disabledPetIds?: number[];
  value?: ApptInfoClientPetInfo;
  defaultStaffId?: number;
  disabledSavedPriceAndDuration?: boolean;
  isRepeatAppt?: boolean;
  onSave?: (params: ApptInfoClientPetInfo) => void;
  onDeletePet?: (repeatType?: number) => void;
  onCancel?: () => void;
  onChange?: (value: ApptInfoClientPetInfo) => void;
  onChangePet?: (petId: number) => void;
}

type HandleAddBundleServiceParams =
  | {
      onAdd: true;
      uuid?: string;
    }
  | {
      onAdd?: false;
      uuid: string;
    };

export const useEditServiceList = (props: EditPetServiceListHybridProps) => {
  const dispatch = useDispatch();
  const store = useStore();
  const [currentStaffId] = useSelector(currentStaffIdBox);

  const {
    appointmentId,
    clientId,
    defaultStaffId = currentStaffId,
    value = { petId: ID_ANONYMOUS, services: [], evaluations: [] },
    onChange,
    disabledSavedPriceAndDuration,
  } = props;
  const apptId = isNormal(appointmentId) ? String(appointmentId) : CreateApptId;
  const { petId, services } = value;
  const [mainService] = useSelector(selectMainServiceInAppt(apptId));

  const baseParams = { appointmentId, petId, clientId, disabledSavedPriceAndDuration };

  const ownerIds = useRef<string[]>([]);
  const [addMoreState, setAddMoreState] = useState<AddMoreProps | undefined>(undefined);

  const { isLoading, getBundleService, transformBundleServiceForBD } = useBundleService({
    clientId,
  });
  const canAutoMerge = useCanAutoMerge();

  const handleAddBundleService = async (service: ApptInfoPetServiceInfo, params: HandleAddBundleServiceParams) => {
    const { onAdd, uuid } = params;
    const getIsNeedBundleService = getIsBundleService(service);

    if (!getIsNeedBundleService) return;

    const bundleServices = await getBundleService({
      serviceId: service.serviceId,
      petIds: [petId],
      staffId: service.staffId,
    });
    const transformedBDData = transformBundleServiceForBD(bundleServices, {
      ...service,
      appointmentId,
    });
    const transformedGroomingData = bundleServices.map((v) => ({
      ...v,
      startDate: service.startDate,
      startTime: service.startTime,
      endTime: service.endTime,
    }));
    const transformedBundleServices = [ServiceItemType.BOARDING, ServiceItemType.DAYCARE].includes(
      mainService.serviceItemType,
    )
      ? transformedBDData
      : transformedGroomingData;
    const filteredServices = uniqBy(
      [...services.filter((s) => s.id !== uuid), ...transformedBundleServices],
      'serviceId',
    ) as ApptInfoPetServiceInfo[];
    const filteredBundleServices = transformedBundleServices.filter(
      (v) => !services.some((s) => s.serviceId === v.serviceId),
    );

    if (!onAdd) {
      onServiceChange(uuid, [service, ...filteredBundleServices]);
    } else {
      onChange?.({
        petId,
        evaluations: getEvaluations([service, ...filteredServices]),
        services: [service, ...filteredServices],
      });
    }
  };

  const getEvaluations = (services: ApptInfoPetServiceInfo[]) => {
    return services.flatMap((it) => it.evaluations).filter(Boolean) as ApptPetEvaluationInfo[];
  };

  const getRelateServiceIds = (serviceItemType?: ServiceItemType) => {
    if (!serviceItemType) return [];
    return services
      .filter((s) => s.serviceItemType === serviceItemType && isNormal(s.serviceId))
      .map((v) => v.serviceId);
  };

  const getDisableServiceIds = (serviceItemType?: ServiceItemType) => {
    if (
      matchApptServiceScene(ApptServiceScene.MultiSameService, {
        serviceItemType,
        mainServiceItemType: mainService.serviceItemType,
      })
    ) {
      return [];
    }
    return services
      .filter((s) => s.serviceItemType === serviceItemType)
      .reduce((prev, cur) => prev.concat(cur.serviceId), [] as number[]);
  };

  const onServiceChange = (uuid: string, newValList: ApptInfoPetServiceInfo[]) => {
    const nextServices = [...services];
    const index = nextServices.findIndex((s) => s.id === uuid);
    const fixedService: ApptInfoPetServiceInfo[] = newValList.map((newVal) => {
      return {
        ...newVal,
        enableOperation: isMultipleStaffService(newVal),
      };
    });
    nextServices.splice(index, 1, ...fixedService);
    onChange?.({
      petId,
      evaluations: getEvaluations(nextServices),
      services: nextServices,
    });
  };

  const handleChangeService = (uuid: string, newService?: ApptInfoPetServiceInfo) => {
    const prevService = services.find((s) => s.id === uuid);
    if (!uuid || !newService || !prevService) return;

    if (!canAutoMerge(prevService.serviceId, newService.serviceId)) {
      const title = newService.serviceType === ServiceType.ADDON ? 'Add add-on' : 'Add service';
      setAddMoreState({
        ...baseParams,
        title,
        value: getDefaultService({
          ...newService,
          serviceType: newService.serviceType,
          staffId: defaultStaffId,
        }),
        serviceItemType: newService.serviceItemType, // 这个漏了的话会，addMore 里取值的时候会 get 一个 undefined，导致新增的 serviceItemType 是 undefined
        disabledServices: getDisableServiceIds(newService.serviceItemType),
        serviceIds: getRelateServiceIds(newService.serviceItemType),
        onClose: () => {
          setAddMoreState(undefined);
        },
        onSave: (v) => {
          if (v) {
            onServiceChange(uuid, [v]);
            handleAddBundleService(v, { uuid });
          }
          setAddMoreState(undefined);
        },
      });
      return;
    }
    onServiceChange(uuid, [newService]);
    handleAddBundleService(newService, { uuid });
  };

  const handleDeleteService = (uuids: string[]) => {
    const nextServices = services.filter((s) => !uuids.includes(s.id));
    onChange?.({
      petId,
      evaluations: getEvaluations(nextServices),
      services: nextServices,
    });
  };

  const resolveAssociatedService = (newService: ApptInfoPetServiceInfo, oldService: ApptInfoPetServiceInfo) => {
    const nextServices = services.map((s) => {
      if (s.associatedId === oldService.id) {
        // 把原 addon 全部挂到新的 service 上
        return {
          ...s,
          associatedId: newService.id,
        };
      }
      return s;
    });
    const { lodgingId, lodgingName, staffId } = oldService;
    const originIndex = services.findIndex((s) => s.id === oldService.id);
    nextServices.splice(originIndex, 1, {
      ...newService,
      lodgingId,
      lodgingName,
      staffId,
    });

    return nextServices;
  };

  const presetNewAssociatedService = (newService: ApptInfoPetServiceInfo) => {
    const { serviceId, staffId } = newService;
    const apptId = isNormal(appointmentId) ? String(appointmentId) : CreateApptId;

    // 这一步的目的是为了保证在后续的流程，能够正确的找到对应的关联service
    // 这里设置的service不会被保存到store中，只是用来在后续的流程中找到对应的关联service
    // 在退出页面的时候，会清除这些service
    const newId = newService.id;
    const newOwnId = ApptServiceRecord.createOwnId(apptId, newId);
    const isExist = store.select(apptServiceMapBox.hasItem(newOwnId));
    if (!isExist) {
      dispatch(
        setServiceForPet(apptId, newId, {
          ...newService,
          serviceId: String(serviceId),
          staffId: String(staffId),
        }),
      );
      ownerIds.current.push(newOwnId);
    }
  };

  const handleClear = () => {
    dispatch(ownerIds.current.map((id) => apptServiceMapBox.deleteItem(id)));
  };

  const handleChangeAssociatedService = (newService: ApptInfoPetServiceInfo, oldService: ApptInfoPetServiceInfo) => {
    const nextServices = resolveAssociatedService(newService, oldService);
    presetNewAssociatedService(newService);
    onChange?.({
      petId,
      evaluations: getEvaluations(nextServices),
      services: nextServices,
    });
  };

  const handleAddService = (result?: ApptInfoPetServiceInfo) => {
    if (!result) return;
    const nextServices = [...services];
    nextServices.push(result);
    setAddMoreState(undefined);
    handleAddBundleService(result, { onAdd: true });
    onChange?.({
      petId,
      evaluations: getEvaluations(nextServices),
      services: nextServices,
    });
  };

  const onAdd = (options: {
    title: string;
    value: ApptInfoPetServiceInfo;
    serviceItemType?: ServiceItemType;
    disabledTabs?: ServiceType[];
  }) => {
    const { title, value, serviceItemType, disabledTabs } = options;
    setAddMoreState({
      ...baseParams,
      title,
      value,
      disabledServices: getDisableServiceIds(serviceItemType),
      serviceIds: getRelateServiceIds(serviceItemType),
      serviceItemType,
      onClose: () => {
        setAddMoreState(undefined);
      },
      onSave: handleAddService,
      disabledTabs,
    });
  };

  const handleAddMoreAddOn = (v: ApptInfoPetServiceInfo) => {
    onAdd({
      title: 'Add add-on',
      value: getDefaultService({
        serviceType: ServiceType.ADDON,
        serviceItemType: v.serviceItemType,
        staffId: defaultStaffId,
        associatedId: String(v.id),
      }),
      serviceItemType: v.serviceItemType,
    });
  };

  const handleAddMoreService = (options?: {
    serviceItemType: ServiceItemType;
    disabledTabs?: ServiceType[];
    associatedId?: string;
    title?: string;
  }) => {
    const {
      title = 'Add service/add-on',
      serviceItemType = ServiceItemType.GROOMING,
      disabledTabs,
      associatedId,
    } = options || {};

    // 这里存在一个需求坑点，假如我们将 service 的预设值给 add more
    // 但是 add 可能会选择一个 not require addon，那预设值会导致返回结果存在冗余的预设
    // 这里最好的是 add more 表单 通过 shouldUnregister 来让 save 的结果只有表单数据
    // 可惜目前做不到，因为edit service item 目前还无法被 Form.Item 接管
    // 此外，假如我们只选了一个 not require staff 的 addon
    // 那在这种情况下，其实数据的预填可能会出问题，因为 add more 时，我既可以选 service，又可以选 addon
    // 一旦我们拿 addon 的数据去填充，可能刚好 service 的表单会出现非法的预设值
    const lastSameCareTypeService = services.find(
      (s) => s.serviceItemType === serviceItemType && s.serviceType === ServiceType.SERVICE,
    );
    const extendLast = omitBy(
      pick(lastSameCareTypeService, ['startTime', 'endTime', 'startDate', 'specificDates', 'dateType', 'staffId']),
      isNil,
    );
    onAdd({
      title,
      value: getDefaultService({ serviceItemType, associatedId, staffId: defaultStaffId, ...extendLast }),
      serviceItemType,
      disabledTabs,
    });
  };

  return {
    addMoreState,
    mainService,
    handleAddMoreService,
    handleAddMoreAddOn,
    handleChangeAssociatedService,
    handleChangeService,
    handleDeleteService,
    handleClear,
    getRelateServiceIds,
    getDisableServiceIds,
    isBundleServiceLoading: isLoading,
  };
};
