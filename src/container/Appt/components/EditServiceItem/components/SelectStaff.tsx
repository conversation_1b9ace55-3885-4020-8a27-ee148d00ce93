import React, { memo } from 'react';
import { isMultipleStaffService } from '../../../../../components/ServiceApplicablePicker/utils/isMultipleStaffService';
import { ServiceStaffPicker } from '../../../../../components/ServiceStaffPicker/ServiceStaffPicker';
import { ID_ANONYMOUS, isNormal } from '../../../../../store/utils/identifier';
import { useStaffShowPriceDurationForService } from '../../../hooks/useStaffShowPriceDurationForService';
import { useServiceStaff } from '../../EditPetServiceList/hooks/useServiceStaff';
import { type EditServiceItemProps } from '../EditServiceItem.type';
import { cn } from '@moego/ui';
import { useSelector } from 'amos';
import { selectApptInfo } from '../../../store/appt.selectors';

interface SelectStaffProps extends Omit<EditServiceItemProps, 'petId' | 'clientId'> {
  disableConflictCheck?: boolean;
  showMultiStaffOption?: boolean;
}

export const SelectStaff = memo((props: SelectStaffProps) => {
  const {
    value,
    appointmentId,
    onChangeStarStaff,
    starStaffId,
    onChange,
    className,
    disableConflictCheck,
    showMultiStaffOption,
  } = props;
  const { petId, serviceId, startDate = '', startTime } = value || {};
  const [apptInfo] = useSelector(selectApptInfo(String(appointmentId)));

  const onChangeStaff = useServiceStaff({
    service: value,
    onChange,
    serviceStartTime: startTime || ID_ANONYMOUS,
  });

  const { showPriceDuration, serviceStaffPickerClassNames } = useStaffShowPriceDurationForService(value?.serviceId);

  return (
    <ServiceStaffPicker
      disableConflictCheck={disableConflictCheck}
      className={cn('moe-w-[200px]', className)}
      classNames={serviceStaffPickerClassNames}
      showPriceDuration={showPriceDuration}
      appointmentId={appointmentId}
      appointmentDate={startDate}
      serviceStartTime={startTime}
      serviceTime={value?.serviceTime}
      value={isNormal(value?.staffId) ? value.staffId : undefined}
      serviceId={serviceId}
      petId={petId}
      isEnableOperations={isMultipleStaffService(value)}
      onChange={(v, serviceVariation) => {
        // 这里应该是一定会有值的，但是仍然兼容一下类型，也不强制断言了
        onChangeStaff(v || ID_ANONYMOUS, serviceVariation);
      }}
      starStaffId={starStaffId}
      onChangeStarStaff={onChangeStarStaff}
      clientId={+apptInfo.appointment.customerId}
      showMultiStaffOption={showMultiStaffOption}
    />
  );
});
