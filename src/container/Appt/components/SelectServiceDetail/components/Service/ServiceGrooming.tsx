import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { cn, DatePicker, Form, numberToString, Select, TimePicker, useWatch } from '@moego/ui';
import { useMemoizedFn } from 'ahooks';
import { useDispatch, useSelector, useStore } from 'amos';
import dayjs from 'dayjs';
import { isNil } from 'lodash';
import React, { memo, useEffect } from 'react';
import { ServiceStaffPicker } from '../../../../../../components/ServiceStaffPicker/ServiceStaffPicker';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import { calendarSelectedDate } from '../../../../../../store/calendarLatest/calendar.boxes';
import { ServiceType } from '../../../../../../store/service/category.boxes';
import { serviceMapBox } from '../../../../../../store/service/service.boxes';
import { isNormal } from '../../../../../../store/utils/identifier';
import { DATE_FORMAT_EXCHANGE } from '../../../../../../utils/DateTimeUtil';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { useStaffShowPriceDurationForService } from '../../../../hooks/useStaffShowPriceDurationForService';
import { setServiceForPet } from '../../../../store/appt.actions';
import { selectApptInfo, selectApptPetService, selectMainServiceInAppt } from '../../../../store/appt.selectors';
import { type ApptServiceInfoRecord } from '../../../../store/appt.types';
import { type ServiceProps } from './Service.type';
import { PetDetailDateType } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';
import { SelectRequireStaffDateTypeOptions } from '../../../../../../components/DateType/DateType.utils';
import { selectIsEnableSlotCalender } from '../../../../../../store/calendarLatest/calendar.selectors';
import { TimeSlot } from './TimeSlot/TimeSlot';
import { useServiceDetailRealStartDate } from '../../hooks/useServiceDetailRealStartDate';
import { useAdditionalServiceListContext } from '../AdditionalService/AdditionalServiceList.context';
import { useShouldUseDefaultStartDate } from '../../../../hooks/useShouldUseDefaultStartDate';

const { DAYCARE } = ServiceItemType;
interface ServiceGroomingProps extends ServiceProps {
  formKey: string;
}

export const ServiceGrooming = memo((props: ServiceGroomingProps) => {
  const {
    formKey: key,
    petId,
    item: { id, serviceType },
    appointmentId,
    form,
  } = props;
  const dispatch = useDispatch();
  const store = useStore();

  const [{ startDate, startTime = 0, staffId, serviceTime, serviceId, dateType }, mainService, business, apptInfo] =
    useSelector(
      selectApptPetService(appointmentId, id),
      selectMainServiceInAppt(appointmentId),
      selectCurrentBusiness,
      selectApptInfo(appointmentId),
    );
  const [service, isEnableSlotCalender] = useSelector(
    serviceMapBox.mustGetItem(Number(serviceId)),
    selectIsEnableSlotCalender,
  );

  const { showPriceDuration } = useStaffShowPriceDurationForService(serviceId);

  const handleChange = useMemoizedFn((params: Partial<ApptServiceInfoRecord>) => {
    dispatch(setServiceForPet(appointmentId, id, params));
    // 已有b/d后，添加grooming 时修改时间，calendar要把preview card 更新到所选时间位置
    const {
      startDate: updatedStartDate,
      startTime: updatedStartTime,
      staffId: updatedStaffId,
    } = store.select(selectApptPetService(appointmentId, id));
    if (updatedStartDate && !isNil(updatedStartTime) && updatedStaffId) {
      dispatch(
        calendarSelectedDate.setState(dayjs(params.startDate || startDate).setMinutes(params.startTime || startTime)),
      );
    }
  });

  const handleStartTimeChange = useLatestCallback((startTime: number) => {
    const endTime = startTime + (serviceTime ?? service.duration);
    handleChange({ startTime, endTime });
  });

  useEffect(() => {
    if (mainService.serviceItemType === DAYCARE) {
      return handleChange({ startDate: mainService.startDate, endDate: mainService.endDate });
    }
  }, [mainService]);

  const shouldUseDefaultStartDate = useShouldUseDefaultStartDate({
    mainServiceItemType: mainService.serviceItemType,
  });

  const startDateValue = useWatch({
    name: `${key}.startDate`,
    control: form.control,
  });

  useEffect(() => {
    const currentDate = store.select(calendarSelectedDate);
    if (startDateValue && !currentDate.isSame(startDateValue, 'date') && isEnableSlotCalender) {
      dispatch(calendarSelectedDate.setState(dayjs(startDateValue)));
    }
  }, [startDateValue]);

  const { isInAdditionalServiceList } = useAdditionalServiceListContext();

  const renderDatePicker = (isFull?: boolean) => (
    <div className={cn(isFull && 'moe-col-span-2')}>
      <Form.Item
        name={`${key}.startDate`}
        rules={{
          required: 'Required',
          validate(v) {
            if (
              [ServiceItemType.BOARDING, ServiceItemType.DAYCARE].includes(mainService.serviceItemType) &&
              serviceType === ServiceType.Service
            ) {
              const isOutOfRange = v?.isBefore(dayjs(mainService.startDate)) || v?.isAfter(dayjs(mainService.endDate));
              if (isOutOfRange) {
                form.setValue(`${key}.startDate`, null);
                return "Date exceeds the range for this pet's stay.";
              }
            }
            return true;
          },
        }}
        label={'Start date'}
      >
        <DatePicker
          isRequired
          defaultViewDate={dayjs(mainService.startDate)}
          format={business.dateFormat}
          placeholder="Select date"
          disabledDate={(d) => {
            return d.isBefore(mainService.startDate, 'date') || d.isAfter(mainService.endDate, 'date');
          }}
          onChange={(v) => {
            const date = v?.format(DATE_FORMAT_EXCHANGE);
            handleChange({ startDate: date, endDate: date });
          }}
        />
      </Form.Item>
    </div>
  );

  const renderDateTypeSelector = () => (
    <div className="moe-col-span-2">
      <Form.Item name={`${key}.dateType`} label="Staff" rules={{ required: true }} transformer={numberToString}>
        <Select
          label="Date"
          isRequired
          value={String(dateType)}
          onChange={(v) => {
            const newDateType = Number(v) as PetDetailDateType;
            let newStartDate: string | undefined = startDate;
            if (newDateType === PetDetailDateType.PET_DETAIL_DATE_FIRST_DAY) {
              newStartDate = mainService.startDate;
            } else if (newDateType === PetDetailDateType.PET_DETAIL_DATE_LAST_DAY) {
              newStartDate = mainService.endDate;
            }
            handleChange({
              dateType: newDateType,
              startDate: newStartDate,
            });
          }}
          classNames={{
            // 文案太长，指定宽度
            overlay: '!moe-w-[290px]',
          }}
        >
          {SelectRequireStaffDateTypeOptions.map((option) => (
            <Select.Item key={String(option.key)} title={option.label} />
          ))}
        </Select>
      </Form.Item>
    </div>
  );

  const renderTimeSlotPicker = (startDateForSlot: string) =>
    isNormal(staffId) && (
      <div className="moe-col-span-2 moe-mt-[-16px]">
        <TimeSlot
          value={{
            id,
            startTime: startTime || 0,
            appointmentId,
            staffId,
            startDate: startDateForSlot,
            petId,
          }}
          isEnableSlotCalender={isEnableSlotCalender}
          business={business}
          onSelect={handleStartTimeChange}
        />
      </div>
    );

  const renderTimePicker = (isFull?: boolean) => (
    <div className={cn(isFull && 'moe-col-span-2')}>
      <Form.Item name={`${key}.startTime`} rules={{ required: true }} label={'Start time'}>
        <TimePicker
          isRequired
          placeholder="Choose time"
          format={business.timeFormat()}
          onChange={(v) => {
            if (v) {
              handleStartTimeChange(v.getMinutes());
            }
          }}
        />
      </Form.Item>
    </div>
  );

  const renderStaffPicker = (isFull?: boolean) => {
    return (
      <div className={cn(isFull && 'moe-col-span-2')}>
        <Form.Item name={`${key}.staffId`} label="Staff" rules={{ required: true }}>
          <ServiceStaffPicker
            showMultiStaffOption={!isEnableSlotCalender}
            className={shouldUseDefaultStartDate ? 'moe-flex-1' : 'moe-w-full'}
            classNames={{
              menuPortal: cn(
                !!shouldUseDefaultStartDate && '!moe-w-[534px] !moe-right-[48px] !moe-left-auto',
                !isFull && !isInAdditionalServiceList && '!moe-w-[400px] !moe-right-[48px] !moe-left-auto',
                !isFull && isInAdditionalServiceList && '!moe-w-[400px] !moe-right-[64px] !moe-left-auto',
              ),
            }}
            value={isNormal(staffId) ? Number(staffId) : undefined}
            serviceId={Number(serviceId)}
            petId={String(petId)}
            appointmentDate={dayjs(startDate)?.format(DATE_FORMAT_EXCHANGE)}
            showPriceDuration={showPriceDuration}
            serviceStartTime={startTime}
            serviceTime={serviceTime}
            showMultiStaff={false}
            onChange={(staffId, serviceVariation) => {
              handleChange({ staffId: String(staffId), ...serviceVariation });
            }}
            disableConflictCheck={isEnableSlotCalender}
            clientId={+apptInfo.appointment.customerId}
          />
        </Form.Item>
      </div>
    );
  };

  const realStartDate = useServiceDetailRealStartDate({
    appointmentId,
    startDate,
    dateType,
  });

  const isDatePoint = dateType === PetDetailDateType.PET_DETAIL_DATE_DATE_POINT;
  const isShowDateTypeSelector = !shouldUseDefaultStartDate;
  const isShowDatePicker = isShowDateTypeSelector && isDatePoint;
  const isAvailableTimeSlotPickerVisible =
    isEnableSlotCalender && !!isNormal(staffId) && !!realStartDate && isNormal(id);
  return (
    <div className="moe-grid moe-grid-cols-2 moe-gap-s">
      {isEnableSlotCalender ? (
        <>
          {renderStaffPicker(true)}
          {isShowDateTypeSelector && renderDateTypeSelector()}
          {isShowDatePicker && renderDatePicker(true)}
          {isAvailableTimeSlotPickerVisible && renderTimeSlotPicker(realStartDate)}
          {renderTimePicker(true)}
        </>
      ) : (
        <>
          {isShowDateTypeSelector && renderDateTypeSelector()}
          {isShowDatePicker && renderDatePicker()}
          {renderTimePicker()}
          {renderStaffPicker(isShowDatePicker)}
        </>
      )}
    </div>
  );
});
