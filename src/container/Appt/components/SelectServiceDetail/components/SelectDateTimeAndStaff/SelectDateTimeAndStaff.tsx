import { PetDetailDateType } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';
import { cn, DatePicker, Form, numberToString, Select, TimePicker } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import dayjs from 'dayjs';
import React, { memo, useEffect, useState } from 'react';
import {
  getDisabledDays,
  SelectRequireStaffDateTypeOptions,
} from '../../../../../../components/DateType/DateType.utils';
import { ServiceStaffPicker } from '../../../../../../components/ServiceStaffPicker/ServiceStaffPicker';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import { isNormal } from '../../../../../../store/utils/identifier';
import { DATE_FORMAT_EXCHANGE, mustDayJs } from '../../../../../../utils/DateTimeUtil';
import { setAddonForPet } from '../../../../store/appt.actions';
import {
  selectApptInfo,
  selectApptPetAddon,
  selectApptPetService,
  selectMainServiceInAppt,
} from '../../../../store/appt.selectors';
import { type ApptAddOnInfoRecord } from '../../../../store/appt.types';
import { type AddonProps } from '../Addon/Addon.type';
import { selectIsEnableSlotCalender } from '../../../../../../store/calendarLatest/calendar.selectors';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { TimeSlot } from '../Service/TimeSlot/TimeSlot';
import { useServiceDetailRealStartDate } from '../../hooks/useServiceDetailRealStartDate';
import { useShouldUseDefaultStartDate } from '../../../../hooks/useShouldUseDefaultStartDate';

interface SelectDateTimeAndStaffProps extends AddonProps {
  formKey: string;
}
export const SelectDateTimeAndStaff = memo((props: SelectDateTimeAndStaffProps) => {
  const { formKey: key, item, petId, appointmentId } = props;
  const { id } = item;
  const dispatch = useDispatch();
  const [business, { staffId, startTime, startDate, serviceTime, associatedId = '', dateType }, mainService] =
    useSelector(selectCurrentBusiness, selectApptPetAddon(appointmentId, id), selectMainServiceInAppt(appointmentId));
  const [associatedService, isEnableSlotCalender, apptInfo] = useSelector(
    selectApptPetService(appointmentId, associatedId),
    selectIsEnableSlotCalender,
    selectApptInfo(appointmentId),
  );

  const shouldUseDefaultStartDate = useShouldUseDefaultStartDate({
    mainServiceItemType: mainService.serviceItemType,
  });

  const [startViewDate, setStartViewDate] = useState(mustDayJs(mainService.startDate));

  const handleChange = useLatestCallback((params: Partial<ApptAddOnInfoRecord>) => {
    dispatch(setAddonForPet(appointmentId, id, params));
  });

  const handleStartTimeChange = useLatestCallback((startTime: number) => {
    const endTime = startTime + (serviceTime ?? 0);
    handleChange({ startTime, endTime });
  });

  // 有可能主 service 的 startDate 发生变化，这里需做一个同步逻辑，否则会报错
  useEffect(() => {
    if (shouldUseDefaultStartDate) {
      return handleChange({
        startDate: mainService.startDate,
        dateType: PetDetailDateType.PET_DETAIL_DATE_DATE_POINT,
      });
    }
  }, [shouldUseDefaultStartDate, mainService.startDate]);

  const renderStaffPicker = (isFull?: boolean) => (
    <div className={cn(isFull && 'moe-col-span-2')}>
      <Form.Item name={`${key}.staffId`} label="Staff" rules={{ required: true }}>
        <ServiceStaffPicker
          value={isNormal(staffId) ? Number(staffId) : undefined}
          serviceId={Number(item.serviceId)}
          petId={String(petId)}
          isRequired={true}
          appointmentDate={dayjs(startDate)?.format(DATE_FORMAT_EXCHANGE)}
          serviceStartTime={startTime}
          serviceTime={serviceTime}
          showMultiStaff={false}
          onChange={(staffId, serviceVariation) => {
            handleChange({
              staffId: String(staffId),
              ...serviceVariation,
            });
          }}
          disableConflictCheck={isEnableSlotCalender}
          showMultiStaffOption={!isEnableSlotCalender}
          clientId={+apptInfo.appointment.customerId}
        />
      </Form.Item>
    </div>
  );

  const renderDateTypeSelector = () => (
    <div className="moe-col-span-2">
      <Form.Item name={`${key}.dateType`} label="Staff" rules={{ required: true }} transformer={numberToString}>
        <Select
          label="Date"
          isRequired
          value={String(dateType)}
          onChange={(v) => {
            const newDateType = Number(v) as PetDetailDateType;
            let newStartDate: string | undefined = startDate;
            if (newDateType === PetDetailDateType.PET_DETAIL_DATE_FIRST_DAY) {
              newStartDate = mainService.startDate;
            } else if (newDateType === PetDetailDateType.PET_DETAIL_DATE_LAST_DAY) {
              newStartDate = mainService.endDate;
            }
            handleChange({
              dateType: newDateType,
              startDate: newStartDate,
            });
          }}
          classNames={{
            // 文案太长，指定宽度
            overlay: '!moe-w-[290px]',
          }}
        >
          {SelectRequireStaffDateTypeOptions.map((option) => (
            <Select.Item key={String(option.key)} title={option.label} />
          ))}
        </Select>
      </Form.Item>
    </div>
  );

  const renderDatePicker = (isFull?: boolean) => (
    <div className={cn(isFull && 'moe-col-span-2')}>
      <Form.Item name={`${key}.startDate`} label="Select date" rules={{ required: true }}>
        <DatePicker
          viewDate={startViewDate}
          onViewDateChange={setStartViewDate}
          onOpenChange={() => setStartViewDate(mustDayJs(mainService.startDate))}
          isRequired
          format={business.dateFormat}
          placeholder="Select date"
          disabledDate={(d) =>
            getDisabledDays(d, {
              minDate: associatedService.startDate || mainService.startDate,
              maxDate: associatedService.endDate || mainService.endDate,
              specificDates: associatedService?.specificDates,
            })
          }
          onChange={(v) => {
            handleChange({
              startDate: v?.format(DATE_FORMAT_EXCHANGE),
            });
          }}
        />
      </Form.Item>
    </div>
  );

  const renderTimeSlotPicker = (startDateValue: string) =>
    isNormal(staffId) && (
      <div className="moe-col-span-2 moe-mt-[-16px]">
        <TimeSlot
          value={{
            id,
            startTime: startTime || 0,
            appointmentId,
            staffId,
            startDate: startDateValue,
            petId,
          }}
          isEnableSlotCalender={isEnableSlotCalender}
          business={business}
          onSelect={handleStartTimeChange}
        />
      </div>
    );

  const renderTimePicker = (isFull?: boolean) => (
    <div className={cn(isFull && 'moe-col-span-2')}>
      <Form.Item name={`${key}.startTime`} label="Start time" rules={{ required: true }}>
        <TimePicker
          isRequired
          placeholder="Select time"
          format={business.timeFormat()}
          onChange={(v) => {
            handleChange({
              startTime: v?.getMinutes(),
            });
          }}
        />
      </Form.Item>
    </div>
  );

  const realStartDate = useServiceDetailRealStartDate({
    appointmentId,
    startDate,
    dateType,
  });

  const isDatePoint = dateType === PetDetailDateType.PET_DETAIL_DATE_DATE_POINT;
  const isShowDateTypeSelector = !shouldUseDefaultStartDate;
  const isShowDatePicker = isShowDateTypeSelector && isDatePoint;
  const isAvailableTimeSlotPickerVisible =
    isEnableSlotCalender && !!isNormal(staffId) && !!realStartDate && isNormal(id);
  return (
    <div className="moe-grid moe-grid-cols-2 moe-gap-s">
      {isEnableSlotCalender ? (
        <>
          {renderStaffPicker(true)}
          {isShowDateTypeSelector && renderDateTypeSelector()}
          {isShowDatePicker && renderDatePicker(true)}
          {isAvailableTimeSlotPickerVisible && renderTimeSlotPicker(realStartDate)}
          {renderTimePicker(true)}
        </>
      ) : (
        <>
          {isShowDateTypeSelector && renderDateTypeSelector()}
          {isShowDatePicker && renderDatePicker()}
          {renderTimePicker()}
          {renderStaffPicker(isShowDatePicker)}
        </>
      )}
    </div>
  );
});
