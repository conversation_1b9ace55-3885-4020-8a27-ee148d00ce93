import { MajorPlusOutlined } from '@moego/icons-react';
import { Button, cn } from '@moego/ui';
import React, { type ReactNode, type PropsWithChildren } from 'react';
import { Condition } from '../../../../../../components/Condition';
import { memoForwardRef } from '../../../../../../utils/react';
import { MedicationItem } from './Item';
import { type MedicationValue, getDefaultMedicationValue } from './Medication.util';

export interface MedicationProps {
  appointmentId: string;
  petId: string;
  value?: MedicationValue[];
  dateLabel?: string;
  containerClassName?: string;
  onChange?: (value: MedicationValue[]) => void;
  renderHeader?: () => ReactNode;
  renderFooter?: () => ReactNode;
  showDeleteButton?: boolean;
}

export const Medication = memoForwardRef<HTMLDivElement, PropsWithChildren<MedicationProps>>((props, ref) => {
  const {
    appointmentId,
    petId,
    value = [],
    dateLabel,
    onChange,
    children,
    renderHeader,
    renderFooter,
    containerClassName,
    showDeleteButton = false,
  } = props;

  const handleChange = (v: MedicationValue[]) => {
    onChange?.(v);
  };

  const handleRemove = (id?: string) => {
    handleChange(value.filter((item) => item.id !== id));
  };

  const handleAdd = () => {
    handleChange(value.concat(getDefaultMedicationValue({})));
  };

  return (
    <div ref={ref}>
      {children}
      <div className={cn('moe-flex moe-flex-col moe-rounded-s moe-bg-neutral-sunken-0', containerClassName)}>
        {renderHeader?.()}
        {value.map((item, index: number) => (
          <div className="moe-border-b-[1px] moe-border-solid moe-border-divider moe-border-bottom moe-p-s" key={index}>
            <MedicationItem
              appointmentId={appointmentId}
              petId={petId}
              value={item}
              dateLabel={dateLabel}
              onChange={(v) => {
                const newValue = value.slice();
                newValue[index] = v;
                handleChange(newValue);
              }}
            />
            <Condition if={showDeleteButton || index !== 0}>
              <Button
                align="start"
                size="s"
                variant="tertiary-legacy"
                className="moe-mt-s"
                onPress={() => handleRemove(item.id)}
              >
                Delete schedule
              </Button>
            </Condition>
          </div>
        ))}

        <div className="moe-p-s moe-pt-xs">
          <Button size="s" icon={<MajorPlusOutlined />} variant="tertiary" onPress={handleAdd}>
            Add medication schedule
          </Button>
        </div>
        {renderFooter?.()}
      </div>
    </div>
  );
});
