import React, { memo, useMemo, type FC } from 'react';
import { Text } from '@moego/ui';
import { type AppointmentPetMedicationScheduleDef } from '@moego/api-web/moego/models/appointment/v1/appointment_pet_medication_schedule_defs';
import { type ServiceFeedingMedication } from '../../../store/appt.types';
import { ViewInstruction } from '../ViewInstruction';
import { useSelectScheduleTime } from '../../../hooks/useSyncPetDetail';
import { useSelector } from 'amos';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { FeedingMedicationScheduleDateType } from '@moego/api-web/moego/models/business_customer/v1/business_pet_feeding_medication_enum';
import { SelectFeedingMedicationDateTypeEnum } from '../../../../../components/DateType/DateType.utils';

interface ViewPetMedicationProps {
  value: ServiceFeedingMedication;
  onClick?: () => void;
}

export const ViewPetMedication: FC<ViewPetMedicationProps> = (props) => {
  const { value, onClick } = props;
  const medications = value?.medications || [];
  if (!medications.length) {
    return null;
  }
  return (
    <ViewInstruction onClick={onClick}>
      {medications.map((instruction, index) => (
        <MedicationInfo data={instruction} key={index} />
      ))}
    </ViewInstruction>
  );
};

interface MedicationInfoProps {
  data: Partial<AppointmentPetMedicationScheduleDef>;
}
const MedicationInfo = memo<MedicationInfoProps>((props) => {
  const [business] = useSelector(selectCurrentBusiness());
  const { formatOptionsLabel } = useSelectScheduleTime();
  const { data } = props;
  const { selectedDate, medicationAmount, medicationUnit, medicationName, medicationNote, medicationTimes } =
    data ?? {};
  const { dateType = FeedingMedicationScheduleDateType.UNSPECIFIED, specificDates } = selectedDate ?? {};

  const dateTimeList = useMemo(() => {
    const scheduleTimeList = (medicationTimes || [])
      ?.map((timeItem) => formatOptionsLabel(timeItem.scheduleTime, timeItem.extraJson.label, true))
      .filter(Boolean);

    const scheduleDateList =
      dateType === FeedingMedicationScheduleDateType.SPECIFIC_DATE
        ? (specificDates ?? []).map((date) => business.formatDate(date))
        : [SelectFeedingMedicationDateTypeEnum.mapLabels[dateType] ?? ''];

    return scheduleDateList
      .map((date) => scheduleTimeList.map((time) => [date, time].filter(Boolean).join(' ')))
      .flat();
  }, [medicationTimes, dateType, specificDates, business]);

  const detailText = useMemo(() => {
    const amountUnit = [medicationAmount, medicationUnit].filter(Boolean).join(' ');
    const typeSourceInstruction = [medicationName, medicationNote].filter(Boolean).join(', ');
    return [amountUnit, typeSourceInstruction].filter(Boolean).join(', ');
  }, [medicationAmount, medicationUnit, medicationName, medicationNote]);

  return (
    <>
      {dateTimeList.map((dateTimeText, index) => {
        return (
          <Text variant="regular" key={index}>
            {dateTimeText}: {detailText}
          </Text>
        );
      })}
    </>
  );
});
