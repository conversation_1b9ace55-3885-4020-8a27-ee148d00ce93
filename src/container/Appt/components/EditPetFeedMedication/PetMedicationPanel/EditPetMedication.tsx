import React, { type FC } from 'react';
import { Medication } from '../../SelectServiceDetail/components/Medication/Medication';
import { CreateApptId, type ServiceFeedingMedication } from '../../../store/appt.types';
import { <PERSON><PERSON>, Heading } from '@moego/ui';
import { toast<PERSON><PERSON> } from '../../../../../components/Toast/Toast';
import { omit } from 'lodash';
import { useSerialCallback } from '@moego/tools';
import { useDispatch, useSelector } from 'amos';
import { petMapBox } from '../../../../../store/pet/pet.boxes';
import { useBool } from '../../../../../utils/hooks/useBool';
import { usePropsState } from '../../../../../utils/hooks/usePropsState';
import { ID_ANONYMOUS } from '../../../../../store/utils/identifier';
import { saveMedicationLocal, saveMedicationServer } from '../../../store/appt.actions';
import { checkMedicationTimeIsInvalid } from '../../../utils/feedingMedication';
import { Switch } from '../../../../../components/SwitchCase';
import { MajorPlusOutlined } from '@moego/icons-react';
import { useGetPresetMedication } from '../hooks/useGetPresetMedication';
import { useReportChange } from '../hooks/useReportChange';
import { AppointmentTaskCategory } from '@moego/api-web/moego/models/appointment/v1/appointment_task_enums';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { type MedicationValue } from '../../SelectServiceDetail/components/Medication/Medication.util';

interface EditPetMedicationProps {
  businessId?: string;
  appointmentId: string;
  petId: string;
  value: ServiceFeedingMedication;
  onSubmit?: () => void;
  onCancel?: () => void;
}
export const EditPetMedication: FC<EditPetMedicationProps> = (props) => {
  const { businessId, appointmentId, petId, value, onSubmit, onCancel } = props;
  const getPresetMedication = useGetPresetMedication(appointmentId, petId);

  const isCreatingNewAppt = appointmentId === CreateApptId;
  const isEditingExistingAppt = !isCreatingNewAppt;

  const dispatch = useDispatch();
  const [petMap] = useSelector(petMapBox);
  const [state, setState] = usePropsState<ServiceFeedingMedication>(value);
  const { medications } = state;
  const isDirty = useBool(medications.length > 0);
  const reportChange = useReportChange({ appointmentId, panelType: AppointmentTaskCategory.MEDICATION });

  const handleAddPresetMedication = () => {
    const presetMedications = getPresetMedication();
    setState({
      ...state,
      medications: presetMedications,
    });
    isDirty.open();
  };

  const handleCancel = useSerialCallback(async () => {
    setState(value);
    isDirty.close();
    onCancel?.();
  });

  const handleChange = useLatestCallback((val: MedicationValue[]) => {
    isDirty.open();
    reportChange();
    setState({
      ...state,
      medications: val,
    });
  });

  const handleSave = useSerialCallback(async () => {
    const isInValid = checkMedicationTimeIsInvalid(medications);
    if (isInValid) {
      const petName = petMap.mustGetItem(+(petId || ID_ANONYMOUS)).petName;
      const text = petName ? `${petName}'s medication time is required.` : 'Medication time is required.';

      toastApi.error(text);
      return;
    }
    const newValue = {
      ...omit(value, 'feedings', 'medications'),
      medications,
    };
    if (isEditingExistingAppt) {
      await dispatch(saveMedicationServer(businessId!, appointmentId, newValue));
    } else {
      await dispatch(saveMedicationLocal(newValue));
    }
    toastApi.success('Update successfully.');
    isDirty.close();
    onSubmit?.();
  });

  const renderHeader = () => (
    <Heading size={5} className="moe-px-s moe-pt-s">
      Instructions
    </Heading>
  );

  const renderFooter = () => {
    if (isDirty.value) {
      return (
        <div className="moe-px-s moe-pb-s moe-flex moe-gap-s">
          <Button className="moe-w-[50%]" variant="secondary" onPress={handleCancel}>
            Cancel
          </Button>
          <Button
            className="moe-w-[50%]"
            isLoading={handleSave.isBusy()}
            isDisabled={handleSave.isBusy()}
            onPress={() => handleSave()}
          >
            Save
          </Button>
        </div>
      );
    }
    return null;
  };

  return (
    <Switch>
      <Switch.Case if={isDirty.value || medications.length}>
        <Medication
          containerClassName="moe-rounded-m"
          appointmentId={appointmentId}
          petId={petId}
          value={medications}
          renderHeader={renderHeader}
          renderFooter={renderFooter}
          showDeleteButton={true}
          onChange={handleChange}
        />
      </Switch.Case>
      <Switch.Case else>
        <div className="moe-py-s moe-px-xs moe-rounded-m moe-bg-neutral-sunken-0">
          <Button size="s" icon={<MajorPlusOutlined />} variant="tertiary" onPress={handleAddPresetMedication}>
            Add medication schedule
          </Button>
        </div>
      </Switch.Case>
    </Switch>
  );
};
