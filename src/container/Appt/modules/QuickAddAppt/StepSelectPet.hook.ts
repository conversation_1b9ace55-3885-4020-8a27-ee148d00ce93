import { PetDetailDateType } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useDispatch, useStore } from 'amos';
import { uniqBy } from 'lodash';
import { useEffect, useMemo } from 'react';
import { useModifyServiceVariation } from '../../../../components/ServiceApplicablePicker/hooks/useModifyServiceVariation';
import { useImperativeServicePriceDurationInfo } from '../../../../components/ServiceApplicablePicker/hooks/useServiceInfo';
import {
  type PetIdsServiceList,
  type ServiceEntry,
} from '../../../../components/ServiceApplicablePicker/types/serviceEntry';
import { generateId } from '../../../../components/ServiceApplicablePicker/utils/getDefaultService';
import { selectBDFeatureEnable } from '../../../../store/company/company.selectors';
import { ReportActionName } from '../../../../utils/reportType';
import { reportData } from '../../../../utils/tracker';
import { quickServiceToServices } from '../../components/SelectServiceDetail/SelectServiceDetail.utils';
import {
  useAddOtherCareType,
  useGetDefaultServiceItemType,
} from '../../components/SelectServiceItemType/SelectServiceItemType.hook';
import { useCalcGroomingOnlySchedule } from '../../hooks/useCalcGroomingOnlySchedule';
import { useCheckConflictAlert } from '../../hooks/useCheckConflictAlert';
import { useGetMergeServiceList } from '../../hooks/useGetMergeServiceList';
import { usePrimaryBtnText } from '../../hooks/usePrimaryBtnText';
import { useResolveStaff } from '../../hooks/useResolveStaff';
import { addApptPetService, deletePlaceholderService } from '../../store/appt.actions';
import { apptPetMapBox } from '../../store/appt.boxes';
import { matchApptServiceScene } from '../../store/appt.options';
import {
  selectApptPetInfo,
  selectApptStartAndEndTime,
  selectMainServiceInAppt,
  selectPetsServiceAddonDateInfo,
} from '../../store/appt.selectors';
import { ApptServiceScene, CreateApptId } from '../../store/appt.types';
import { useQuickAddConfig } from './hooks/useQuickAddConfig';
import { CreateApptRouteName, useCreateApptRouterContext } from './QuickAddApptDrawer.router';
import { type UseItemTypeOptionsProps } from './StepSelectPet.type';
import { useAutoRouteToEditSchedule } from './hooks/useAutoRouteToEditSchedule';

export const useAutoFillServiceWithStaff = () => {
  const store = useStore();
  const [{ preferredStaffId, showOnCalendarStaffs }] = useQuickAddConfig();
  const { resolveStaffs } = useResolveStaff();
  const modifyServiceVariation = useModifyServiceVariation();

  return (serviceList: ServiceEntry[]) => {
    const mainService = store.select(selectMainServiceInAppt());
    const nextStaffId = resolveStaffs([mainService.staffId, preferredStaffId], showOnCalendarStaffs);

    return serviceList.map((s) => {
      const nextVariation = modifyServiceVariation(s, nextStaffId);
      return { ...s, ...nextVariation, staffId: nextStaffId };
    });
  };
};

export const useSubmitAutoFill = () => {
  const [{ allPetsStartAtSameTime, clientId }] = useQuickAddConfig();
  const calcGroomingOnlySchedule = useCalcGroomingOnlySchedule();
  const autoFillServiceWithStaff = useAutoFillServiceWithStaff();
  const drawerRouter = useCreateApptRouterContext();
  const store = useStore();
  const { isAutoRouteToEditSchedule } = useAutoRouteToEditSchedule();
  const getServicePriceDurationInfo = useImperativeServicePriceDurationInfo();

  return async (params: {
    serviceItemType: ServiceItemType;
    petIdsServiceList: PetIdsServiceList;
  }) => {
    const { petIdsServiceList } = params;
    const { startDateTime } = store.select(selectApptStartAndEndTime());
    const initPetDates = store.select(selectPetsServiceAddonDateInfo());

    await calcGroomingOnlySchedule({
      allPetsStartAtSameTime,
      customerId: String(clientId),
      petIdsServiceList: petIdsServiceList.map(({ petId, serviceList }) => ({
        petId,
        serviceList: autoFillServiceWithStaff(serviceList).map((s) => ({
          ...s,
          ...getServicePriceDurationInfo({
            petId,
            serviceId: s.serviceId,
            staffId: s.staffId,
          }),
        })),
      })),
    });

    if (isAutoRouteToEditSchedule && startDateTime) {
      // grooming only 的情况 auto fill 大多都不准（时间/staff），需要帮商家跳转到 edit schedule 手动调整时间/staff
      // 这个在 appt drawer 重构完后可以移除
      drawerRouter.go(CreateApptRouteName.EditSchedule, { initDateTime: startDateTime, initDates: initPetDates });
    } else {
      drawerRouter.go(CreateApptRouteName.Home);
    }
  };
};

export const usePresetNextDetail = () => {
  const dispatch = useDispatch();
  const getMergeServiceList = useGetMergeServiceList();

  return async (params: {
    serviceList: ServiceEntry[];
    serviceItemType: ServiceItemType;
    petIdsServiceList: PetIdsServiceList;
  }) => {
    const { serviceItemType, petIdsServiceList } = params;

    const { newServiceList: mergeServiceList, skipDetail } = getMergeServiceList({
      serviceItemType,
      petIdsServiceList,
      appointmentId: CreateApptId,
    });

    await Promise.all(
      petIdsServiceList.map(async ({ petId, serviceList }) => {
        const petServiceList = mergeServiceList.filter(({ id }) =>
          serviceList?.some(({ id: serviceId }) => serviceId === id),
        );

        if (!petServiceList.length) return;
        await dispatch(addApptPetService(petId, petServiceList));
      }),
    );

    dispatch(deletePlaceholderService());

    return skipDetail;
  };
};

export const useSubmitNextDetail = () => {
  const store = useStore();
  const dispatch = useDispatch();
  const presetNextDetail = usePresetNextDetail();
  const drawerRouter = useCreateApptRouterContext();

  return async (params: {
    serviceList: ServiceEntry[];
    serviceItemType: ServiceItemType;
    petIdsServiceList: PetIdsServiceList;
  }) => {
    const { serviceItemType, serviceList, petIdsServiceList } = params;
    const petIds = petIdsServiceList.map(({ petId }) => petId);

    // 在 dispatch 之前，先快照之前的数据，方便后续 cancel 的逻辑
    const snapshotPet = store.select(selectApptPetInfo()).toJSON();

    const skipDetail = await presetNextDetail(params);
    if (skipDetail) {
      return drawerRouter.go(CreateApptRouteName.Home);
    }

    drawerRouter.go(CreateApptRouteName.SelectServiceDetail, {
      services: quickServiceToServices(serviceList, serviceItemType),
      serviceItemType,
      petIds,
      petIdsServiceList: petIdsServiceList?.map((s) => ({
        ...s,
        serviceList: quickServiceToServices(s.serviceList, serviceItemType),
      })),
      onBack() {
        const payload = drawerRouter.mustGetParams(CreateApptRouteName.SelectPetService);
        drawerRouter.go(CreateApptRouteName.SelectPetService, {
          petIds,
          serviceList: uniqBy(serviceList, 'serviceId'), // Back 时 multiple pets 需要去重 service，否则删除 pets 后 service detail 会要求多填 service （MER-4841）
          serviceItemType,
          isMultiplePets: payload?.isMultiplePets,
        });
        dispatch(apptPetMapBox.mergeItem(CreateApptId, snapshotPet));
      },
      onConfirm() {
        drawerRouter.go(CreateApptRouteName.Home);
      },
    });
  };
};

export const useSubmitService = () => {
  const submitAutoFill = useSubmitAutoFill();
  const submitNextMore = useSubmitNextDetail();
  const getServicePriceDurationInfo = useImperativeServicePriceDurationInfo();
  const checkConflictAlert = useCheckConflictAlert();
  const store = useStore();

  return async (params: {
    serviceList: ServiceEntry[];
    serviceItemType: ServiceItemType;
    petIds: number[];
  }) => {
    const { serviceList, serviceItemType, petIds } = params;
    const mainService = store.select(selectMainServiceInAppt(CreateApptId));
    const isBD = store.select(selectBDFeatureEnable);
    const isAutoFill = matchApptServiceScene(ApptServiceScene.AutoFillStaff, {
      serviceItemType,
      mainServiceItemType: mainService.serviceItemType,
    });

    // 纯 Grooming 场景下，需要指定默认值是 DatePoint，其余情况保持默认（因为后面有一个 select service detail 的页面会指定此处默认值）
    const dateTypeOverride =
      serviceItemType === ServiceItemType.GROOMING
        ? {
            dateType: PetDetailDateType.PET_DETAIL_DATE_DATE_POINT,
          }
        : undefined;

    const petIdsServiceList = petIds.map((petId) => {
      const overrideServiceList = serviceList.map((s) => ({
        ...s,
        serviceItemType,
        ...getServicePriceDurationInfo({
          petId,
          serviceId: s.serviceId,
          staffId: s.staffId,
        }),
        id: generateId(),
        ...dateTypeOverride,
      }));

      return {
        petId,
        serviceList: overrideServiceList,
      };
    }) as PetIdsServiceList;
    const nextServiceList = petIdsServiceList.flatMap(
      ({ serviceList }) => serviceList as (ServiceEntry & { serviceItemType: ServiceItemType })[],
    );

    if (isBD) {
      if (petIds.length > 1) {
        reportData(ReportActionName.createMultiplePets, {
          petsLength: petIds.length,
        });
      } else {
        reportData(ReportActionName.createSinglePetInMultipleDrawer);
      }
    }

    if (isAutoFill) {
      await checkConflictAlert({
        appointmentId: CreateApptId,
        startDateStr: mainService.startDate,
        endDateStr: mainService.endDate,
        petIds: petIds.map(String),
      });
      await submitAutoFill({ serviceItemType, petIdsServiceList });
    } else {
      await submitNextMore({
        serviceItemType,
        serviceList: nextServiceList,
        petIdsServiceList,
      });
    }
  };
};

export const useItemTypeOptions = (params: UseItemTypeOptionsProps) => {
  const { payload, petIds, serviceItemType, onChange } = params;
  const getOtherCareType = useAddOtherCareType(CreateApptId);

  const serviceItemTypeOptions = useMemo(
    () => [...new Set(petIds.flatMap(getOtherCareType))],
    [getOtherCareType, petIds.length],
  );
  const serviceItemTypeFromSource = useGetDefaultServiceItemType();
  const primaryBtnText = usePrimaryBtnText(serviceItemType);

  const defaultServiceItemType = payload?.serviceItemType || serviceItemTypeFromSource;

  useEffect(() => {
    if (defaultServiceItemType) {
      const isValid = serviceItemTypeOptions.some((v) => v === defaultServiceItemType);
      // 如果被禁用了 或 不在范围内，那么就优先选择第一个可用的 service item type
      onChange({
        serviceItemType: isValid ? defaultServiceItemType : serviceItemTypeOptions[0],
      });
    }
  }, [defaultServiceItemType, serviceItemTypeOptions.join()]);

  return {
    serviceItemTypeOptions,
    primaryBtnText,
  };
};
