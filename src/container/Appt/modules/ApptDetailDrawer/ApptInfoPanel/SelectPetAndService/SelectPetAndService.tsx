import { Button } from '@moego/ui';
import { useSelector, useStore } from 'amos';
import classNames from 'classnames';
import React, { memo, useMemo } from 'react';
import { Condition } from '../../../../../../components/Condition';
import { DrawerHeader } from '../../../../../../components/Drawer/DrawerHeader';
import { PetPicker } from '../../../../../../components/PetPicker/PetPicker';
import { ServiceApplicablePicker } from '../../../../../../components/ServiceApplicablePicker/ServiceApplicablePicker';
import { type ServiceEntry } from '../../../../../../components/ServiceApplicablePicker/types/serviceEntry';
import { toastApi } from '../../../../../../components/Toast/Toast';
import { ScrollerProvider } from '../../../../../../layout/components/ScrollerProvider';
import { selectCurrentPermissions } from '../../../../../../store/business/role.selectors';
import { isNormal } from '../../../../../../store/utils/identifier';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../../../../utils/hooks/useSerialCallback';
import { permissionAlertsAsync } from '../../../../../../utils/message';
import { useHandleRepeatConfirm } from '../../../../../Calendar/latest/ApptCalendar/hooks/useRepeatSeriesConfirm';
import { DrawerFooter } from '../../../../../Calendar/latest/AwesomeCalendar.style';
import { SelectServiceItemType } from '../../../../components/SelectServiceItemType/SelectServiceItemType';
import { useServiceItemTypeOptions } from '../../../../components/SelectServiceItemType/SelectServiceItemType.hook';
import { useAddServiceInExistAppt } from '../../../../hooks/useAddServiceInExistAppt';
import { useBundleService } from '../../../../hooks/useBundleService';
import { useCheckConflictAlert } from '../../../../hooks/useCheckConflictAlert';
import { usePickServiceTabs } from '../../../../hooks/usePickServiceTabs';
import { usePrimaryBtnText } from '../../../../hooks/usePrimaryBtnText';
import { matchApptServiceScene } from '../../../../store/appt.options';
import { selectMainServiceInAppt, selectPetInAppt, selectPetsInAppt } from '../../../../store/appt.selectors';
import { ApptServiceScene } from '../../../../store/appt.types';
import { ApptDetailRouteName, useApptDetailRouter } from '../../ApptDetailDrawer.router';
import { useTicketActions } from '../../hooks/useTicketActions';
import { useTicketDrawerDetail } from '../../hooks/useTicketDrawerDetail';
import { useSubmitHybrid } from './hooks/useSubmitHybrid';

export interface SelectPetAndServiceProps {
  className?: string;
}

export const SelectPetAndService = memo<SelectPetAndServiceProps>(function SelectPetAndService(props) {
  const { className } = props;
  const store = useStore();
  const drawerRouter = useApptDetailRouter();
  const { ticket } = useTicketDrawerDetail();
  const {
    appointment: { id: ticketId, repeatId },
  } = ticket;
  const isRepeatSeries = isNormal(repeatId);
  const { petId, clientId, disabledPetIds, isDisabled, defaultVisible, serviceList, serviceItemType } =
    drawerRouter.mustGetParams(ApptDetailRouteName.SelectPetService);

  const [pet, permissions] = useSelector(selectPetInAppt(petId, ticketId), selectCurrentPermissions);
  const { refreshTicket } = useTicketActions();
  const handleRepeatConfirm = useHandleRepeatConfirm();
  const serviceItemTypeOptions = useServiceItemTypeOptions(petId, ticketId);
  const submitHybrid = useSubmitHybrid();
  const primaryBtnText = usePrimaryBtnText(serviceItemType, ticketId);
  const addServiceInExistAppt = useAddServiceInExistAppt(ticketId);
  const checkConflictAlert = useCheckConflictAlert();

  const prevSelectedServiceIds = useMemo(() => {
    if (!pet) return [];
    return pet.services.filter((s) => !s.associatedId).map((s) => Number(s.serviceId));
  }, [pet]);

  const tabs = usePickServiceTabs({
    serviceItemType,
    apptId: ticketId,
    serviceList,
  });

  const validatePetId = isNormal(petId);
  const validateServiceIds = serviceList.length > 0;
  const validateForm = validatePetId && validateServiceIds;

  const onCancel = () => {
    drawerRouter.back();
  };
  const setServices = useLatestCallback((serviceList: ServiceEntry[]) => {
    drawerRouter.setParams(ApptDetailRouteName.SelectPetService, { serviceList });
  });

  const onSave = useSerialCallback(async () => {
    await permissionAlertsAsync(permissions, ['canAdvancedEditTicket'], false);

    const mainService = store.select(selectMainServiceInAppt(ticketId));
    const isAutoFill = matchApptServiceScene(ApptServiceScene.AutoFillStaff, {
      mainServiceItemType: mainService.serviceItemType,
      serviceItemType,
    });

    // 这里在有 repeat 的情况下，就是应该先出现，所以判断是否前置
    // 需要 detail 的场景就是先填充表单，然后再询问是否 repeat apply
    if (!isAutoFill) {
      await submitHybrid(petId, serviceList, serviceItemType, ticketId);
    }

    if (isAutoFill) {
      // grooming only 的情况下校验冲突
      const originPetList = store.select(selectPetsInAppt(ticketId));
      await checkConflictAlert({
        appointmentId: ticketId,
        startDateStr: mainService.startDate,
        endDateStr: mainService.endDate,
        petIds: [...new Set([...originPetList.map((item) => item.petId), `${petId}`])],
      });
    }

    try {
      const repeatType = await handleRepeatConfirm(isRepeatSeries);
      const { save, saveWithAutoFill } = addServiceInExistAppt({
        petId: String(petId),
        serviceList,
        serviceItemType,
        repeatType,
      });
      if (isAutoFill) {
        await saveWithAutoFill();
      } else {
        await save();
      }
      await refreshTicket();
      toastApi.success('Updated successfully!');
      drawerRouter.back();
    } catch (e) {
      console.log(e);
      refreshTicket();
    }
  });

  const setPetId = useLatestCallback((petId: number) => {
    drawerRouter.setParams(ApptDetailRouteName.SelectPetService, { petId, serviceList: [] });
  });

  const { resolveServiceList, isLoading } = useBundleService({ clientId });

  return (
    <div className={classNames(className, 'moe-flex moe-flex-col moe-flex-1 moe-min-w-0 moe-min-h-0')}>
      <DrawerHeader title={isDisabled ? 'Select service' : 'Select pet and service'} onClick={onCancel} />
      <ScrollerProvider className="moe-flex-1 moe-min-h-0 moe-py-[20px] moe-px-[32px] moe-flex moe-flex-col">
        <PetPicker
          clientId={clientId}
          value={petId}
          onChange={setPetId}
          disabledPetIds={disabledPetIds}
          defaultVisible={defaultVisible}
          isDisabled={isDisabled}
        />

        <Condition if={validatePetId}>
          <SelectServiceItemType
            options={serviceItemTypeOptions}
            value={serviceItemType}
            onChange={(v) => {
              drawerRouter.setParams(ApptDetailRouteName.SelectPetService, { serviceItemType: v });
            }}
          />
          <Condition if={serviceItemType}>
            <ServiceApplicablePicker
              petIds={[String(petId)]}
              tabs={tabs}
              className="moe-mt-[24px]"
              serviceItemType={serviceItemType}
              value={serviceList}
              prevSelectedServiceIds={prevSelectedServiceIds}
              onChange={(services) => {
                setServices(services);
              }}
              onServiceChange={async (nextServices, extra) => {
                const bundles = await resolveServiceList({ ...extra, nextServices, petIds: [petId] });
                bundles && setServices(bundles);
              }}
              clientId={clientId}
            />
          </Condition>
        </Condition>
      </ScrollerProvider>
      <DrawerFooter className="border">
        <Button variant="secondary" className="moe-flex-1" onPress={onCancel}>
          Cancel
        </Button>
        <Button
          isLoading={onSave.isBusy() || isLoading}
          className="moe-flex-1"
          onPress={onSave}
          isDisabled={!validateForm}
        >
          {primaryBtnText}
        </Button>
      </DrawerFooter>
    </div>
  );
});
