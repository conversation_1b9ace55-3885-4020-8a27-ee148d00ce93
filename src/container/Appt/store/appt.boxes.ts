import { CalendarPeriod } from '@moego/api-web/google/type/calendar_period';
import {
  type AddOnComposite,
  type ServiceComposite,
  type ServiceDetail,
} from '@moego/api-web/moego/api/appointment/v1/appointment_api';
import {
  AppointmentPaymentStatus,
  AppointmentSource,
  AppointmentStatus,
} from '@moego/api-web/moego/models/appointment/v1/appointment_enums';
import { AppointmentNoteType } from '@moego/api-web/moego/models/appointment/v1/appointment_note_enums';
import { type AppointmentPetFeedingScheduleDef } from '@moego/api-web/moego/models/appointment/v1/appointment_pet_feeding_schedule_defs';
import { type AppointmentPetMedicationScheduleDef } from '@moego/api-web/moego/models/appointment/v1/appointment_pet_medication_schedule_defs';
import { PetDetailDateType, WorkMode } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';
import { WaitListStatus } from '@moego/api-web/moego/models/appointment/v1/wait_list_enums';
import {
  ServiceItemType,
  ServiceOverrideType,
  ServicePriceUnit,
  ServiceScopeType,
} from '@moego/api-web/moego/models/offering/v1/service_enum';
import { OrderModelPaymentStatus } from '@moego/api-web/moego/models/order/v1/order_models';
import { MoeMoney, transEnumAliasValue, type ConvertMoenyToMoeMoney } from '@moego/finance-utils';
import { OrderPaymentStatusEnum, type TypeofOrderPaymentStatus } from '@moego/finance-web-kit';
import dayjs from 'dayjs';
import { Record } from 'immutable';
import { isNil } from 'lodash';
import { type ServiceEntry } from '../../../components/ServiceApplicablePicker/types/serviceEntry';
import { DefaultQuantityPerDay } from '../../../components/ServiceApplicablePicker/utils/getDefaultService';
import { DAY_MAX_END_MINS } from '../../../store/calendarLatest/calendar.boxes';
import { CustomerTypeEnum } from '../../../store/customer/customer.boxes';
import { GroomingTicketPrepaidStatus } from '../../../store/grooming/grooming.boxes';
import { ServiceType } from '../../../store/service/category.boxes';
import { createOwnListBox, OwnList } from '../../../store/utils/OwnList';
import { createRecordMapBox } from '../../../store/utils/RecordMap';
import { isNormal, toNumber } from '../../../store/utils/identifier';
import { getTicketPaidStatus } from '../../Calendar/Grooming/interfaces';
import { TicketPaymentStatus } from '../../TicketDetail/interfaces';
import { type PetDetailParams } from '../components/RepeatSeries/adapters/ApptParamAdaptersForMisc';
import {
  type ApptAddOnInfoRecord,
  type ApptDetailInfoRecord,
  type ApptPetsListInfo,
  type ApptServiceInfoRecord,
  type ApptServiceTimeInfo,
} from './appt.types';
import {
  checkFeedingEmpty,
  checkMedicationEmpty,
  getAssociatedId,
  getOperationServiceTimes,
  ownIdUtil,
  serviceItemTypeToEnum,
  transformOperationFromServiceOperationModel,
  transformOperationList,
} from './appt.utils';
import { CustomizedServiceView } from '@moego/api-web/moego/models/offering/v1/service_models';

export interface ApptDaycareServiceExceedDuration {
  petName: string;
  exceedDurations: number;
}

const getStartDateTime = (startDate?: string, startTime?: number) => {
  if (!startDate) {
    return null;
  }
  const date = dayjs(startDate);
  return date.isValid() ? date.setMinutes(startTime || 0) : null;
};

/** 普通service */
export class ApptServiceRecord extends Record<ApptServiceInfoRecord>({
  serviceItemType: ServiceItemType.GROOMING,
  ownId: '',
  serviceId: '',
  petId: '',
  serviceName: '',
  startDate: '',
  endDate: '',
  startTime: undefined,
  endTime: undefined,
  lodgingId: undefined,
  lodgingName: undefined,
  splitLodgings: [],
  feedings: [],
  medications: [],
  // grooming service
  staffId: undefined,
  serviceTime: undefined,
  servicePrice: undefined,
  scopeTypePrice: ServiceScopeType.DO_NOT_SAVE,
  scopeTypeTime: ServiceScopeType.DO_NOT_SAVE,
  priceOverrideType: ServiceOverrideType.UNSPECIFIED,
  durationOverrideType: ServiceOverrideType.UNSPECIFIED,
  workMode: WorkMode.SEQUENCE,
  enableOperation: false,
  operations: [],
  dateType: PetDetailDateType.PET_DETAIL_DATE_DATE_POINT,
  specificDates: [],
  maxDuration: undefined,
  quantityPerDay: DefaultQuantityPerDay,
  priceUnit: ServicePriceUnit.PER_SESSION,
  id: '',
  orderLineItemId: '',
  isSlotFreeService: false,
}) {
  static createOwnId(appointmentId: string, s: string) {
    return ownIdUtil.join(appointmentId, s);
  }

  static getAppointmentIdFromOwnId(ownId: string) {
    return ownId.split(ownIdUtil.delimiter)[0];
  }

  static transformToQuickAddPetServiceEntry(record: ApptServiceInfoRecord): ServiceEntry {
    const {
      serviceId,
      serviceName,
      servicePrice,
      serviceTime,
      scopeTypePrice,
      scopeTypeTime,
      workMode,
      operations,
      staffId,
      enableOperation,
      ...others
    } = record;

    return {
      ...others,
      serviceId: toNumber(serviceId),
      serviceName,
      serviceType: ServiceType.Service,
      servicePrice: toNumber(servicePrice),
      serviceTime: toNumber(serviceTime),
      scopeTypePrice: toNumber(scopeTypePrice),
      scopeTypeTime: toNumber(scopeTypeTime),
      workMode: toNumber(workMode),
      staffId: toNumber(staffId),
      enableOperation: Boolean(enableOperation),
      operationList: operations.map((operation) => ({
        ...operation,
        staffId: toNumber(operation.staffId),
      })),
    };
  }

  static createFromPetService(serviceInfo: ServiceEntry, petId: string | number, appointmentId: string | number) {
    const {
      serviceId,
      serviceName,
      servicePrice,
      serviceTime,
      scopeTypePrice,
      scopeTypeTime,
      staffId,
      workMode,
      operationList,
      maxDuration,
      id,
      ...others
    } = serviceInfo;

    return new ApptServiceRecord({
      ...others,
      petId: String(petId),
      ownId: ApptServiceRecord.createOwnId(String(appointmentId), id),
      serviceId: String(serviceId),
      serviceName,
      staffId: isNormal(staffId) ? String(staffId) : '',
      servicePrice,
      serviceTime,
      scopeTypePrice,
      scopeTypeTime,
      workMode,
      operations: transformOperationList(operationList),
      maxDuration,
      id,
    });
  }

  static createFromServiceDetail(
    model: ServiceComposite,
    petDetail: ServiceDetail & {
      feedings?: AppointmentPetFeedingScheduleDef[];
      medications?: AppointmentPetMedicationScheduleDef[];
    },
  ) {
    const { serviceDetail, operations, splitLodgings, isSlotFreeService } = model;
    const {
      pet: { id: petId },
      feedings = [],
      medications = [],
    } = petDetail;
    const {
      serviceId,
      servicePrice,
      serviceTime,
      scopeTypePrice,
      scopeTypeTime,
      staffId,
      workMode,
      appointmentId,
      maxDuration,
      dateType,
      id,
      ...others
    } = serviceDetail;

    return new ApptServiceRecord({
      ...others,
      dateType: dateType as unknown as PetDetailDateType,
      petId,
      ownId: ApptServiceRecord.createOwnId(appointmentId, id),
      serviceId: String(serviceId),
      staffId,
      servicePrice,
      serviceTime,
      scopeTypePrice,
      scopeTypeTime,
      workMode,
      operations: transformOperationFromServiceOperationModel(operations),
      feedings,
      medications,
      maxDuration,
      id,
      splitLodgings,
      isSlotFreeService,
    });
  }

  get isSavedPrice() {
    return this.scopeTypePrice !== ServiceScopeType.DO_NOT_SAVE;
  }

  get isSavedTime() {
    return this.scopeTypeTime !== ServiceScopeType.DO_NOT_SAVE;
  }

  get serviceEndDate() {
    const { endDate, endTime } = this;
    if (!endDate) {
      return null;
    }
    return dayjs(endDate).setMinutes(endTime || 0);
  }

  get isEmptyFeeding() {
    const { feedings } = this;
    return !feedings.length || feedings.every(checkFeedingEmpty);
  }

  get isEmptyMedication() {
    const { medications } = this;
    return !medications.length || medications.every(checkMedicationEmpty);
  }

  get validateFeedings() {
    const { feedings } = this;
    return feedings.filter((feeding) => !checkFeedingEmpty(feeding));
  }

  get validateMedications() {
    const { medications } = this;
    return medications.filter((medication) => !checkMedicationEmpty(medication));
  }

  getEndTimeFromStart(start: number) {
    const { startTime, endTime } = this;
    return endTime! - startTime! + start;
  }

  /** dayjs实例 */
  get startDateTime() {
    const { startDate, startTime } = this;
    return getStartDateTime(startDate, startTime);
  }

  get lodgingCalendarEventStartAndEnd() {
    const daycareServiceEnd = dayjs(this.startDate).setMinutes(DAY_MAX_END_MINS - 1);
    const start = this.startDate ? dayjs(this.startDate).setMinutes(this.startTime || 0) : null;
    const end =
      this.serviceItemType === ServiceItemType.DAYCARE
        ? daycareServiceEnd
        : this.endDate
          ? dayjs(this.endDate).setMinutes(this.endTime || 0)
          : null;
    return { start, end };
  }
}

export const apptServiceMapBox = createRecordMapBox('appt/edit/services', ApptServiceRecord, 'ownId');

/** addOns */
export class ApptAddOnRecord extends Record<ApptAddOnInfoRecord>({
  serviceItemType: ServiceItemType.GROOMING,
  ownId: '',
  addOnId: '',
  petId: '',
  serviceName: '',
  requireDedicatedStaff: false,
  // 默认都用 datePoint，因为后台默认用的是 datePoint，以及 grooming only 场景接口上会隐式依赖这个 dateType = DATE_POINT，暂不能修改
  dateType: PetDetailDateType.PET_DETAIL_DATE_DATE_POINT,
  startDate: '',
  specificDates: [],
  startTime: undefined,
  // grooming service
  staffId: undefined,
  serviceTime: undefined,
  servicePrice: undefined,
  scopeTypePrice: ServiceScopeType.DO_NOT_SAVE,
  scopeTypeTime: ServiceScopeType.DO_NOT_SAVE,
  priceOverrideType: ServiceOverrideType.UNSPECIFIED,
  durationOverrideType: ServiceOverrideType.UNSPECIFIED,
  workMode: WorkMode.SEQUENCE,
  enableOperation: false,
  operations: [],
  associatedId: undefined,
  quantityPerDay: DefaultQuantityPerDay,
  priceUnit: ServicePriceUnit.PER_SESSION,
  endDate: '',
  endTime: undefined,
  id: '',
  orderLineItemId: '',
}) {
  static createOwnId(appointmentId: string, s: string) {
    return ownIdUtil.join(appointmentId, s);
  }

  static getAppointmentIdFromOwnId(ownId: string) {
    return ownId.split(ownIdUtil.delimiter)[0];
  }

  static transformToQuickAddPetServiceEntry(record: ApptAddOnInfoRecord): ServiceEntry {
    const {
      addOnId,
      serviceName,
      servicePrice,
      serviceTime,
      scopeTypePrice,
      scopeTypeTime,
      workMode,
      operations,
      associatedId,
      staffId,
      enableOperation,
      dateType,
      ...others
    } = record;

    return {
      ...others,
      dateType,
      serviceId: toNumber(addOnId),
      serviceName,
      serviceType: ServiceType.Addon,
      associatedId,
      servicePrice: toNumber(servicePrice),
      serviceTime: toNumber(serviceTime),
      scopeTypePrice: toNumber(scopeTypePrice),
      scopeTypeTime: toNumber(scopeTypeTime),
      workMode: toNumber(workMode),
      staffId: toNumber(staffId),
      enableOperation: Boolean(enableOperation),
      operationList: operations.map((operation) => ({
        ...operation,
        staffId: toNumber(operation.staffId),
      })),
    };
  }

  static createFromPetService(serviceInfo: ServiceEntry, petId: string | number, appointmentId: string | number) {
    const {
      serviceId,
      serviceName,
      servicePrice,
      serviceTime,
      scopeTypePrice,
      scopeTypeTime,
      staffId,
      workMode,
      operationList,
      serviceType,
      associatedId,
      dateType,
      id,
      ...others
    } = serviceInfo;

    return new ApptAddOnRecord({
      ...others,
      dateType,
      petId: String(petId),
      associatedId,
      ownId: ApptAddOnRecord.createOwnId(String(appointmentId), id),
      addOnId: String(serviceId),
      serviceName,
      staffId: isNormal(staffId) ? String(staffId) : '',
      servicePrice,
      serviceTime,
      scopeTypePrice,
      scopeTypeTime,
      workMode,
      operations: transformOperationList(operationList),
      id,
    });
  }

  static createFromServiceDetail(model: AddOnComposite, petDetail: ServiceDetail, associatedId?: string) {
    const { serviceDetail, operations } = model;
    const petId = petDetail.pet.id;
    const {
      appointmentId,
      serviceId,
      servicePrice,
      serviceTime,
      scopeTypePrice,
      scopeTypeTime,
      staffId,
      workMode,
      enableOperation,
      serviceType,
      specificDates,
      startDate,
      dateType,
      id,
      ...others
    } = serviceDetail;

    return new ApptAddOnRecord({
      ...others,
      dateType,
      petId,
      ownId: ApptAddOnRecord.createOwnId(appointmentId, id),
      addOnId: String(serviceId),
      staffId,
      specificDates,
      startDate,
      servicePrice,
      serviceTime,
      scopeTypePrice,
      scopeTypeTime,
      workMode,
      associatedId: getAssociatedId(associatedId),
      operations: transformOperationFromServiceOperationModel(operations),
      id,
    });
  }

  get isSavedPrice() {
    return this.scopeTypePrice !== ServiceScopeType.DO_NOT_SAVE;
  }

  get isSavedTime() {
    return this.scopeTypeTime !== ServiceScopeType.DO_NOT_SAVE;
  }

  get startDateTime() {
    const { startDate, startTime } = this;
    return getStartDateTime(startDate, startTime);
  }
}

export const apptAddOnMapBox = createRecordMapBox('appt/edit/add-ons', ApptAddOnRecord, 'ownId');

export class ApptPetInfo extends Record<ApptPetsListInfo>({
  appointmentId: '',
  pets: [],
}) {}

export const apptPetMapBox = createRecordMapBox('appt/detail/pets', ApptPetInfo, 'appointmentId');

export class ApptInfo extends Record<ConvertMoenyToMoeMoney<ApptDetailInfoRecord>>({
  appointmentId: '',
  appointment: {
    id: '',
    invoiceId: '',
    startAtSameTime: false,
    businessId: '',
    customerId: '',
    appointmentDate: '',
    appointmentStartTime: 0,
    appointmentEndTime: 0,
    isWaitingList: false,
    moveWaitingBy: '',
    confirmedTime: '',
    checkInTime: '',
    checkOutTime: '',
    canceledTime: '',
    status: AppointmentStatus.UNSPECIFIED,
    isBlock: false,
    bookOnlineStatus: 0,
    customerAddressId: '',
    repeatId: '',
    isPaid: AppointmentPaymentStatus.UNSPECIFIED,
    colorCode: '',
    noShow: false,
    noShowFee: 0,
    pushNotification: false,
    cancelByType: 0,
    cancelBy: '',
    confirmByType: 0,
    confirmBy: '',
    createdById: '',
    outOfArea: false,
    isDeprecate: false,
    createTime: '',
    updateTime: '',
    source: AppointmentSource.WEB,
    oldAppointmentDate: '',
    oldAppointmentStartTime: 0,
    oldAppointmentEndTime: 0,
    oldApptId: '',
    scheduleType: 0,
    sourcePlatform: '',
    readyTime: '',
    readyTimestamp: '',
    pickupNotificationSendStatus: 0,
    pickupNotificationFailedReason: '',
    statusBeforeCheckin: AppointmentStatus.UNSPECIFIED,
    statusBeforeReady: AppointmentStatus.UNSPECIFIED,
    statusBeforeFinish: AppointmentStatus.UNSPECIFIED,
    noStartTime: false,
    updatedById: '',
    companyId: '',
    isAutoAccept: false,
    waitListStatus: WaitListStatus.APPTONLY,
    appointmentEndDate: '',
    serviceTypeInclude: 0,
    isNewOrder: false,
  },
  serviceDetail: [],
  notes: [],
  customer: {
    lastAlertNote: {
      id: '',
      businessId: '',
      customerId: '',
      companyId: '',
      groomingId: '',
      note: '',
      type: AppointmentNoteType.ALERT_NOTES,
      createdAt: undefined,
      updatedAt: undefined,
      createdBy: '',
      updatedBy: '',
      isDeleted: false,
    },
    isNewCustomer: false,
    requiredSign: false,
    reviewBoosterSent: false,
    customerProfile: {
      id: '',
      companyId: '',
      preferredBusinessId: '',
      businessId: '',
      customerCode: '',
      accountId: '',
      externalId: '',
      firstName: '',
      lastName: '',
      avatarPath: '',
      email: '',
      phoneNumber: '',
      clientColor: '',
      referralSourceId: '',
      source: '',
      inactive: false,
      deleted: false,
      createdAt: '',
      updatedAt: '',
      createdBy: '',
      updatedBy: '',
      isBlockOnlineBooking: false,
      isBlockMessage: false,
      sendAutoEmail: false,
      sendAutoMessage: false,
      unconfirmedReminderBy: 0,
      isUnsubscribed: false,
      isRecurring: false,
      preferredGroomerId: '',
      preferredGroomingFrequency: {
        period: CalendarPeriod.DAY,
        value: 0,
      },
      preferredDayOfWeek: [],
      preferredTimeOfDay: {
        start: { hours: 0, minutes: 0, seconds: 0, nanos: 0 },
        end: { hours: 0, minutes: 0, seconds: 0, nanos: 0 },
      },
      shareApptStatus: 0,
      shareRangeType: 0,
      shareRangeValue: 0,
      shareApptIds: [],
      lastServiceTime: undefined,
      sendAppAutoMessage: false,
      birthday: '',
      referralSourceDesc: '',
      customerType: CustomerTypeEnum.CUSTOMER,
    },
    customerTags: [],
    customerAddress: {
      id: '',
      customerId: '',
      businessId: '',
      address1: '',
      address2: '',
      country: '',
      city: '',
      state: '',
      zipcode: '',
      coordinate: { latitude: 0, longitude: 0 },
      isPrimary: false,
      deleted: false,
    },
    customerPackages: [],
  },
  invoice: {
    invoiceId: '',
    paidAmount: 0,
    refundedAmount: 0,
    status: 0,
    totalAmount: 0,
    subTotalAmount: 0,
    paymentStatus: 0,
    outstandingBalance: 0,
  },
  noShowInvoice: {
    invoiceId: '',
    status: 0,
  },
  prePay: {
    ticketId: '',
    prePayAmount: 0,
    prePayStatus: 0,
    prePayRate: 0,
  },
  preAuth: {
    preAuthId: '',
    paymentId: '',
    ticketId: '',
    customerId: '',
    preAuthAmount: 0,
    preAuthTime: '',
    preAuthStatus: 0,
    preAuthPaymentMethod: '',
    preAuthFailedMessage: '',
    preAuthCardNumber: '',
    inBspd: false,
    preAuthFinishTime: '',
    isCapture: false,
    convenienceFee: 0,
    tipsAmount: 0,
    bookingFee: 0,
    isOpen: false,
  },
  autoAssign: {
    id: '',
    staffId: '',
    appointmentTime: 0,
  },
  waitList: {
    id: '',
    appointmentId: '',
  },
  staffs: [],
  serviceItemTypes: [],
  membershipSubscriptions: {
    membershipSubscriptions: [],
    pagination: {
      pageNum: 0,
      pageSize: 0,
      total: 0,
    },
  },
  pricingRuleApplyLogs: [],
  pricingRuleApplyLogsV2: [],
  orders: [], // new invoice 以这个为准
  paymentSummary: {
    subtotalAmount: MoeMoney.empty(),
    collectedAmount: MoeMoney.empty(),
    depositAmount: MoeMoney.empty(),
    collectedDepositAmount: MoeMoney.empty(),
    collectedTipsAmount: MoeMoney.empty(),
    useStoreCredit: false,
  },
}) {
  /** 有多少只pet */
  get petSize() {
    const { serviceDetail } = this;
    const petIds = new Set(serviceDetail.map((item) => item.pet.id));
    return petIds.size;
  }

  get moreThanOnePet() {
    return this.petSize > 1;
  }

  get customerId() {
    return this.customer.customerProfile.id;
  }

  /**
   * 为了区分 invoice v4 前后，Big invoice id 从此处获取
   */
  get bigInvoiceId() {
    return this.orders?.[0]?.id ?? this.invoice.invoiceId;
  }

  get repeatId() {
    return Number(this.appointment.repeatId);
  }

  get id() {
    return this.appointment.id;
  }

  get isNewOrder() {
    return this.appointment.isNewOrder;
  }

  get estimatedTotal() {
    if (this.isNewOrder) {
      return this.paymentSummary.subtotalAmount.valueOf();
    }
    return this.invoice.subTotalAmount;
  }

  get appointmentDate() {
    return this.appointment.appointmentDate;
  }

  get appointmentEndDate() {
    return this.appointment.appointmentEndDate;
  }

  get appointmentStartTime() {
    return this.appointment.appointmentStartTime;
  }

  get appointmentEndTime() {
    return this.appointment.appointmentEndTime;
  }

  get appointmentNights() {
    const { appointmentDate, appointmentEndDate } = this;
    if (!appointmentDate || !appointmentEndDate) {
      return 0;
    }
    return dayjs(appointmentEndDate).diff(dayjs(appointmentDate), 'day');
  }

  get noShowStatus() {
    return this.noShowInvoice.status;
  }

  get noShowFee() {
    return this.appointment.noShowFee;
  }

  get noShowInvoiceId() {
    return this.noShowInvoice.invoiceId;
  }

  get appointmentStatus() {
    return this.appointment.status;
  }

  get isOBPrepaid() {
    const {
      appointment: { bookOnlineStatus },
      prePay: { prePayStatus },
    } = this;
    const isOBPending = !!bookOnlineStatus;
    return isOBPending && prePayStatus === GroomingTicketPrepaidStatus.RequireCapture;
  }

  get paidAmount() {
    if (this.isNewOrder) {
      return this.paymentSummary.collectedAmount.valueOf();
    }
    return this.invoice.paidAmount;
  }

  get apptPayStatus() {
    const {
      appointment: { isPaid, status: appointmentStatus },
      prePay: { prePayStatus: prepayStatus, prePayAmount: prepaidAmount },
    } = this;
    const payStatusNum = getTicketPaidStatus({
      isPaid,
      status: appointmentStatus,
      paidAmount: this.paidAmount,
      prepaidAmount,
      prepayStatus,
      appointmentStatus,
    });
    const isPrepaid = payStatusNum === TicketPaymentStatus.Prepaid;
    // Prepaid 仅是ob，其他情况(ob 转换成了 ticket)都算作partial pay
    if (isPrepaid) {
      return this.isOBPrepaid ? TicketPaymentStatus.Prepaid : TicketPaymentStatus.PartialPaid;
    }
    return payStatusNum;
  }

  /**
   * invoice reinvent 把 appt 和 payment 拆开了，在非 appt finished 但 Paid 的情况下，需要展示成 Paid
   */
  get orderPaymentStatus(): TypeofOrderPaymentStatus | null {
    const paymentStatus = this.invoice?.paymentStatus ?? OrderModelPaymentStatus.UNPAID;

    return transEnumAliasValue(OrderPaymentStatusEnum, paymentStatus);
  }

  get apptPayStatusLabel() {
    const { apptPayStatus } = this;
    if (isNil(apptPayStatus)) {
      return '';
    }
    return TicketPaymentStatus.mapLabels[apptPayStatus]?.label ?? '';
  }

  get groomingTicketComments() {
    const { notes } = this;
    return notes.find((i) => i.type === AppointmentNoteType.COMMENT)?.note ?? '';
  }

  get alertNotes() {
    const { notes } = this;
    return notes.find((i) => i.type === AppointmentNoteType.ALERT_NOTES)?.note ?? '';
  }

  get petsServiceDetail() {
    const { serviceDetail } = this;
    return serviceDetail.map((pet) => {
      const {
        pet: { id, petName, breed },
        services,
        addOns,
        petCodes,
        evaluations,
      } = pet;
      return {
        petId: id,
        petName,
        breed,
        petCodes,
        evaluations,
        services: services.map(
          ({
            serviceDetail: { id, serviceName, staffId, lodgingUnitName, serviceItemType },
            isSlotFreeService,
            operations,
          }) => ({
            serviceId: id,
            serviceName,
            staffId,
            lodgingUnitName,
            operationList: operations,
            serviceType: ServiceType.Service,
            serviceItemType: serviceItemType,
            isSlotFreeService,
          }),
        ),
        addOns: addOns.map(({ serviceDetail: { id, serviceName, staffId, serviceItemType }, operations }) => ({
          serviceId: id,
          serviceName,
          staffId,
          operationList: operations,
          serviceType: ServiceType.Addon,
          serviceItemType: serviceItemType,
        })),
      };
    });
  }

  /** repeat 参数 */
  get serviceTimeRanges() {
    const list: ApptServiceTimeInfo[] = [];
    const { serviceDetail } = this;
    for (const svc of serviceDetail) {
      const { services, addOns } = svc;
      for (const s of services) {
        const {
          serviceDetail: { startTime, endTime, serviceTime, staffId },
          operations,
        } = s;
        if (Array.isArray(operations) && operations.length > 1) {
          list.push(...getOperationServiceTimes(operations));
          continue;
        }
        list.push({
          startTime,
          endTime,
          serviceTime,
          staffId: staffId ? +staffId : undefined,
        });
      }
      for (const addOn of addOns) {
        const {
          serviceDetail: { startTime, endTime, serviceTime, staffId },
          operations,
        } = addOn;
        if (Array.isArray(operations) && operations.length > 1) {
          list.push(...getOperationServiceTimes(operations));
          continue;
        }
        list.push({
          startTime,
          endTime,
          serviceTime,
          staffId: staffId ? +staffId : undefined,
        });
      }
    }

    return list;
  }

  /** 兼容老逻辑：目前后端只有 repeat 创建场景会用到，前端还有 waitlist 相关会引用 */
  get serviceList() {
    const list: PetDetailParams[] = [];
    const {
      serviceDetail,
      appointment: { id: appointmentId },
    } = this;
    for (const svc of serviceDetail) {
      const {
        services,
        addOns,
        pet: { id: petId },
      } = svc;
      const service = [...services, ...addOns];
      for (const s of service) {
        const {
          operations,
          serviceDetail: {
            lodgingId,
            scopeTypePrice,
            scopeTypeTime,
            serviceId,
            servicePrice,
            serviceTime,
            serviceType,
            staffId,
            startTime,
            workMode,
            startDate,
            endDate,
            serviceItemType,
            priceOverrideType,
            durationOverrideType,
            quantityPerDay,
          },
        } = s;
        const petSvc: PetDetailParams = {
          enableOperation: !!operations?.length,
          groomingId: +appointmentId,
          lodgingId,
          operationList: operations as unknown as PetDetailParams['operationList'],
          petId: +petId,
          scopeTypePrice,
          scopeTypeTime,
          serviceId: +serviceId,
          servicePrice,
          serviceTime,
          serviceType,
          staffId: +staffId,
          startDate,
          endDate,
          startTime,
          endTime: startTime + serviceTime,
          workMode,
          serviceItemEnum: serviceItemTypeToEnum(serviceItemType),
          star: false,
          priceOverrideType,
          durationOverrideType,
          // TODO: 这里缺少真正的 feedings / medications 数据，会导致基于现存 Appt 新建 Repeat 时，feedings / medications 不会 copy 到 repeat appt 上，先做记录，单独排期修复
          feedings: [],
          medications: [],
          quantityPerDay,
        };
        list.push(petSvc);
      }
    }
    return list;
  }

  get isRepeatAppt() {
    return isNormal(this.repeatId);
  }

  get startDateTime() {
    const { appointmentDate, appointmentStartTime } = this.appointment;
    return dayjs(appointmentDate).setMinutes(appointmentStartTime);
  }

  get hasCheckedIn() {
    return dayjs(this.appointment.checkInTime).unix() > 0;
  }

  get hasCheckedOut() {
    return dayjs(this.appointment.checkOutTime).unix() > 0;
  }
}

/** boarding/daycare/grooming 详情 */
export const apptInfoMapBox = createRecordMapBox('appt/detail/all', ApptInfo, 'appointmentId');

/**
 * 记录 appt 编辑过程中，需要屏蔽掉的 service detail id 列表
 * 背景：boarding 需要支持符合特定规则动态 auto add service / add-on 的能力，在编辑的过程中，当日期超过 setting 设置的 days / nights 时，就需要添加 add-on，不符合时就需要屏蔽掉它们
 * 问题：当前 store 的 appt 的 service detail 记录暂不支持不删除的情况下，disabled 一条记录的能力；在重构之前不做大改，故新增一个 appointmentId -> serviceDetailId[] 的 map 来记录，然后在用到的消费场景中手动过滤
 */
export const apptDisabledServiceDetailListBox = createOwnListBox('appt/edit/disabled/services', OwnList.ss());

export class ApplicableServiceMap extends Record<CustomizedServiceView>({
  id: '',
  name: '',
  price: 0,
  priceOverrideType: ServiceOverrideType.UNSPECIFIED,
  durationOverrideType: ServiceOverrideType.UNSPECIFIED,
  priceUnit: ServicePriceUnit.UNSPECIFIED,
  duration: 0,
  type: ServiceType.Service,
  categoryId: '',
  taxId: '',
  serviceItemType: ServiceItemType.UNSPECIFIED,
  description: '',
  requireDedicatedStaff: false,
  requireDedicatedLodging: false,
  inactive: false,
  maxDuration: 0,
  images: [],
  staffOverrideList: [],
  availableStaffs: {
    isAllAvailable: false,
    ids: [],
  },
  lodgingFilter: false,
  customizedLodgings: [],
  bundleServiceIds: [],
  additionalServiceRule: {
    enable: false,
    minStayLength: 0,
    applyRules: [],
  },
}) {
  getStaffOverrideVariation(staffId: number) {
    return this.staffOverrideList?.find((staff) => staff.staffId === staffId.toString());
  }
}

export const applicableServiceMapBox = createRecordMapBox('appt/applicable/services/map', ApplicableServiceMap, 'id');
