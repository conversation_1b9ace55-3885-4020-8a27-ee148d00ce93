import { PetDetailDateType } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';
import { ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useDispatch, useSelector, useStore } from 'amos';
import { uniqBy } from 'lodash';
import { useEffect } from 'react';
import { getApplicablePetService } from '../../../components/ServiceApplicablePicker/hooks/useApplicablePetServices';
import { useImperativeServicePriceDurationInfo } from '../../../components/ServiceApplicablePicker/hooks/useServiceInfo';
import { type ServiceEntry } from '../../../components/ServiceApplicablePicker/types/serviceEntry';
import { getDefaultService } from '../../../components/ServiceApplicablePicker/utils/getDefaultService';
import { currentBusinessIdBox } from '../../../store/business/business.boxes';
import { type ApptInfoPetServiceInfo } from '../../../store/calendarLatest/calendar.types';
import { type ServiceRecord, serviceMapBox } from '../../../store/service/service.boxes';
import { ID_ANONYMOUS, isNormal } from '../../../store/utils/identifier';
import { useAsyncCallback } from '../../../utils/hooks/useAsyncCallback';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { selectMainServiceInAppt } from '../store/appt.selectors';
import { CreateApptId } from '../store/appt.types';
import { getApplicableIdsByValue } from '../../../components/ServiceApplicablePicker/utils/applicableService.utils';

export function getIsBundleService(service: Partial<ServiceRecord>) {
  return (
    service.bundleServiceIds?.length &&
    (service.type === ServiceType.SERVICE ||
      (service as { serviceType: ServiceType }).serviceType === ServiceType.SERVICE) &&
    service.serviceItemType === ServiceItemType.GROOMING
  );
}

export interface GetBundleServiceParams {
  serviceItemType?: ServiceItemType;
  serviceId: number;
  petIds: number[];
  staffId: number;
}

interface UseBundleServiceParams {
  onLoadingChange?: (loading: boolean) => void;
  clientId: number;
}

export function useBundleService(params?: UseBundleServiceParams) {
  const { onLoadingChange, clientId } = params || {};
  const dispatch = useDispatch();
  const store = useStore();
  const getServicePriceDurationInfo = useImperativeServicePriceDurationInfo();
  const [businessId] = useSelector(currentBusinessIdBox);

  const getIsNeedBundleService = useLatestCallback(
    (serviceId: ServiceRecord['serviceId'], serviceItemType: ServiceItemType) => {
      const service = store.select(serviceMapBox.mustGetItem(serviceId));
      return getIsBundleService(service) && serviceItemType === ServiceItemType.GROOMING;
    },
  );

  const getBundleService = useAsyncCallback(async (params: GetBundleServiceParams) => {
    const { serviceId, petIds, staffId } = params;
    const service = store.select(serviceMapBox.mustGetItem(serviceId));
    const serviceItemType = params.serviceItemType ?? service.serviceItemType;
    const needBundleService = getIsNeedBundleService(serviceId, serviceItemType) && petIds.length === 1;
    const bundleSalesAddonServices: ServiceEntry[] = [];
    const onlyAvailable = true;

    if (needBundleService) {
      // Grooming 的 bundleService 捆绑选择 add-on
      const applicableValues = await getApplicablePetService(
        {
          serviceType: ServiceType.ADDON,
          selectedServiceIds: [String(serviceId)],
          onlyAvailable,
          petIds: petIds.map(String),
          businessId: String(businessId),
          serviceItemType,
          customerId: String(clientId),
        },
        dispatch,
      );
      const applicableAddonIds = getApplicableIdsByValue(applicableValues, ServiceType.ADDON, onlyAvailable);

      for (const bundleServiceId of service.bundleServiceIds) {
        const numberBundleServiceId = Number(bundleServiceId);
        if (!applicableAddonIds.includes(numberBundleServiceId)) {
          continue;
        }

        const bundleSalesAddon = store.select(serviceMapBox.mustGetItem(numberBundleServiceId));
        const newVal = getDefaultService({
          ...getServicePriceDurationInfo({ petId: petIds[0], serviceId: numberBundleServiceId, staffId }),
          serviceId: numberBundleServiceId,
          serviceName: bundleSalesAddon.name,
          serviceType: ServiceType.ADDON,
          requireDedicatedStaff: bundleSalesAddon.requireDedicatedStaff,
        });
        bundleSalesAddonServices.push(newVal);
      }
    }

    return bundleSalesAddonServices;
  });

  const transformBundleServiceForBD = useLatestCallback(
    (
      bundleSalesAddonServices: ServiceEntry[],
      params: Partial<Pick<ApptInfoPetServiceInfo, 'startTime' | 'startDate' | 'staffId' | 'serviceId' | 'id'>> & {
        appointmentId: number | undefined;
      },
    ) => {
      const { appointmentId, startTime, startDate, staffId, id } = params;
      const apptId = isNormal(appointmentId) ? String(appointmentId) : CreateApptId;
      const mainService = store.select(selectMainServiceInAppt(apptId));
      const isBoardingMainService = mainService.serviceItemType === ServiceItemType.BOARDING;

      return bundleSalesAddonServices.map((v) => ({
        ...v,
        ...(isBoardingMainService ? { associatedId: id } : {}),
        ...(v.requireDedicatedStaff
          ? {
              startTime,
              startDate,
              staffId: isNormal(staffId) ? staffId : ID_ANONYMOUS,
              dateType: PetDetailDateType.PET_DETAIL_DATE_DATE_POINT,
            }
          : {
              dateType: PetDetailDateType.PET_DETAIL_DATE_EVERYDAY,
            }),
        lodgingId: undefined,
        lodgingName: undefined,
        uuid: Math.random(),
        serviceItemType: ServiceItemType.GROOMING,
      }));
    },
  );

  const resolveServiceList = async (params: {
    nextServices: ServiceEntry[];
    service: ServiceEntry;
    petIds: number[];
    checked: boolean;
  }) => {
    const { checked, petIds, service, nextServices } = params;
    if (checked) {
      const isBundleService = getIsBundleService(service);
      if (isBundleService) {
        const bundleServices = await getBundleService({
          petIds,
          serviceId: service.serviceId,
          staffId: service.staffId,
        });
        return uniqBy([...nextServices, ...bundleServices], 'serviceId');
      }
    }
    return undefined;
  };

  useEffect(() => {
    onLoadingChange?.(getBundleService.loading);
  }, [getBundleService.loading]);

  return {
    getBundleService,
    getIsNeedBundleService,
    isLoading: getBundleService.loading,
    transformBundleServiceForBD,
    resolveServiceList,
  };
}
