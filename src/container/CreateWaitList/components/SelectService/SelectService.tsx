import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Button } from '@moego/ui';
import { useSelector } from 'amos';
import React, { forwardRef, memo, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { useSetState } from 'react-use';
import { Condition } from '../../../../components/Condition';
import { DrawerHeader } from '../../../../components/Drawer/DrawerHeader';
import { DrawerLayer } from '../../../../components/Drawer/DrawerLayer';
import { PetPicker } from '../../../../components/PetPicker/PetPicker';
import { ServiceApplicablePicker } from '../../../../components/ServiceApplicablePicker/ServiceApplicablePicker';
import { ServiceApplicablePickerProvider } from '../../../../components/ServiceApplicablePicker/components/ServiceOverride.context';
import { selectSceneCareType } from '../../../../store/careType/careType.selectors';
import { ID_ANONYMOUS, isNormal } from '../../../../store/utils/identifier';
import { SelectServiceItemType } from '../../../Appt/components/SelectServiceItemType/SelectServiceItemType';
import { useBundleService } from '../../../Appt/hooks/useBundleService';
import { type ResolverRef, type SelectServiceRef, type SelectServiceShowParams } from './SelectService.props';

export const SelectService = memo(
  forwardRef<SelectServiceRef>((_, ref) => {
    const resolverRef = useRef<ResolverRef>();
    const [
      { clientId, petIds, serviceList, visible, params, isMultiplePets, getTabs, selectItemTypesScene },
      setState,
    ] = useSetState<SelectServiceShowParams>({
      clientId: ID_ANONYMOUS,
    });
    const [serviceItemType, setServiceItemType] = useState<ServiceItemType | undefined>();

    const isConfirmDisabled = serviceItemType !== ServiceItemType.GROOMING ? !serviceList?.length : !petIds?.length;
    const { resolveServiceList, isLoading } = useBundleService({ clientId });

    const [careTypeOptions] = useSelector(selectSceneCareType(selectItemTypesScene));

    const handleServiceItemTypeChange = (newVal?: ServiceItemType) => {
      setServiceItemType(newVal);
    };

    useImperativeHandle(ref, () => ({
      show: (props) => {
        return new Promise((resolve, reject) => {
          resolverRef.current = { resolve, reject };
          setState({ ...props, visible: true });
          props?.serviceItemType && setServiceItemType(props.serviceItemType);
        });
      },
    }));

    const reset = () => {
      setState({ clientId: ID_ANONYMOUS, petIds: undefined, serviceList: undefined, visible: false });
    };

    const onClose = () => {
      reset();
      resolverRef.current?.reject();
      resolverRef.current = undefined;
    };

    const onConfirm = () => {
      if (isConfirmDisabled || !petIds) return;
      reset();
      resolverRef.current?.resolve({ petIds, serviceList: serviceList || [] });
      resolverRef.current = undefined;
    };

    const tabs = useMemo(() => {
      return getTabs?.(serviceItemType);
    }, [getTabs, serviceItemType]);

    useEffect(() => {
      return onClose;
    }, []);

    return (
      <DrawerLayer visible={visible}>
        <DrawerHeader title="Pet and service" onClick={onClose} className="moe-px-[24px]" />
        <div className="moe-flex-1 moe-overflow-y-auto moe-p-m">
          <PetPicker
            {...params}
            autoFocus={isMultiplePets}
            isMultiplePets={isMultiplePets}
            clientId={clientId!}
            value={isMultiplePets ? petIds! : petIds?.[0] || ID_ANONYMOUS}
            onChange={(v: number | number[]) => {
              setState({ petIds: [v].flat() as number[] });
            }}
          />
          {petIds?.length ? (
            <>
              <Condition if={!!selectItemTypesScene}>
                <SelectServiceItemType
                  options={careTypeOptions}
                  value={serviceItemType}
                  onChange={handleServiceItemTypeChange}
                />
              </Condition>
              {((isNormal(serviceItemType) && isMultiplePets) || !isMultiplePets) && (
                <ServiceApplicablePickerProvider value={{ isDisableStaffOverride: true }}>
                  <ServiceApplicablePicker
                    tabs={tabs}
                    clientId={clientId}
                    petIds={petIds.map(String)}
                    value={serviceList}
                    onChange={(v) => {
                      setState({ serviceList: v });
                    }}
                    serviceItemType={serviceItemType}
                    onServiceChange={async (nextServices, extra) => {
                      const bundles = await resolveServiceList({ ...extra, nextServices, petIds });
                      bundles && setState({ serviceList: bundles });
                    }}
                  />
                </ServiceApplicablePickerProvider>
              )}
            </>
          ) : null}
        </div>
        <div className="moe-w-full moe-flex moe-items-center moe-justify-between moe-gap-[16px] moe-p-[20px] moe-bg-[#fff] moe-shadow-[0px_4px_22px_-1px_rgba(0,0,0,0.08),0px_0px_8px_0px_rgba(0,0,0,0.04),0px_0px_0px_1px_#E6E6E6_inset]">
          <Button variant="secondary" className="moe-flex-1" onPress={onClose}>
            Cancel
          </Button>
          <Button
            variant="primary"
            className="moe-flex-1"
            onPress={onConfirm}
            isDisabled={isConfirmDisabled}
            isLoading={isLoading}
          >
            Next
          </Button>
        </div>
      </DrawerLayer>
    );
  }),
);
