import { ServiceOverrideType, ServiceScopeType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useSelector } from 'amos';
import { PetSavedPriceRecord, petSavedPriceMapBox } from '../../../store/pet/petSavedPrice.boxes';
import { serviceMapBox } from '../../../store/service/service.boxes';
import { ID_ANONYMOUS } from '../../../store/utils/identifier';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { type ServicePriceDurationInfo } from '../../Marketing/Discounts/CreateEditDiscount/hooks/useServiceInfo';
import { useGetApptServicePrice } from '../../../components/ServiceApplicablePicker/hooks/useGetApptServicePrice';

export function useImperativeServicePriceDurationInfo() {
  const [serviceMap, petSavedServiceMap] = useSelector(serviceMapBox, petSavedPriceMapBox);
  const { getApptServiceStaffOverrideVariation, getApptServicePrice } = useGetApptServicePrice();

  const getServicePriceDurationInfo = useLatestCallback(
    (petId: number, serviceId: number, staffId?: number): ServicePriceDurationInfo => {
      const service = serviceMap.mustGetItem(serviceId);
      const savedService = petSavedServiceMap.mustGetItem(PetSavedPriceRecord.ownId(petId, serviceId));

      const isSavedService = !!savedService.id;
      const isSavedPrice = isSavedService && !!savedService.isSavePrice;
      const isSavedDuration = isSavedService && !!savedService.isSaveDuration;
      const serviceStaffVariation = getApptServiceStaffOverrideVariation(serviceId, staffId ?? ID_ANONYMOUS);
      const servicePrice = getApptServicePrice(serviceId);

      return {
        servicePrice: isSavedPrice ? savedService.price : (serviceStaffVariation?.price ?? servicePrice),
        serviceTime: isSavedDuration ? savedService.duration : (serviceStaffVariation?.duration ?? service.duration),
        priceOverrideType: isSavedPrice
          ? ServiceOverrideType.CLIENT
          : serviceStaffVariation?.price
            ? ServiceOverrideType.STAFF
            : ServiceOverrideType.UNSPECIFIED,
        durationOverrideType: isSavedDuration
          ? ServiceOverrideType.CLIENT
          : serviceStaffVariation?.duration
            ? ServiceOverrideType.STAFF
            : ServiceOverrideType.UNSPECIFIED,
        scopeTypePrice: isSavedPrice ? ServiceScopeType.ONLY_THIS : ServiceScopeType.DO_NOT_SAVE,
        scopeTypeTime: isSavedDuration ? ServiceScopeType.ONLY_THIS : ServiceScopeType.DO_NOT_SAVE,
        priceUnit: service.priceUnit,
      };
    },
  );

  return getServicePriceDurationInfo;
}
