import { useDispatch, useSelector } from 'amos';
import { useAsync, useLatest } from 'react-use';
import { currentBusinessIdBox } from '../../../store/business/business.boxes';
import { setPetApplicableServices } from '../../../store/createTicket/createTicket.action';
import { type PetServiceWithCategory } from '../../../store/createTicket/createTicket.types';
import { getPetApplicableServicesAddons, getPetLastServices } from '../../../store/pet/petServices.actions';
import { ID_ANONYMOUS, isNormal } from '../../../store/utils/identifier';
import { useTicketStoreCtx } from './useTicketForm';

/** 切换biz，重新获取services */
export function useGetPetService(petId: number) {
  const [{ petsService, clientId }, updateTicketStore] = useTicketStoreCtx();
  const [businessId] = useSelector(currentBusinessIdBox);
  const dispatch = useDispatch();

  /** 切换business 或者preset data初始化 检查service是否被删除 */
  const checkPetIfExistService = useLatest((petId: number, availableServices: PetServiceWithCategory[]) => {
    const nextPetsService = petsService.map((item) => {
      const { services } = item;
      if (item.petId === petId) {
        return {
          ...item,
          services: services.map((serviceItem) => {
            const { serviceId } = serviceItem;
            /** service当前是否有效 */
            const exist = availableServices.some((item) => item.id === serviceId);
            if (exist) {
              return serviceItem;
            }
            return {
              ...serviceItem,
              serviceId: ID_ANONYMOUS,
            };
          }),
        };
      }
      return item;
    });
    updateTicketStore({
      petsService: nextPetsService,
    });
  });

  return useAsync(async () => {
    const validatePetId = isNormal(petId);
    let serviceList: PetServiceWithCategory[] = [];
    if (isNormal(businessId) && validatePetId) {
      await dispatch(getPetLastServices(petId));
      const serviceWithCategoryList = await dispatch(getPetApplicableServicesAddons({ petId, clientId }));
      dispatch(setPetApplicableServices(petId, serviceWithCategoryList));
      checkPetIfExistService.current(petId, serviceWithCategoryList);
      serviceList = serviceWithCategoryList;
    }
    return serviceList;
  }, [petId, businessId]);
}
