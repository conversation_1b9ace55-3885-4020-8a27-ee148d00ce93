import update from 'immutability-helper';
import React from 'react';
import { QuestionTooltip } from '../../../components/Popup/Tooltip';
import { type PetServiceWithCategory } from '../../../store/createTicket/createTicket.types';
import { ServiceType } from '../../../store/service/category.boxes';
import { isNormal } from '../../../store/utils/identifier';
import { useSyncPetsServiceStartTime } from '../hooks/ticketTimeSync';
import { usePetAndService } from '../hooks/useGroomingPet';
import { useInitPetService } from '../hooks/useInitPetService';
import { useMemorizeLastOperation } from '../hooks/useMemorizeLastOperation';
import { calcOperationServiceTime } from '../hooks/useServiceDuration';
import { useSyncOperationPrice } from '../hooks/useSyncOperationPrice';
import { useTicketFieldsEdit } from '../hooks/useTicketFieldsEdit';
import { useTicketStoreCtx } from '../hooks/useTicketForm';
import { type LocalSelectedServiceInfo, type ServiceOperation } from '../interfaces';
import { GroomingDuration } from './GroomingDuration';
import { GroomingOperation } from './GroomingOperation';
import { GroomingPrice } from './GroomingPrice';
import { GroomingSelectService } from './GroomingSelectService';
import { IconRemoveService } from './IconRemoveService';
import { MultiStaffDuration } from './MultiStaffDuration';
import { SavePrefered } from './SavePrefered';
import { ServicePeriodTip } from './ServicePeriodTip';
import { ServiceStaff } from './ServiceStaff';

export interface GroomingServiceProps {
  petId: number;
  serviceLocalId: number;
  loadingOptions?: boolean;
  options?: PetServiceWithCategory[];
}

export function GroomingService(props: GroomingServiceProps) {
  const { petId, serviceLocalId, loadingOptions, options } = props;
  const { pet, service, petIndex, serviceIndex, isEnableOperations } = usePetAndService(petId, serviceLocalId);
  const [{ petsService, clientId }, updateTicketStore] = useTicketStoreCtx();
  const { initOperation } = useInitPetService();
  const { memorizeLastOperation } = useMemorizeLastOperation();
  const { syncOperationPrice } = useSyncOperationPrice();
  const syncPetsServiceStartTime = useSyncPetsServiceStartTime();
  const ticketFieldsEdit = useTicketFieldsEdit();

  return (
    <div className="service-row">
      <div className="service-row-wrapper">
        <div className="service-item service-item-service">
          <label className="item-name">
            {`${service.serviceType === ServiceType.Addon ? 'Add-on' : 'Service'}`}
            <ServicePeriodTip curServiceInfo={service} serviceIndex={serviceIndex} />
          </label>
          <GroomingSelectService
            loadingOptions={loadingOptions}
            options={options}
            petIndex={petIndex}
            serviceIndex={serviceIndex}
            pet={pet}
            service={service}
          />
        </div>
        <GroomingPrice petId={petId} serviceLocalId={serviceLocalId} />
        <div className="service-item service-item-duration">
          <label className="item-name">
            Duration
            {isEnableOperations && (
              <QuestionTooltip
                overlay="Calculate based on all the operations"
                theme="black"
                overlayStyle={{ width: 136 }}
                overlayInnerStyle={{ padding: '8px 16px', fontSize: 12 }}
              />
            )}
          </label>
          {isEnableOperations ? (
            <MultiStaffDuration petId={petId} serviceLocalId={serviceLocalId} />
          ) : (
            <GroomingDuration duration={service.duration} petId={petId} serviceLocalId={serviceLocalId} />
          )}
        </div>
        <ServiceStaff petId={petId} serviceLocalId={serviceLocalId} clientId={clientId} />
        {serviceIndex > 0 ? (
          <IconRemoveService petIndex={petIndex} serviceIndex={serviceIndex} />
        ) : (
          <div className="!moe-w-[28px]" />
        )}
      </div>
      <SavePrefered
        className="!moe-mt-[16px]"
        petId={pet.petId}
        service={service}
        petIndex={petIndex}
        serviceIndex={serviceIndex}
      />
      {isEnableOperations && (
        <GroomingOperation
          className="!moe-mt-[16px]"
          petId={pet.petId}
          serviceLocalId={serviceLocalId}
          onChangeWorkMode={(workMode) => {
            const newPetsService = update(petsService, {
              [petIndex]: { services: { [serviceIndex]: { $merge: { workMode } } } },
            });
            const nextPetsService = syncPetsServiceStartTime(newPetsService);
            updateTicketStore({
              petsService: nextPetsService,
              ticketChanged: true,
            });
            ticketFieldsEdit.add('service');
            memorizeLastOperation(nextPetsService[petIndex].services[serviceIndex]);
          }}
          onChangeMainStaff={(staffId) => {
            const newPetsService = update(petsService, {
              [petIndex]: { services: { [serviceIndex]: { $merge: { staffId } } } },
            });
            updateTicketStore({
              petsService: newPetsService,
              ticketChanged: true,
            });
            ticketFieldsEdit.add('service');
          }}
          onAddOperation={() => {
            const op = initOperation({ operationName: service.serviceName });
            const newPetsService = update(petsService, {
              [petIndex]: { services: { [serviceIndex]: { operationList: { $push: [op] } } } },
            });
            const modifiedService = newPetsService[petIndex].services[serviceIndex];
            const operationDuration = calcOperationServiceTime(modifiedService);
            modifiedService.duration = operationDuration;
            // 同步price
            modifiedService.operationList = syncOperationPrice(modifiedService.operationList, modifiedService.price);
            const nextPetsService = syncPetsServiceStartTime(newPetsService);
            updateTicketStore({
              petsService: nextPetsService,
              ticketChanged: true,
            });
            ticketFieldsEdit.add('service');
            memorizeLastOperation(nextPetsService[petIndex].services[serviceIndex]);
          }}
          onRemoveOperation={(index: number) => {
            const isMainStaff = service.staffId === service.operationList![index].staffId;
            const newPetsService = update(petsService, {
              [petIndex]: { services: { [serviceIndex]: { operationList: { $splice: [[index, 1]] } } } },
            });
            const modifiedService = newPetsService[petIndex].services[serviceIndex];
            const operationDuration = calcOperationServiceTime(modifiedService);
            modifiedService.duration = operationDuration;
            if (isMainStaff) {
              // 重置staffId为operation中已经存在的staff，如果没有undefined也没有关系，提交会校验
              modifiedService.staffId = modifiedService.operationList!.find((i) => isNormal(i.staffId))?.staffId!;
            }
            // 同步price
            modifiedService.operationList = syncOperationPrice(modifiedService.operationList, modifiedService.price);
            const nextPetsService = syncPetsServiceStartTime(newPetsService);
            updateTicketStore({
              petsService: nextPetsService,
              ticketChanged: true,
            });
            ticketFieldsEdit.add('service');
            memorizeLastOperation(nextPetsService[petIndex].services[serviceIndex]);
          }}
          onModifyOperation={(index: number, operation: ServiceOperation, operationName?: keyof ServiceOperation) => {
            const serviceEmptyStaff = !isNormal(service.staffId);
            const isMainStaff = service.staffId === service.operationList![index].staffId;
            let newPetsService = update(petsService, {
              [petIndex]: {
                services: {
                  [serviceIndex]: {
                    operationList: {
                      $splice: [[index, 1, operation]],
                    },
                  },
                },
              },
            });
            const modifiedService = newPetsService[petIndex].services[serviceIndex];
            const updateService: Partial<LocalSelectedServiceInfo> = {};
            if (operationName === 'duration' || operationName === 'startTime') {
              const operationDuration = calcOperationServiceTime(modifiedService);
              updateService.duration = operationDuration;
              updateService.showSaveDuration = true;
            } else if (operationName === 'price') {
              updateService.showSavePrice = true;
            }
            if (serviceEmptyStaff || isMainStaff) {
              // 重置staffId
              updateService.staffId = operation.staffId;
            }
            // 同步price
            updateService.operationList = syncOperationPrice(modifiedService.operationList, modifiedService.price);
            newPetsService = update(newPetsService, {
              [petIndex]: {
                services: {
                  [serviceIndex]: {
                    $merge: updateService,
                  },
                },
              },
            });
            const nextPetsService = syncPetsServiceStartTime(newPetsService);
            updateTicketStore({
              petsService: nextPetsService,
              ticketChanged: true,
            });
            ticketFieldsEdit.add('service');
            memorizeLastOperation(nextPetsService[petIndex].services[serviceIndex]);
          }}
        />
      )}
    </div>
  );
}
