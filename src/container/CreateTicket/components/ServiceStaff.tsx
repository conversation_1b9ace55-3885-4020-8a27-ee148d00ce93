import classNames from 'classnames';
import React from 'react';
import IconMultiStaff from '../../../assets/svg/icon-multiple-staff.svg';
import { SvgIcon } from '../../../components/Icon/Icon';
import { MultipleStaffId } from '../../../components/ServiceStaffPicker/utils/multipleStaffId';
import { useBizRelatedRotatingValue } from '../../../utils/hooks/useBizRelatedOnce';
import { useChangeStaff } from '../hooks/useChangeStaff';
import { usePetAndService } from '../hooks/useGroomingPet';
import { useStaffConflictCheck } from '../hooks/useStaffConflictCheck';
import { ErrorTooltip } from './ErrorTooltip';
import { GroomingStaff } from './GroomingStaff';
import { IconPreferredStaff } from './IconPreferredStaff';
import { NewOperationGuide } from './NewOperationGuide';

export interface ServiceStaffProps {
  petId: number;
  serviceLocalId: number;
  clientId?: number;
}

export function ServiceStaff(props: ServiceStaffProps) {
  const { petId, serviceLocalId, clientId } = props;
  const { service, petIndex, serviceIndex, isEnableOperations } = usePetAndService(petId, serviceLocalId);
  const { changeServiceStaff } = useChangeStaff(petId, serviceLocalId);
  const [visibleNewOperationFeature, setNewTagtHidden] = useBizRelatedRotatingValue('operation-feature-new', [
    true,
    false,
  ]);
  const { showConflictErrorTip, conflictErrorMsg, conflictCheckLoading } = useStaffConflictCheck(service);
  const isFirstPetFirstService = petIndex === 0 && serviceIndex === 0;

  return (
    <ErrorTooltip
      theme="warning"
      placement="bottomRight"
      visible={showConflictErrorTip}
      errorMsg={conflictErrorMsg}
      className={classNames(
        'service-item service-item-staff select-staff-item !moe-min-w-min',
        showConflictErrorTip ? '!moe-mb-[5px]' : undefined,
      )}
    >
      <>
        <label className="item-name">
          Staff
          {isFirstPetFirstService && <NewOperationGuide visibleNewOperationFeature={visibleNewOperationFeature} />}
        </label>
        <GroomingStaff
          loading={conflictCheckLoading}
          value={isEnableOperations ? MultipleStaffId : service.staffId}
          petId={petId.toString()}
          serviceId={service.serviceId}
          serviceType={service.serviceType}
          onChange={changeServiceStaff}
          onDropdownVisibleChange={(visible) => {
            if (visible) {
              setNewTagtHidden();
            }
          }}
          clientId={clientId}
        />
        {isEnableOperations ? (
          <div className="!moe-absolute !moe-top-[27px] !moe-left-[10px]">
            <SvgIcon src={IconMultiStaff} size={18} />
          </div>
        ) : (
          <IconPreferredStaff staffId={service.staffId} className="!moe-top-[31px]" />
        )}
      </>
    </ErrorTooltip>
  );
}
