import { ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Checkbox, Text, cn } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import { Select } from 'antd';
import { type RenderDOMFunc } from 'rc-select/es/interface';
import React from 'react';
import IconMultiStaff from '../../../assets/svg/icon-multiple-staff.svg';
import { Condition } from '../../../components/Condition';
import { SvgIcon } from '../../../components/Icon/Icon';
import { Tooltip } from '../../../components/Popup/Tooltip';
import { PricingUpgradeModal } from '../../../components/Pricing/PricingUpgradeModal';
import { usePricingEnableUpgrade } from '../../../components/Pricing/pricing.hooks';
import { TagServiceDuration } from '../../../components/ServiceApplicablePicker/components/TagService/TagServiceDuration';
import { TagServicePrice } from '../../../components/ServiceApplicablePicker/components/TagService/TagServicePrice';
import { AssignMultiStaffTip } from '../../../components/ServiceStaffPicker/components/AssignMultiStaffTip';
import { useMultiStaffPermission } from '../../../components/ServiceStaffPicker/hooks/useMultiStaffPermission';
import {
  type ServiceVariation,
  useServiceApplicableStaffList,
} from '../../../components/ServiceStaffPicker/hooks/useServiceApplicableStaffList';
import { MultipleStaffId } from '../../../components/ServiceStaffPicker/utils/multipleStaffId';
import { type IApptStaff } from '../../../config/interface';
import { CTTestIds } from '../../../config/testIds/createTicket';
import { getAccountCompanyList } from '../../../store/company/company.actions';
import { ticketOptionStaffsBox } from '../../../store/createTicket/createTicket.boxes';
import { printFullName } from '../../../store/customer/customer.boxes';
import { staffMapBox } from '../../../store/staff/staff.boxes';
import { ID_ANONYMOUS, isNormal } from '../../../store/utils/identifier';
import { useBool } from '../../../utils/hooks/useBool';
import { ArrowSuffixIcon } from './ArrowSuffixIcon';

const { Option } = Select;

export interface GroomingStaffProps {
  value?: number;
  petId?: string;
  serviceId?: number;
  serviceType?: ServiceType;
  loading?: boolean;
  onChange?: (v: number, serviceVariation?: ServiceVariation) => void;
  onDropdownVisibleChange?: (b: boolean) => void;
  staffs?: IApptStaff[];
  getPopupContainer?: RenderDOMFunc;
  clientId?: number;
}

export function GroomingStaff(props: GroomingStaffProps) {
  const { value, serviceId, petId, serviceType, onChange, onDropdownVisibleChange, loading, staffs, clientId } = props;
  const [staffLists] = useSelector(ticketOptionStaffsBox);
  const optionStaffs = staffs ?? staffLists;
  const dispatch = useDispatch();
  const pricingUpgradeModalVisible = useBool();
  const showUpgradeTip = useBool();
  const onlyAvailableStaff = useBool(true);

  const [currentStaff] = useSelector(staffMapBox.mustGetItem(value ?? ID_ANONYMOUS));
  const currentStaffName = printFullName(currentStaff.firstName, currentStaff.lastName);
  const { access, ...restProps } = usePricingEnableUpgrade('operation');
  const { hasMultiStaffPermission, showUpgradeForMultiStaffTip } = useMultiStaffPermission();
  // staff有2个以上，才能开启Multi staff
  const enableMultiple = hasMultiStaffPermission && (optionStaffs.length > 1 || value === MultipleStaffId);
  const showMultiStaffOption = enableMultiple || showUpgradeForMultiStaffTip;
  const isService = serviceType === ServiceType.SERVICE;

  const {
    loading: isStaffLoading,
    availableStaffList,
    unavailableStaffList,
    getServiceVariation,
  } = useServiceApplicableStaffList({
    serviceId,
    petId,
    selectedStaffId: value,
    filterStaffIds: optionStaffs.map((staff) => staff.staffId),
    enableServiceAvailableStaff: isService,
    clientId,
  });

  const staffList = onlyAvailableStaff.value ? availableStaffList : availableStaffList.concat(unavailableStaffList);

  return (
    <>
      <Select
        open={showUpgradeTip.value === true ? true : undefined}
        loading={loading || isStaffLoading}
        placeholder=""
        className="!moe-w-full !moe-min-w-[174px] moe-max-w-[300px]"
        value={value === ID_ANONYMOUS ? undefined : value}
        optionLabelProp="selectedLabel"
        onChange={(nextStaffId) => {
          const serviceVariation = getServiceVariation(nextStaffId);
          onChange?.(nextStaffId, serviceVariation);
        }}
        virtual={false}
        onDropdownVisibleChange={(visible) => {
          onDropdownVisibleChange?.(visible);
        }}
        dropdownClassName={cn('select-multiple-staff-dropdown', {
          'select-multiple-staff-dropdown-service': isService,
        })}
        suffixIcon={<ArrowSuffixIcon />}
        getPopupContainer={(triggerNode) => triggerNode.parentNode}
        data-testid={CTTestIds.GroomingSelectStaffOption}
        dropdownRender={(menu) => (
          <>
            {menu}
            <div className="moe-flex moe-justify-end moe-p-xs moe-border-t moe-border-divider">
              <Condition if={showMultiStaffOption}>
                <Tooltip
                  disabled={!showUpgradeForMultiStaffTip}
                  onVisibleChange={showUpgradeTip.as}
                  overlay={<AssignMultiStaffTip onUpgrade={() => pricingUpgradeModalVisible.open()} />}
                  width={258}
                  placement="left"
                  align={{ offset: [-4, 0] }}
                >
                  <div
                    className="moe-flex-1 assign-multi-staff !moe-flex !moe-items-center !moe-gap-x-[5px] moe-cursor-pointer"
                    title="Assign to Multi-staff"
                    onClick={() => {
                      if (showUpgradeForMultiStaffTip) {
                        return;
                      }
                      onChange?.(MultipleStaffId);
                    }}
                  >
                    <SvgIcon src={IconMultiStaff} size={18} />
                    Assign to Multi-staff
                  </div>
                </Tooltip>
              </Condition>
              <Condition if={showMultiStaffOption && isService}>
                <div className="moe-h-[20px] moe-mx-xs moe-border-r moe-border-divider" />
              </Condition>
              <Condition if={isService}>
                <Checkbox isSelected={onlyAvailableStaff.value} onChange={onlyAvailableStaff.as}>
                  <Text variant="small" className="moe-text-primary">
                    Only show applicable staff
                  </Text>
                </Checkbox>
              </Condition>
            </div>
          </>
        )}
      >
        {isNormal(value) && (
          <Option
            key={`must_exist_${value}`}
            value={value}
            // incase staff id not in the staff option list
            className="!moe-hidden"
            selectedLabel={<span className="preferred-staff-name">{currentStaffName}</span>}
          >
            {currentStaffName}
          </Option>
        )}
        {staffList.map((staff) => {
          const { staffId, servicePrice, serviceDuration, priceOverrideType, durationOverrideType, isDisabled } = staff;
          const staffName = printFullName(staff.firstName, staff.lastName);
          return (
            <Option
              value={Number(staffId)}
              key={staffId}
              selectedLabel={<span className="preferred-staff-name">{staffName}</span>}
            >
              <div className="moe-flex moe-justify-between">
                <Text variant="small" className="moe-text-primary moe-truncate">
                  {staffName}
                </Text>
                <Condition if={isService && !isDisabled}>
                  <div className="moe-flex moe-gap-xs moe-flex-shrink-0 moe-ml-8px-150">
                    <TagServicePrice price={servicePrice} overrideType={priceOverrideType} />
                    <TagServiceDuration duration={serviceDuration} overrideType={durationOverrideType} />
                  </div>
                </Condition>
              </div>
            </Option>
          );
        })}
        {showMultiStaffOption && (
          <Option
            value={MultipleStaffId}
            // hidden multi-staff option, because will show it in dropdownRender footer
            className="!moe-hidden"
            selectedLabel={<span className="preferred-staff-name">Assign to Multi-staff</span>}
          >
            Assign to Multi-staff
          </Option>
        )}
      </Select>
      {!access && (
        <PricingUpgradeModal
          visible={pricingUpgradeModalVisible.value}
          onClose={async () => {
            pricingUpgradeModalVisible.close();
            await dispatch(getAccountCompanyList());
          }}
          {...restProps}
        />
      )}
    </>
  );
}
