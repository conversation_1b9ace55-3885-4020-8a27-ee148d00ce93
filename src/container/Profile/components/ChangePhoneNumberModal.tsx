import { useSerialCallback } from '@moego/finance-utils';
import { Modal, Form, type ModalProps, useForm, Button, Input } from '@moego/ui';
import { TextInput } from '@moego/ui/dist/esm/components/Input/TextInput';
import React, { useEffect } from 'react';
import { toastApi } from '../../../components/Toast/Toast';
import { BFFAuthnClient } from '../../../middleware/bff';
import { useCountDown } from '../../../utils/hooks/useCountDown';
import { ReportActionName } from '../../../utils/reportType';
import { reportData } from '../../../utils/tracker';

interface ChangePhoneNumberForm {
  e164Number: string; // e164 number
  token: string;
  code: string;
}

const REFRESH_COUNTDOWN_SEC = 60;

export const ChangePhoneNumberModal = ({
  isOpen,
  onClose,
  regionCode,
}: Pick<ModalProps, 'isOpen' | 'onClose'> & { regionCode: string }) => {
  const form = useForm<ChangePhoneNumberForm>({ mode: 'onBlur' });
  const countdown = useCountDown();
  const remainSec = Math.floor((countdown?.remain || 0) / 1000);

  const handleGetCode = useSerialCallback(async () => {
    reportData(ReportActionName.twofaPhoneNumberChangeSendCodeClick);
    if (!form.getValues('e164Number')) {
      toastApi.error('Please enter your phone number.');
      return;
    }
    try {
      const res = await BFFAuthnClient.sendUpdatePhoneNumberVerificationCode(
        {
          regionCode: regionCode.toUpperCase(),
          e164Number: form.getValues('e164Number'),
        },
        { autoToast: false },
      );
      form.setValue('token', res.verificationToken);
      toastApi.success('Verification code sent. Please check your SMS.');
      form.setFocus('code');
    } catch (e) {
      toastApi.error(
        e?.data?.message || 'Failed to send verification code. Please check the phone number and try again.',
      );
      return;
    }

    countdown.start(REFRESH_COUNTDOWN_SEC * 1000);
  });

  const submit = useSerialCallback(async (values: ChangePhoneNumberForm) => {
    reportData(ReportActionName.twofaPhoneNumberChangeConfirmClick);
    try {
      await BFFAuthnClient.changePhoneNumberFactor(
        {
          regionCode: regionCode.toUpperCase(),
          e164Number: values.e164Number,
          verificationToken: values.token,
          verificationCode: values.code,
        },
        { autoToast: false },
      );
    } catch (e) {
      toastApi.error(
        e?.data?.message || 'Failed to change phone number. Please check the verification code and try again.',
      );
      return;
    }
    toastApi.success('Phone number changed successfully!');
    onClose?.();
  });

  const handleSubmit = () => {
    return form.handleSubmit(submit, (errors) => console.log(errors))();
  };

  useEffect(() => {
    if (isOpen) {
      form.resetField('e164Number');
      form.resetField('code');
    }
  }, [form, isOpen]);

  return (
    <Modal
      isOpen={isOpen}
      title="Change your phone number"
      onClose={onClose}
      onConfirm={handleSubmit}
      confirmButtonProps={{
        isLoading: submit.isBusy(),
        'data-dd-action-name': ReportActionName.twofaPhoneNumberChangeConfirmClick,
      }}
      autoCloseOnConfirm={false}
      classNames={{ container: 'moe-w-[640px]' }}
    >
      <Form form={form} footer={null}>
        <Form.Item name="phone">
          <div className="moe-flex moe-gap-2 moe-items-end">
            <Input.Phone
              className="rr-block rr-ignore"
              label="New phone number"
              isRequired
              onChange={(value) => form.setValue('e164Number', value)}
              country={regionCode}
              canSelectCountry={false}
              autoFocus
              classNames={{ base: 'moe-flex-1' }}
            />
            <Button
              variant="secondary"
              onPress={handleGetCode}
              isLoading={handleGetCode.isBusy()}
              isDisabled={remainSec > 0}
              data-dd-action-name={ReportActionName.twofaPhoneNumberChangeSendCodeClick}
            >
              {remainSec > 0 ? `Re-send (${remainSec}s)` : 'Send SMS'}
            </Button>
          </div>
        </Form.Item>
        <Form.Item name="code" rules={{ required: 'Please input the verification code in SMS' }}>
          <TextInput label="Verification code" isRequired />
        </Form.Item>
      </Form>
    </Modal>
  );
};
