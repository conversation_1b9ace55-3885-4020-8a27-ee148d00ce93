import { useSerialCallback, isNormal } from '@moego/finance-utils';
import { computeUnits } from '@moego/reporting';
import { TextInput } from '@moego/ui/dist/esm/components/Input/TextInput';
import { useDispatch, useSelector } from 'amos';
import { Typography, Form, Button, useForm } from '@moego/ui';
import dayjs from 'dayjs';
import React, { useEffect } from 'react';
import { toastApi } from '../../../components/Toast/Toast';
import { updateAccountInfo } from '../../../store/account/account.actions';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { useBool } from '../../../utils/hooks/useBool';
import { useEnableMfa } from '../../../utils/hooks/useEnableMfa';
import { useAccountPageState } from '../../Account/AccountInfo/AccountInfo';
import { ReportActionName } from '../../../utils/reportType';
import { reportData } from '../../../utils/tracker';
import { ChangePasswordModal } from './ChangePasswordModal';
import { SetupPhoneNumberModal } from './SetupPhoneNumberModal';
import { ChangePhoneNumberModal } from './ChangePhoneNumberModal';
import { useMfaFactors } from './useMfaFactors';

export const SettingsPanel = () => {
  return (
    <div>
      <AccountBasicInfo />
      <AccountSecurity className="moe-mt-l" />
    </div>
  );
};

type Account = {
  firstName: string;
  lastName: string;
};

const AccountBasicInfo = () => {
  const { account } = useAccountPageState();
  const form = useForm<{
    firstName: string;
    lastName: string;
  }>({ mode: 'all' });

  const dispatch = useDispatch();
  const submit = useSerialCallback(async (values: Account) => {
    await dispatch(updateAccountInfo({ ...values }));
    toastApi.success('Profile updated successfully!');
    form.reset(values);
    form.trigger;
  });

  const handleSubmit = useSerialCallback(async () => {
    await form.handleSubmit(submit, (errors) => console.log(errors))();
  });

  useEffect(() => {
    if (!isNormal(account.id)) return;
    form.reset({
      firstName: account.firstName,
      lastName: account.lastName,
    });
  }, [account.id, account.firstName, account.lastName, form]);

  return (
    <div>
      <Typography.Heading size="3">Basic info</Typography.Heading>
      <Form
        form={form}
        className="moe-mt-m"
        footer={
          <div className="moe-flex">
            <Button
              isDisabled={!form.formState.isValid || !form.formState.isDirty}
              onPress={handleSubmit}
              isLoading={handleSubmit.isBusy()}
            >
              Save
            </Button>
          </div>
        }
      >
        {/* <Form.Item name="avatarPath" rules={{ required: true }}>
          <AvatarUpload src="https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/1697453729cb7cea76b2914434a91333c0b604c7a4.png?name=Rectangle%**********.png" />
        </Form.Item> */}
        <Form.Item name="firstName" rules={{ required: true }}>
          <TextInput label="First name" isRequired />
        </Form.Item>
        <Form.Item name="lastName" rules={{ required: true }}>
          <TextInput label="Last name" isRequired />
        </Form.Item>
        {/* <Form.Item name="email" rules={{ required: true }}>
          <TextInput label="Email address" isRequired />
        </Form.Item>
        <Form.Item name="phone" rules={{ required: true }}>
          <TextInput label="Phone number" isRequired />
        </Form.Item> */}
      </Form>
    </div>
  );
};

const AccountSecurity = ({ className }: { className?: string }) => {
  const { account } = useAccountPageState();
  const days = dayjs().diff(dayjs(account.passwordUpdateTime * 1000), 'day');

  const [business] = useSelector(selectCurrentBusiness());
  const enableMfaLogin = useEnableMfa(business.countryAlpha2Code);
  const showChangePasswordModal = useBool();
  const showSetupPhoneNumberModal = useBool();
  const showChangePhoneNumberModal = useBool();
  const { mfaFactors, phoneNumberMfa } = useMfaFactors();
  const openSetupPhoneNumberModal = () => {
    reportData(ReportActionName.twofaPhoneNumberSetupClick);
    showSetupPhoneNumberModal.open();
  };
  const openChangePhoneNumberModal = () => {
    reportData(ReportActionName.twofaPhoneNumberChangeClick);
    showChangePhoneNumberModal.open();
  };
  const onPhoneNumberMfaAdd = () => {
    showSetupPhoneNumberModal.close();
    mfaFactors.refetch();
  };
  const onPhoneNumberMfaChange = () => {
    showChangePhoneNumberModal.close();
    mfaFactors.refetch();
  };

  const renderChangePassword = () => (
    <div className="moe-flex moe-justify-between">
      <div>
        <Typography.Heading size="6">Password</Typography.Heading>
        <Typography.Text variant="small" className="moe-text-tertiary">
          Last updated {days} {computeUnits(days, 'day')[1]} ago
        </Typography.Text>
      </div>
      <Button variant="secondary" onPress={showChangePasswordModal.open}>
        Change password
      </Button>
      <ChangePasswordModal isOpen={showChangePasswordModal.value} onClose={showChangePasswordModal.close} />
    </div>
  );

  const renderChangePhoneNumber = () => (
    <div className="moe-flex moe-justify-between">
      <div>
        <Typography.Heading size="6">Phone number verification</Typography.Heading>
        <Typography.Text variant="small" className="moe-text-tertiary">
          {phoneNumberMfa
            ? `Security verification phone number: ${phoneNumberMfa.details.value?.e164Number}`
            : 'Please setup your phone number for security verification'}
        </Typography.Text>
      </div>
      {phoneNumberMfa ? (
        <Button
          variant="secondary"
          onPress={openChangePhoneNumberModal}
          data-dd-action-name={ReportActionName.twofaPhoneNumberChangeClick}
        >
          Change phone number
        </Button>
      ) : (
        <Button
          variant="secondary"
          onPress={openSetupPhoneNumberModal}
          data-dd-action-name={ReportActionName.twofaPhoneNumberSetupClick}
        >
          Setup phone number
        </Button>
      )}
      <SetupPhoneNumberModal
        isOpen={showSetupPhoneNumberModal.value}
        onClose={onPhoneNumberMfaAdd}
        regionCode={business.countryAlpha2Code.toLowerCase()}
      />
      <ChangePhoneNumberModal
        isOpen={showChangePhoneNumberModal.value}
        onClose={onPhoneNumberMfaChange}
        regionCode={business.countryAlpha2Code.toLowerCase()}
      />
    </div>
  );

  return (
    <div className={className}>
      <Typography.Heading size="3">Security</Typography.Heading>
      <div className="moe-mt-m">{renderChangePassword()}</div>
      {enableMfaLogin ? <div className="moe-mt-m">{renderChangePhoneNumber()}</div> : null}
    </div>
  );
};
