import { <PERSON><PERSON>, Head<PERSON> } from '@moego/ui';
import { useSelector } from 'amos';
import copy from 'copy-to-clipboard';
import React, { memo, useMemo } from 'react';
import SvgPricingIconCrownSvg from '../../../../../assets/svg/pricing-icon-crown.svg';
import { SvgIcon } from '../../../../../components/Icon/Icon';
import { UpgradePopover } from '../../../../../components/Pricing/UpgradePopover';
import { toastApi } from '../../../../../components/Toast/Toast';
import { selectPricingPermission } from '../../../../../store/company/company.selectors';
import { useEnableFeature } from '../../../../../store/metadata/featureEnable.hooks';
import { META_DATA_KEY_LIST } from '../../../../../store/metadata/metadata.config';
import { selectOnlineBookingLatestPreference } from '../../../../../store/onlineBooking/onlineBookingPreference.selectors';
import { useBool } from '../../../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { EmbedDrawer } from '../BookingSite/components/EmbedDrawer';
import { GenerateQRCodeDrawer } from '../BookingSite/components/GenerateQRCodeDrawer';
import { LinkToolsConfigList, LinkToolsWaringConfig } from './Overview.config';

export const LinkTools = memo(() => {
  const [preference, pricingPermission] = useSelector(selectOnlineBookingLatestPreference, selectPricingPermission);
  const { enable: isPackageEnabled } = useEnableFeature(META_DATA_KEY_LIST.PackageEnabled);
  const embedDrawerVisible = useBool();
  const qrCodeDrawerVisible = useBool();

  const enableLinkEntryList = useMemo(() => {
    return LinkToolsConfigList.values.filter((val) => {
      if (val === LinkToolsConfigList.PackageLink) {
        return isPackageEnabled;
      }
      if (val === LinkToolsConfigList.MembershipLink) {
        return preference.supportMembership;
      }
      return true;
    });
  }, [preference.supportMembership, isPackageEnabled]);

  const handleAction = useLatestCallback((configValue: number) => {
    switch (configValue) {
      case LinkToolsConfigList.BookingLink:
        if (copy(preference.bookingLink)) {
          toastApi.success('Book now link copied!');
        }
        return;
      case LinkToolsConfigList.BookingEmbed:
        embedDrawerVisible.open();
        return;
      case LinkToolsConfigList.BusinessQRCode:
        qrCodeDrawerVisible.open();
        return;
      case LinkToolsConfigList.MembershipLink:
        if (copy(preference.membershipLink)) {
          toastApi.success('Membership link copied!');
        }
        return;
      case LinkToolsConfigList.PackageLink:
        if (copy(preference.packageLink)) {
          toastApi.success('Package link copied!');
        }
        return;
      default:
        return;
    }
  });

  // const handleWarningAction = useSerialCallback(async () => {
  //   if (preference.isPublished) {
  //     await dispatch(updateOnlineBookingPreference({ isEnable: 1 }));
  //   } else {
  //     await dispatch(updateOnlineBookingLatestPreference({ isPublished: true }));
  //   }
  // });

  const renderWarning = () => {
    if (preference.isPublished && preference.bookingEnable) {
      return null;
    }
    const configValue = preference.isPublished
      ? LinkToolsWaringConfig.EnableOnlineBooking
      : LinkToolsWaringConfig.PublishSite;
    const config = LinkToolsWaringConfig.mapLabels[configValue];

    return (
      <div
        className={`!moe-flex !moe-items-center !moe-justify-between !moe-mx-[-24px] !moe-mt-[-24px] !moe-mb-[24px] !moe-px-[24px] !moe-py-[8px] moe-text-warning`}
      >
        {config.title}
      </div>
    );
  };

  return (
    <div>
      <Heading size="3" className="moe-mb-[16px]">
        Link tools
      </Heading>
      {renderWarning()}
      <div className="moe-flex moe-flex-col moe-gap-y-[13px]">
        {enableLinkEntryList.map((value) => {
          const { icon, title, desc, actionText } = LinkToolsConfigList.mapLabels[value];
          const qrCodeDisabled =
            value === LinkToolsConfigList.BusinessQRCode && !pricingPermission.enable.has('qrCode');
          const disabled =
            !preference.isPublished || (!preference.bookingEnable && value === LinkToolsConfigList.BookingLink);
          const opacityClass = disabled ? '!moe-opacity-40' : '!moe-opacity-100';
          return (
            <div
              key={value}
              className={
                'moe-flex moe-items-center moe-justify-between moe-gap-[16px] moe-py-spacing-s moe-pl-spacing-s moe-rounded-spacing-xs moe-border moe-border-divider moe-border-solid moe-pr-m'
              }
            >
              <div className={`!moe-flex ${opacityClass} moe-items-start`}>
                {icon}
                <div>
                  <div className="moe-mb-xxs">
                    {qrCodeDisabled && (
                      <SvgIcon src={SvgPricingIconCrownSvg} color="#ffd029" className="!moe-mr-[4px]" />
                    )}
                    <Heading size="5">{title}</Heading>
                  </div>
                  <div className="moe-text-sm-20 moe-text-tertiary moe-font-regular max-w-[496px]">{desc}</div>
                </div>
              </div>
              <UpgradePopover
                permission="qrCode"
                allowed={value !== LinkToolsConfigList.BusinessQRCode}
                childrenWrapperClassName="!moe-w-auto"
                overrideEvent="onPress"
              >
                <Button
                  variant="tertiary"
                  onPress={() => handleAction(value)}
                  isDisabled={disabled}
                  className="moe-flex-shrink-0"
                  classNames={{
                    content: `${opacityClass}`,
                  }}
                >
                  {actionText}
                </Button>
              </UpgradePopover>
            </div>
          );
        })}
      </div>
      <EmbedDrawer visible={embedDrawerVisible.value} onClose={embedDrawerVisible.close} />
      <GenerateQRCodeDrawer visible={qrCodeDrawerVisible.value} onClose={qrCodeDrawerVisible.close} />
    </div>
  );
});
