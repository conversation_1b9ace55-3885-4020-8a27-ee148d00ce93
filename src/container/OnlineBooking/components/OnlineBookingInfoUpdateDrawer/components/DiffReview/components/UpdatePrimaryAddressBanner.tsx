import React, { memo } from 'react';
import { Alert } from '@moego/ui';
import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { GrowthBookFeatureList } from '../../../../../../../utils/growthBook/growthBook.config';
import { useQuery } from '../../../../../../../store/utils/useQuery';
import { listPricingRules } from '../../../../../../../store/pricingRule/actions/private/pricingRule.actions';
import { RuleType } from '@moego/api-web/moego/models/offering/v2/pricing_rule_enums';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';

export interface UpdatePrimaryAddressBannerProps {}

export const UpdatePrimaryAddressBanner = memo<UpdatePrimaryAddressBannerProps>(() => {
  const isEnableVaryPricingByZone = useFeatureIsOn(GrowthBookFeatureList.EnableVaryPricingByZone);

  const { value, loading } = useQuery(
    listPricingRules({
      filter: {
        isActive: true,
        ruleTypes: [RuleType.ZONE],
        careTypes: [ServiceItemType.GROOMING],
        ids: [],
        excludeIds: [],
      },
      pagination: {
        pageNum: 1,
        pageSize: 10,
      },
    }),
    isEnableVaryPricingByZone,
  );

  if (loading || !value || (value && value.pagination.total === 0)) {
    return null;
  }

  return (
    <Alert
      isRounded
      description="Service price will be calculated based on the address of the specific appointment, regardless of whether the primary address is updated."
    />
  );
});

UpdatePrimaryAddressBanner.displayName = 'UpdatePrimaryAddressBanner';
