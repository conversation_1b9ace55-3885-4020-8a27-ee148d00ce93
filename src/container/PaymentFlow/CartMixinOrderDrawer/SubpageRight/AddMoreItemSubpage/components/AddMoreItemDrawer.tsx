import { type ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Drawer, type DrawerProps } from '@moego/ui';
import { useSelector } from 'amos';
import React, { useMemo, useState } from 'react';
import { ServiceApplicablePicker } from '../../../../../../components/ServiceApplicablePicker/ServiceApplicablePicker';
import { type ServiceEntry } from '../../../../../../components/ServiceApplicablePicker/types/serviceEntry';
import { useBundleService } from '../../../../../Appt/hooks/useBundleService';
import { usePickServiceTabs } from '../../../../../Appt/hooks/usePickServiceTabs';
import { selectApptInfo, selectPetInAppt } from '../../../../../Appt/store/appt.selectors';

export interface AddMoreItemModalProps extends Pick<DrawerProps, 'title' | 'zIndex' | 'confirmText'> {
  serviceItemType: ServiceItemType;
  appointmentId: string;
  petId: number;
  onClose?: () => void;
  onConfirm?: (v: ServiceEntry[]) => void;
}

export const AddMoreItemDrawer = (props: AddMoreItemModalProps) => {
  const { serviceItemType, petId, appointmentId, onClose, onConfirm, ...restProps } = props;
  const [pet, apptInfo] = useSelector(selectPetInAppt(petId, appointmentId), selectApptInfo(appointmentId));
  const [serviceList, setServiceList] = useState<ServiceEntry[]>([]);
  const { resolveServiceList, isLoading } = useBundleService({ clientId: Number(apptInfo.customerId) });

  const tabs = usePickServiceTabs({
    serviceItemType,
    apptId: appointmentId,
    serviceList,
  });

  const prevSelectedServiceIds = useMemo(() => {
    if (!pet) return [];
    return pet.services.filter((s) => !s.associatedId).map((s) => Number(s.serviceId));
  }, [pet]);

  return (
    <Drawer
      {...restProps}
      isOpen
      onClose={onClose}
      onCancel={onClose}
      onConfirm={() => {
        onConfirm?.(serviceList);
      }}
      confirmButtonProps={{ isLoading, isDisabled: !serviceList.length }}
    >
      <ServiceApplicablePicker
        clientId={Number(apptInfo.customerId)}
        tabs={tabs}
        className="moe-mt-0"
        serviceItemType={serviceItemType}
        petIds={[String(petId)]}
        value={serviceList}
        prevSelectedServiceIds={prevSelectedServiceIds}
        onChange={(services) => {
          setServiceList(services);
        }}
        onServiceChange={async (nextServices, extra) => {
          const bundles = await resolveServiceList({ ...extra, nextServices, petIds: [petId] });
          bundles && setServiceList(bundles);
        }}
      />
    </Drawer>
  );
};
