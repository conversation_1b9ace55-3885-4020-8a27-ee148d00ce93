import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useSelector } from 'amos';
import React, { createContext, useContext, useState, type FC } from 'react';
import { useApplicablePetServices } from '../../../../../../../components/ServiceApplicablePicker/hooks/useApplicablePetServices';
import { type ServicePriceDurationInfo } from '../../../../../../../components/ServiceApplicablePicker/hooks/useServiceInfo';
import { PetRecord, petMapBox } from '../../../../../../../store/pet/pet.boxes';
import { ServiceType } from '../../../../../../../store/service/category.boxes';
import { ID_ANONYMOUS } from '../../../../../../../store/utils/identifier';
import { getEmptyBoolState, useBool } from '../../../../../../../utils/hooks/useBool';

interface AddPetServicesState {
  loading: boolean;
  customerId: number;
  serviceType: number;
  renderListInfo: ReturnType<ReturnType<typeof useApplicablePetServices>['getRenderListInfo']>;
  getServiceInfo: ReturnType<typeof useApplicablePetServices>['getServiceInfo'];
  petId: number;
}

interface AddPetServicesContextProps extends AddPetServicesState {
  onlyAvailable: ReturnType<typeof useBool>;
  setPetId: (newPetId: number) => void;
  setCustomerId: (newCustomerId: number) => void;
  setServiceType: (newServiceType: number) => void;
  pet: PetRecord;
}

const AddPetServicesContext = createContext<AddPetServicesContextProps>({
  loading: false,
  customerId: ID_ANONYMOUS,
  serviceType: ServiceType.Service,
  renderListInfo: {
    isEmpty: false,
    lastActiveIds: [],
    list: [],
  },
  petId: ID_ANONYMOUS,
  onlyAvailable: getEmptyBoolState(),
  setPetId: () => {},
  setCustomerId: () => {},
  setServiceType: () => {},
  getServiceInfo: (() => ({}) as ServicePriceDurationInfo) as unknown as AddPetServicesState['getServiceInfo'],
  pet: new PetRecord(),
});

export const AddPetServicesContextProvider: FC = ({ children }) => {
  const [customerId, setCustomerId] = useState<number>(ID_ANONYMOUS);
  const [petId, setPetId] = useState<number>(ID_ANONYMOUS);
  const [serviceType, setServiceType] = useState<number>(ServiceType.Service);
  const onlyAvailable = useBool(true);
  const [petMap] = useSelector(petMapBox);

  // clientId + petId 都 ready 时，内部自动触发请求
  const { loading, getRenderListInfo, getServiceInfo } = useApplicablePetServices({
    onlyAvailable: onlyAvailable.value,
    selectedServiceIds: [],
    serviceItemType: ServiceItemType.GROOMING,
    serviceType,
    petIds: [String(petId)],
    customerId: String(customerId),
  });
  const renderListInfo = getRenderListInfo(serviceType);

  return (
    <AddPetServicesContext.Provider
      value={{
        loading,
        customerId,
        serviceType,
        renderListInfo,
        onlyAvailable,
        petId,
        pet: petMap.mustGetItem(petId),
        getServiceInfo,
        setPetId: (newPetId: number) => setPetId(newPetId),
        setCustomerId: (newCustomerId: number) => setCustomerId(newCustomerId),
        setServiceType: (newServiceType: number) => setServiceType(newServiceType),
      }}
    >
      {children}
    </AddPetServicesContext.Provider>
  );
};

export const useAddPetServicesContext = () => useContext(AddPetServicesContext);
