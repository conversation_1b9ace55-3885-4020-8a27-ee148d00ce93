import { Form, Heading, Modal, Spin, Tag, Text, useForm, useFormState } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { useEffect } from 'react';
import { useHistory } from 'react-router';
import { toast<PERSON><PERSON> } from '../../../components/Toast/Toast';
import { FullPageLayout } from '../../../layout/FullPageLayout';
import { PATH_CREATE_OR_EDIT_PACKAGE, PATH_PACKAGE } from '../../../router/paths';
import { addPackageDetail, getPackageDetail, updatePackageDetail } from '../../../store/packages/package.actions';
import { selectBusinessPackageDetail } from '../../../store/packages/package.selector';
import { isOldPackageData } from '../../../store/packages/utils';
import { isNormal } from '../../../store/utils/identifier';
import { useRouteParams, useRouteQueryV2 } from '../../../utils/RoutePath';
import { useBool } from '../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../utils/hooks/useSerialCallback';
import { useUnsavedConfirmGlobalV2 } from '../../../utils/hooks/useUnsavedConfirmGlobalV2';
import { PackageBasicInfo } from './components/PackageBasicInfo';
import { PackageItems } from './components/PackageItems';
import { PackageOnlineBooking } from './components/PackageOnlineBooking';
import { PackagePrice } from './components/PackagePrice';
import { PackageValidity } from './components/PackageValidity';
import { type PackageAddInfo, type PackageServiceItem } from './types';
import { PackageModelSource } from '../PackageList/components/PackageTable/useColumns';

const initPackageValues: PackageAddInfo = {
  packageId: undefined,
  description: '',
  name: '',
  price: undefined,
  items: [
    {
      quantity: 1,
      services: [],
    },
  ],
  taxId: undefined,
  isActive: true,

  enableOnlineBooking: false,

  // 动态设置该值，form 不支持 undefined 清空默认值，select 不支持 null 展示 placeholder。
  // expirationDays: PackageExpiresDate.OneMonth,
};

/**
 *  过滤出有选择 service 的项
 */
function filterSelectedService(serviceList: PackageServiceItem): serviceList is Required<PackageServiceItem> {
  return serviceList.services.filter((item) => isNormal(item.serviceId)).length !== 0;
}

export function PackageAdd() {
  const history = useHistory();
  const dispatch = useDispatch();
  const tipModalVisible = useBool();
  const { id: packageId } = useRouteQueryV2(PATH_CREATE_OR_EDIT_PACKAGE);
  const { type } = useRouteParams(PATH_CREATE_OR_EDIT_PACKAGE);
  const form = useForm<PackageAddInfo>({
    mode: 'all',
    defaultValues: initPackageValues,
  });
  const [packageInfo] = useSelector(selectBusinessPackageDetail(packageId || ''));

  const isEditMode = type === 'edit' && isNormal(packageId);
  const isOldPackage = isEditMode && isOldPackageData(packageInfo.expirationDays);

  const { isDirty, isValid } = useFormState({
    control: form.control,
  });

  useUnsavedConfirmGlobalV2({
    showConfirm: isDirty,
    modalProps: {
      title: 'Unsaved changes',
      content: 'You have unsaved changes. Are you sure you’re ready to leave?',
      onConfirm: () => {
        // 这个 CTA 是留在当前页面，所以抛个 error 中断后续流程
        throw Error('Back to edit');
      },
      confirmText: 'Back to edit',
      cancelText: 'Leave anyway',
    },
  });

  useEffect(() => {
    if (isEditMode) {
      const editPackageInfo: Required<PackageAddInfo> = {
        ...packageInfo.toJSON(),
        /**
         * package detail 里面用的 key:id，保存/更新使用的 key:packageId
         */
        packageId: +packageId,
      };
      form.reset({
        ...editPackageInfo,
        expirationDays /** 如果是旧的数据，默认需要用户选择过期时间 */: isOldPackage
          ? undefined
          : packageInfo.expirationDays,
      });
    } else {
      form.reset({
        ...initPackageValues,
        expirationDays: undefined, // 需要让用户选择过期时间
      });
    }
  }, [packageInfo, isEditMode, isOldPackage]);

  const handleGetData = useSerialCallback(async () => {
    if (isEditMode) {
      await dispatch(getPackageDetail(packageId));
    }
  });

  useEffect(() => {
    handleGetData();
  }, [isEditMode]);

  const gotoPackagePage = useLatestCallback(() => {
    form.reset(undefined, { keepValues: true, keepDirty: false, keepDefaultValues: false });
    history.push(PATH_PACKAGE.build({ tab: 'packages' }));
  });

  const getFilterServiceList = useLatestCallback(() => {
    const serviceList = form.getValues().items?.filter(filterSelectedService);
    if (!serviceList || serviceList.length === 0) {
      toastApi.error('Please select service!');
      return undefined;
    }
    return serviceList;
  });

  const handleSave = useLatestCallback(async () => {
    await form.handleSubmit(async (value) => {
      const serviceList = getFilterServiceList();
      if (!serviceList) {
        return;
      }

      if (!isEditMode) {
        delete value.packageId;
        await dispatch(addPackageDetail({ ...value, items: serviceList }));
        toastApi.success('New package added');
        gotoPackagePage();
        return;
      }
      tipModalVisible.open();
    })();
  });

  const handleSaveAfterModal = useLatestCallback(async () => {
    if (!isValid) {
      return;
    }
    const values = form.getValues();

    const serviceList = getFilterServiceList();

    if (!serviceList) {
      return;
    }

    await dispatch(
      updatePackageDetail({
        ...values,
        items: serviceList,
      }),
    );
    toastApi.success('Package settings updated');
    gotoPackagePage();
  });

  return (
    <FullPageLayout
      onClose={() => {
        history.push(PATH_PACKAGE.build({ tab: 'packages' }));
      }}
      onSave={handleSave}
    >
      <div className="moe-w-full moe-flex moe-justify-center moe-mb-xl moe-mt-s">
        <div className="moe-w-[900px]">
          <Heading size="2" className="moe-mb-xl moe-flex moe-items-center">
            {type === 'create' ? 'Add' : 'Edit'} package
            {form.getValues().source == PackageModelSource.ENTERPRISE_HUB && (
              <Tag className="moe-ml-[8px]" color="discover" label="Corporate" />
            )}
          </Heading>
          <Spin
            isLoading={isEditMode && handleGetData.isBusy()}
            classNames={{
              base: 'moe-w-full moe-h-full',
              container: 'moe-w-full moe-h-full',
              iconContainer: 'moe-top-[20%]',
            }}
          >
            <Form form={form} footer={null} className="moe-gap-xl">
              <PackageBasicInfo />
              <PackagePrice />
              <PackageItems form={form} />
              <PackageValidity isOldPackageData={isOldPackage} />
              <PackageOnlineBooking />
            </Form>
          </Spin>
        </div>
      </div>
      <Modal
        isOpen={tipModalVisible.value}
        title="Confirm to save"
        classNames={{
          container: 'moe-w-[480px]',
        }}
        onConfirm={handleSaveAfterModal}
        onClose={tipModalVisible.close}
      >
        <Text variant="regular">
          Changes to package settings will not impact packages that have already been sold to clients.
        </Text>
      </Modal>
    </FullPageLayout>
  );
}
