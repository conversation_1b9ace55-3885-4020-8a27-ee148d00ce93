import { FactorType, type LoginResponse } from '@moego/bff-openapi/dist/esm/clients/client.authn';
import { FocusScope, Input, Modal, Spin, Text } from '@moego/ui';
import { useSerialCallback } from '@moego/tools';
import { useMount } from 'ahooks';
import React, { useState } from 'react';
import { useCountDown } from '../../../utils/hooks/useCountDown';
import { ReportActionName } from '../../../utils/reportType';
import { reportData } from '../../../utils/tracker';

const REFRESH_COUNTDOWN_SEC = 60;

interface MfaModalProps {
  factor: Required<LoginResponse>['factor'];
  onSendCode: () => Promise<void>;
  onSubmit: (code: string) => void;
  onCancel: () => void;
}

export const MfaModal = ({ factor, onSendCode, onSubmit, onCancel }: MfaModalProps) => {
  const [code, setCode] = useState('');
  const countdown = useCountDown();
  const remainSec = Math.floor((countdown?.remain || 0) / 1000);

  useMount(async () => {
    await onSendCode();
    countdown.start(REFRESH_COUNTDOWN_SEC * 1000);
  });

  const openIntercom = () => {
    window.Intercom('showNewMessage', 'I need help with two-factor authentication.');
  };

  const renderContentByFactor = (factor: MfaModalProps['factor']) => {
    switch (factor.type) {
      case FactorType.PHONE_NUMBER:
        return (
          <div className="moe-font-normal">
            <Text variant="regular" className="moe-pb-2">
              Input the code we sent to your phone <b>{factor.details.value?.e164Number}</b> to continue.
            </Text>
            <Input value={code} onChange={setCode} placeholder="Input verification code" />
            <Text variant="regular" className="moe-pt-2 moe-text-gray-500">
              Any questions?{' '}
              <span className="moe-link moe-cursor-pointer moe-font-bold" onClick={openIntercom}>
                Contact us.
              </span>
            </Text>
          </div>
        );
      case FactorType.FACTOR_TYPE_UNSPECIFIED:
      // fall through
      default:
        return null;
    }
  };

  const handleTertiaryButtonClick = useSerialCallback(async () => {
    if (remainSec > 0) {
      return;
    }
    await onSendCode();
    countdown.start(REFRESH_COUNTDOWN_SEC * 1000);
  });

  const handleConfirm = () => {
    reportData(ReportActionName.twofaLoginByVerificationCodeClick);
    return onSubmit(code);
  };

  return (
    <Modal
      isOpen
      title="Two-Factor Authentication"
      isMaskCloseable={false}
      autoCloseOnConfirm={false}
      showCloseButton={false}
      disableFocusManagement
      onConfirm={handleConfirm}
      onCancel={onCancel}
      onClose={onCancel}
      confirmText="Submit"
      confirmButtonProps={{
        isDisabled: !code,
        'data-dd-action-name': ReportActionName.twofaLoginByVerificationCodeClick,
      }}
      showTertiaryButton
      tertiaryButtonProps={{
        icon: handleTertiaryButtonClick.isBusy() ? <Spin /> : null,
        isDisabled: handleTertiaryButtonClick.isBusy() || remainSec > 0,
      }}
      tertiaryText={remainSec > 0 ? `Resend (${remainSec}s)` : 'Resend code'}
      onTertiary={handleTertiaryButtonClick}
      classNames={{ container: 'moe-w-[640px]' }}
    >
      <FocusScope autoFocus contain={false}>
        {renderContentByFactor(factor)}
      </FocusScope>
    </Modal>
  );
};
