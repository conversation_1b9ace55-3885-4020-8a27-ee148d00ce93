import { useDispatch } from 'amos';
import { Form } from 'antd';
import { useForm } from 'antd/es/form/Form';
import React, { memo, useEffect, useMemo, useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useMount } from 'react-use';
import IconInvalidName3xPng from '../../../assets/icon/<EMAIL>';
import IconShape3xPng from '../../../assets/icon/<EMAIL>';
import { Button } from '../../../components/Button/Button';
import { ImgIcon } from '../../../components/Icon/Icon';
import { IMPERSONATE_TOKEN_PREFIX, INTERCOM_APP_ID } from '../../../config/host/const';
import { PATH_FORGET_PASSWORD, PATH_SIGN_IN, PATH_SIGN_UP } from '../../../router/paths';
import { loginLegacy } from '../../../store/account/account.actions';
import { type InviteModel, getInvite } from '../../../store/business/invite.actions';
import { useRouteQueryV2 } from '../../../utils/RoutePath';
import { useAfterSignIn } from '../../../utils/hooks/useAfterSignIn';
import { useSerialCallback } from '@moego/tools';
import { getInviteSource } from '../../../utils/invite';
import { ReportActionName, ReportEvent } from '../../../utils/reportType';
import { reportData, reportGTMOnly } from '../../../utils/tracker';
import { CoverLayout } from '../components/CoverLayout';
import { SignInput } from '../components/CoverLayout.style';
import { openWindow } from '../../../utils/utils';
import { MfaModal } from './MfaModal';
import { useMfaLoginFlow } from './useMfaLoginFlow';
import { type SignInInput } from './types';

export const useInvite = () => {
  const [invite, setInvite] = useState<InviteModel | null>(null);
  const query = useRouteQueryV2<{ inviteCode?: string }>();
  const dispatch = useDispatch();

  useEffect(() => {
    if (query.inviteCode) {
      dispatch(getInvite(query.inviteCode)).then(setInvite);
    } else {
      setInvite(null);
    }
  }, [dispatch, query.inviteCode]);

  return invite;
};

const useIsSkipLogin = (cb: () => void) => {
  const query = useRouteQueryV2(PATH_SIGN_IN);
  useMount(() => {
    if (query.skipLogin) {
      cb();
    }
  });
};

const MOE_ANONYMOUS_UID = 'MOE_ANONYMOUS_UID';

export const SignIn = memo(() => {
  const location = useLocation();
  const query = useRouteQueryV2(PATH_SIGN_IN);
  const dispatch = useDispatch();
  const [form] = useForm();
  const afterSignInCallback = useAfterSignIn();
  const invite = useInvite();
  useIsSkipLogin(() => afterSignInCallback());

  useMount(() => {
    let t = localStorage.getItem(MOE_ANONYMOUS_UID);
    if (!t) {
      t = `anonymous_signin_desktop_${Math.random().toString(36).substring(2, 15)}`;
      localStorage.setItem(MOE_ANONYMOUS_UID, t);
    }
    window.Intercom('update', {
      app_id: INTERCOM_APP_ID,
      user_id: t,
    });
  });

  const { enableMfaLogin, mfaFactor, sendMfaCode, cancelMfaLogin, mfaLoginFlowStep1, mfaLoginFlowStep2 } =
    useMfaLoginFlow(afterSignInCallback);

  const handleSubmit = useSerialCallback(async () => {
    reportData(ReportActionName.InProductLogIn);
    const input = (await form.validateFields()) as SignInInput;

    try {
      if (input.password.startsWith(IMPERSONATE_TOKEN_PREFIX)) {
        const token = input.password.substring(IMPERSONATE_TOKEN_PREFIX.length);
        await dispatch(loginLegacy({ byToken: { token } }));
        reportGTMOnly(ReportEvent.Login);
        await afterSignInCallback();
      } else if (enableMfaLogin) {
        reportData(ReportActionName.twofaLoginRequireVerificationCodeClick);
        await mfaLoginFlowStep1(input);
      } else {
        await dispatch(loginLegacy({ byEmailPassword: input }));
        reportGTMOnly(ReportEvent.Login);
        await afterSignInCallback();
      }
    } catch (error: any) {
      if (error?.data?.code === 20025) {
        openWindow('https://go.moego.pet/', '_self');
        return;
      }
    }
  });

  const inviteSource = useMemo(() => {
    return getInviteSource(invite);
  }, [invite]);

  return (
    <>
      <CoverLayout>
        <div className="title">
          {query.inviteCode ? (invite ? `Connect to ${inviteSource} Team` : '\u00a0') : 'Welcome back!'}
        </div>
        {query.inviteCode ? null : (
          <div className="intro">
            MoeGo is made with ❤️ &nbsp;in ☀️ &nbsp;California, to empower pet businesses all over the world.
          </div>
        )}
        <Form form={form} size="large" onFinish={handleSubmit} className="sign-card sign-in">
          {query.inviteCode ? null : (
            <div className="intro">
              MoeGo is made with ❤️ &nbsp;in ☀️ &nbsp;California, to empower pet businesses all over the world.
            </div>
          )}
          <Form.Item
            name="email"
            rules={[
              { required: true, message: 'Please input email!' },
              { type: 'email', message: 'Email address is invalid.' },
            ]}
            getValueFromEvent={(e) => e.target.value.trim()}
          >
            <SignInput prefix={<ImgIcon src={IconShape3xPng} width={20} />} placeholder="Email" autoFocus />
          </Form.Item>
          <Form.Item name="password" rules={[{ required: true, message: 'Please input password!' }]}>
            <SignInput
              prefix={<ImgIcon src={IconInvalidName3xPng} width={18} />}
              placeholder="Password"
              type="password"
            />
          </Form.Item>
          <div className="forget-password">
            <Link to={PATH_FORGET_PASSWORD.build() + location.search}>Forgot password</Link>
          </div>
          <div className="btn-submit">
            <Button
              buttonRadius="circle"
              htmlType="submit"
              btnType="secondary"
              onClick={handleSubmit}
              loading={handleSubmit.isBusy()}
            >
              Login
            </Button>
          </div>
          <div className="sign-tip">
            Not a MoeGo member?&nbsp;
            <Link to={PATH_SIGN_UP.build() + location.search}>Sign up</Link>.
          </div>
        </Form>
        {mfaFactor ? (
          <MfaModal
            factor={mfaFactor}
            onSendCode={sendMfaCode}
            onSubmit={mfaLoginFlowStep2}
            onCancel={cancelMfaLogin}
          />
        ) : null}
      </CoverLayout>
    </>
  );
});
