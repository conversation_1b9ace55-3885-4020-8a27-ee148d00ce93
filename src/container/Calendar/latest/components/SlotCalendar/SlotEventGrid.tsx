import { ID_ANONYMOUS } from '@moego/finance-utils';
import { useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { selectBusinessCalendarConfig } from '../../../../../store/calendarLatest/calendar.selectors';
import { useCalendarConfig } from '../../ApptCalendar/hooks/useCalendarConfig';
import { ExtraLargeEventMinHeight, LargeEventMinHeight, StandardEventMinHeight } from '../../AwesomeCalendar.utils';
import { EventPositioner, SlotCoordinateSystem } from './EventPositioner/EventPositioner';
import {
  type CalendarEvent,
  type EventSegment,
  type PositionedEvent,
  type SlotEventRenderType,
} from './SlotEvent.type';
import { SlotEventRender } from './SlotEventRender';
import { cn } from '@moego/ui';
import { isNil } from 'lodash';

export interface SlotEventGridProps extends SlotEventRenderType {
  events: CalendarEvent[];
  resources: Array<{ id: string }>;
  /** biz 开始小时数 */
  startHour?: number;
  /** biz 结束小时数 */
  endHour?: number;
  /** 时间槽持续时间（分钟），默认 15 */
  slotDuration?: number;
  /** 时间槽高度（像素），默认 23 */
  slotHeight?: number;
  /** 当前显示的日期，默认今天 */
  date?: Date;
  className?: string;
}

export const SlotEventGrid = memo<SlotEventGridProps>((props) => {
  const { events, resources, startHour, endHour, slotDuration = 15, date = new Date(), eventRender, className } = props;

  const [calendarConfig] = useSelector(selectBusinessCalendarConfig);
  const { calendarBaseStartTime, calendarBaseEndTime, isCalendarZoomLevelLarge, isCalendarZoomLevelExtraLarge } =
    useCalendarConfig();

  const { bizStartHour, bizEndHour } = useMemo(() => {
    const slotMinTime = calendarBaseStartTime.hour();
    const slotMaxTime = calendarBaseEndTime.hour();
    return {
      bizStartHour: startHour ?? slotMinTime,
      // calendar中可见的最大时间
      bizEndHour: endHour ?? slotMaxTime,
    };
  }, [calendarBaseStartTime, calendarBaseEndTime, startHour, endHour]);

  const slotHeight = useMemo(() => {
    if (!isNil(props.slotHeight)) {
      return props.slotHeight;
    }
    if (isCalendarZoomLevelExtraLarge) {
      return ExtraLargeEventMinHeight;
    }
    if (isCalendarZoomLevelLarge) {
      return LargeEventMinHeight;
    }
    return StandardEventMinHeight;
  }, [props.slotHeight, isCalendarZoomLevelLarge, isCalendarZoomLevelExtraLarge]);

  // 创建时间槽坐标系统
  const slotCoords = useMemo(() => {
    return new SlotCoordinateSystem(bizStartHour, bizEndHour, slotDuration, slotHeight, date);
  }, [bizStartHour, bizEndHour, slotDuration, slotHeight, date]);

  // 创建事件定位器
  const eventPositioner = useMemo(() => {
    return new EventPositioner(slotCoords, {
      eventMinHeight: isCalendarZoomLevelExtraLarge
        ? ExtraLargeEventMinHeight
        : isCalendarZoomLevelLarge
          ? LargeEventMinHeight
          : StandardEventMinHeight,
    });
  }, [slotCoords, isCalendarZoomLevelLarge, isCalendarZoomLevelExtraLarge]);

  // 将事件转换为事件段
  const eventSegments = useMemo(() => {
    return events
      .filter((event) => {
        return event.start.getHours() <= bizEndHour;
      })
      .map(
        (event) =>
          ({
            id: event.id,
            start: event.start,
            end: event.end,
            resourceId: event.resourceId,
            data: event,
          }) satisfies EventSegment,
      );
  }, [events, bizEndHour]);

  // 计算事件位置
  const positionedEvents = useMemo(() => {
    return eventPositioner.positionEvents(eventSegments);
  }, [eventPositioner, eventSegments]);

  // 按资源ID分组
  const eventsByResource = useMemo(() => {
    const map = new Map<string, PositionedEvent[]>();
    resources.forEach((resource) => map.set(resource.id, []));

    positionedEvents.forEach((positioned) => {
      const resourceId = positioned.segment.resourceId || `${ID_ANONYMOUS}`;
      if (!map.has(resourceId)) {
        map.set(resourceId, []);
      }
      map.get(resourceId)!.push(positioned);
    });

    return map;
  }, [positionedEvents, resources]);

  return (
    <div className={cn('moe-relative moe-w-full moe-h-full moe-flex', className)}>
      {/* 资源列（员工列） */}
      {resources.map((resource, index) => {
        const events = eventsByResource.get(resource.id) || [];
        return (
          <div
            key={`${resources.length}-${resource.id}-${index}`}
            className="moe-relative moe-flex-1 moe-min-w-[140px] moe-w-full"
          >
            {/* Slots 事件内容 */}
            <SlotEventRender
              calendarConfig={calendarConfig}
              events={events}
              eventRender={eventRender}
              isFirstSlot={index === 0}
              isLastSlot={index === resources.length - 1}
            />
          </div>
        );
      })}
    </div>
  );
});
