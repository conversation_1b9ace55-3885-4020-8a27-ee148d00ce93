import { cn } from '@moego/ui';
import { isFunction } from 'lodash';
import React, { type ForwardedRef, useEffect, useMemo } from 'react';
import { BasedTableView } from '../../../../../components/BasedTable/BasedTable.styles';
import { flatTableRowShow, getRootTable } from '../../../../../components/BasedTable/BasedTable.utils';
import { BasedTableRow } from '../../../../../components/BasedTable/BasedTableRow';
import { type BasedTableProps, type TableRowData } from '../../../../../components/BasedTable/types';
import { Condition } from '../../../../../components/Condition';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { memoForwardRef } from '../../../../../utils/react';

export const BasedTable = memoForwardRef(function BasedTable<T>(
  props: BasedTableProps<T>,
  ref: ForwardedRef<HTMLDivElement>,
) {
  const {
    columns,
    rowKey,
    expandable,
    expandedRowRender,
    className = '',
    onRowClick,
    data = [],
    noDataArea: noDataLine,
    showHeaderWhenEmpty,
    renderExpandedDesc,
  } = props;
  const [expandedRowKeys, setExpandedRowKeys] = React.useState<Set<string>>(new Set());

  const handleToggleExpandedRowKeys = useLatestCallback((key: string) => {
    setExpandedRowKeys((prev) => {
      if (prev.has(key)) {
        prev.delete(key);
      } else {
        prev.add(key);
      }
      return new Set(prev);
    });
  });

  const tableRoot = useMemo(() => {
    return getRootTable(data, rowKey);
  }, [data]);

  const tableRow = useMemo(() => {
    const cache: TableRowData<T>[] = [];
    flatTableRowShow(tableRoot.children, cache);
    return cache;
  }, [tableRoot]);

  useEffect(() => {
    setExpandedRowKeys(new Set());
  }, [expandable, expandedRowRender, data, rowKey]);

  return (
    <>
      <Condition if={data.length > 0 || showHeaderWhenEmpty}>
        <BasedTableView className={className} ref={ref}>
          <table>
            <tbody>
              <tr className="table-head">
                {columns.map((column, index) => {
                  const isAlignRight = !!column.alignRight;
                  return (
                    <th
                      style={{ width: column.width, minWidth: column.minWidth ?? column.width }}
                      key={index}
                      className={cn(
                        'moe-text-[14px] moe-leading-[18px] moe-font-bold moe-text-secondary moe-whitespace-nowrap !moe-px-spacing-s',
                        {
                          'moe-text-right': isAlignRight,
                        },
                        column.titleClassName,
                      )}
                    >
                      {column.renderHeader ? column.renderHeader(column, index, props.data[0]) : column.title}
                    </th>
                  );
                })}
              </tr>
              {tableRow.map((item, index) => {
                const original = item.type === 'row' ? item.original : undefined;
                const isExpandable = original ? !!expandable?.(original) : false;
                const key = original ? (rowKey ? rowKey(original) : `${index}`) : item.id;
                const isExpanded = expandedRowKeys.has(key);
                return (
                  <BasedTableRow<T>
                    key={key}
                    rowKey={key}
                    isExpanded={isExpanded}
                    data={item as TableRowData<T>}
                    columns={columns}
                    isExpandable={isExpandable}
                    handleToggleExpandedRowKeys={handleToggleExpandedRowKeys}
                    expandedRowRender={expandedRowRender}
                    onClick={onRowClick}
                    renderExpandedDesc={renderExpandedDesc}
                  />
                );
              })}
            </tbody>
          </table>
        </BasedTableView>
      </Condition>
      <Condition if={data.length === 0}>
        <div
          className={cn(
            'moe-rounded-l moe-bg-neutral-sunken-0 moe-py-[24px] moe-flex moe-items-center moe-justify-center',
            {
              'moe-w-full moe-my-[96px] moe-bg-transparent': showHeaderWhenEmpty,
            },
          )}
        >
          {isFunction(noDataLine) ? noDataLine() : noDataLine}
        </div>
      </Condition>
    </>
  );
}) as <T>(props: BasedTableProps<T> & { ref?: ForwardedRef<HTMLDivElement> }) => React.JSX.Element;
