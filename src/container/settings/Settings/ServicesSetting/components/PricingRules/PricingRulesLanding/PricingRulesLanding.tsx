import { Spin } from '@moego/ui';
import React, { type FC } from 'react';
import { Condition } from '../../../../../../../components/Condition';
import { getPricingRuleOverview } from '../../../../../../../store/pricingRule/actions/private/pricingRule.actions';
import { useQuery } from '../../../../../../../store/utils/useQuery';
import { SettingTitle } from '../../../../components/SettingTitle';
import { useServiceSettingContext } from '../../../ServicesSettingContext';
import { usePricingRulesEntrySection } from '../../../hooks/usePricingRulesEntrySection';
import { ServicesTitle } from '../../ServicesTitle';
import { DiscountSettingButton } from '../components/DiscountSettingButton';
import { PricingRuleEntryCard } from '../components/PricingRuleEntryCard';
import { PricingRuleSection } from '../components/PricingRuleSection';
import { SettingsDesc } from '../../../../components/SettingsDesc';

export const PricingRulesLanding: FC = () => {
  const { navType } = useServiceSettingContext();
  const { loading } = useQuery(getPricingRuleOverview());
  const pricingRulesEntrySections = usePricingRulesEntrySection();

  return (
    <div className="moe-w-full moe-flex moe-flex-col moe-mb-[100px]">
      <SettingTitle className="moe-items-center moe-mb-xs" title={<ServicesTitle navType={navType} />} />
      <SettingsDesc />
      <Spin isLoading={loading}>
        {pricingRulesEntrySections.map((section) => (
          <PricingRuleSection
            key={section.title}
            title={section.title}
            className={section.showDiscountPricingSettingsModal ? 'moe-gap-[12px]' : ''}
            extra={
              <Condition if={section.showDiscountPricingSettingsModal}>
                <DiscountSettingButton />
              </Condition>
            }
          >
            {section.rules.map((rule) => (
              <PricingRuleEntryCard key={rule.id} {...rule} />
            ))}
          </PricingRuleSection>
        ))}
      </Spin>
    </div>
  );
};
