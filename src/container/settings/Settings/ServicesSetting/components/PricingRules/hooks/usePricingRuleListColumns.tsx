import { RuleType, Source } from '@moego/api-web/moego/models/offering/v2/pricing_rule_enums';
import { type PricingRule } from '@moego/api-web/moego/models/offering/v2/pricing_rule_models';
import { Button, type ColumnDef, Dropdown, Heading, IconButton, MajorMoreOutlined, Tag, Text } from '@moego/ui';
import { useSelector } from 'amos';
import React, { useMemo } from 'react';
import { type BasedTableColumnProps } from '../../../../../../../components/BasedTable/types';
import { Condition } from '../../../../../../../components/Condition';
import { selectCurrentBusiness } from '../../../../../../../store/business/business.selectors';
import { PricingRuleModalAction } from '../PricingRules.enum';
import { PricingRulesHelper } from '../PricingRulesHelper';
import { usePricingRuleSaveModal } from './usePricingRuleSaveModal';

interface UsePricingRuleListColumnsProps {
  ruleType: RuleType;
  handleDelete: (ruleId: string, isSelected: boolean) => void;
  handleGoAddOrEditPricingRule: (ruleId: string) => void;
  handleActiveOrInactive: (pricingRuleId: string, isSelected: boolean) => void;
}

export const usePricingRuleListColumns = (props: UsePricingRuleListColumnsProps) => {
  const { ruleType, handleGoAddOrEditPricingRule, handleDelete, handleActiveOrInactive } = props;
  const [business] = useSelector(selectCurrentBusiness);
  const openPricingRuleSaveModal = usePricingRuleSaveModal();
  const showServiceName = [RuleType.ZONE].includes(ruleType);

  return useMemo(() => {
    const columns: ColumnDef<PricingRule>[] = [
      {
        header: 'Rule name',
        cell: ({ row }) => {
          return (
            <Heading size={5}>
              {row.original.ruleName}
              {row.original.source === Source.ENTERPRISE_HUB && (
                <Tag className="moe-ml-[4px]" color="discover" label="Corporate" />
              )}
            </Heading>
          );
        },
      },
    ];
    if (ruleType === RuleType.PEAK_DATE) {
      columns.push(
        ...([
          {
            header: 'Dates',
            maxSize: 260,
            cell: ({ row }) => {
              const dateRange = PricingRulesHelper.getDateText(row.original);
              return (
                <Condition if={dateRange}>
                  <Text variant="regular-short" ellipsis={{ rowCount: 3, tooltip: true }}>
                    {dateRange}
                  </Text>
                </Condition>
              );
            },
          },
          {
            header: 'Surcharge',
            cell: ({ row }) => {
              return (
                <Text variant="regular-short">{`${PricingRulesHelper.getRuleEffectText(row.original)} ${PricingRulesHelper.getRuleApplyText(row.original)}`}</Text>
              );
            },
          },
        ] as ColumnDef<PricingRule>[]),
      );
    }
    if ([RuleType.MULTIPLE_PET, RuleType.MULTIPLE_STAY].includes(ruleType)) {
      columns.push({
        header: 'Rule configuration',
        cell: ({ row }) => {
          const ruleConfigurationText = PricingRulesHelper.getRuleConfigurationText(row.original);
          return (
            <Text variant="regular-short" className="moe-whitespace-pre-line">
              {ruleConfigurationText.join('\n')}
            </Text>
          );
        },
      });
    }
    columns.push(
      ...([
        {
          header: 'Applicable services',
          cell: ({ row }) => {
            return (
              <Text variant="regular-short">
                {PricingRulesHelper.getRuleApplicableServices(row.original, showServiceName).join(', ')}
              </Text>
            );
          },
        },
        {
          header: 'Action',
          meta: {
            sticky: 'right',
          },
          cell: ({ row }) => {
            const isActive = row.original.isActive;
            return (
              <div className="moe-flex moe-items-center moe-gap-x-s">
                <Button variant="tertiary" onPress={() => handleGoAddOrEditPricingRule(row.original.id)}>
                  Edit
                </Button>
                <Dropdown align="end">
                  <Dropdown.Trigger>
                    <IconButton icon={<MajorMoreOutlined />} classNames={{ base: 'moe-bg-transparent' }} />
                  </Dropdown.Trigger>
                  <Dropdown.Menu>
                    <Dropdown.Item onAction={() => handleGoAddOrEditPricingRule(row.original.id)}>Edit</Dropdown.Item>
                    <Dropdown.Item
                      onAction={() =>
                        openPricingRuleSaveModal({
                          action: isActive ? PricingRuleModalAction.Active : PricingRuleModalAction.Inactive,
                          onSave: async (isSelected) => handleActiveOrInactive(row.original.id, isSelected),
                        })
                      }
                    >
                      {isActive ? 'Deactivate' : 'Active'}
                    </Dropdown.Item>
                    <Dropdown.Item
                      classNames={{ title: 'moe-text-danger' }}
                      onAction={() =>
                        openPricingRuleSaveModal({
                          action: PricingRuleModalAction.Delete,
                          onSave: async (isSelected) => handleDelete(row.original.id, isSelected),
                        })
                      }
                    >
                      Delete
                    </Dropdown.Item>
                  </Dropdown.Menu>
                </Dropdown>
              </div>
            );
          },
        },
      ] as ColumnDef<PricingRule>[]),
    );
    return columns;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [business, ruleType]);
};

const adaptColumnDefToBasedTableColumn = <T,>(columnDef: ColumnDef<T>, index: number): BasedTableColumnProps<T> => {
  return {
    prop: `column_${index}` as keyof T, // 使用索引作为 prop，因为 BasedTable 需要 prop
    title: columnDef.header as string,
    width: columnDef.maxSize,
    minWidth: columnDef.maxSize,
    stickyRight: (columnDef.meta as any)?.sticky === 'right',
    render: (data: T) => {
      if (columnDef.cell) {
        return (columnDef.cell as any)?.({
          row: {
            original: data,
          },
        });
      }
      return null;
    },
  };
};

export const usePricingRuleListBasedTableColumns = (props: UsePricingRuleListColumnsProps) => {
  const columnDefs = usePricingRuleListColumns(props);

  return useMemo(() => {
    return columnDefs.map((columnDef, index) => {
      if (columnDef.header === 'Action') {
        return {
          ...adaptColumnDefToBasedTableColumn(columnDef, index),
          width: 110,
          stickyRight: false,
        };
      }
      return adaptColumnDefToBasedTableColumn(columnDef, index);
    });
  }, [columnDefs]);
};
