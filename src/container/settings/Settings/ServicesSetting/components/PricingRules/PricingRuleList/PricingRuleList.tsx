import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { type RuleType } from '@moego/api-web/moego/models/offering/v2/pricing_rule_enums';
import {
  AlertDialog,
  Button,
  Empty,
  Heading,
  IconButton,
  type PaginationState,
  LegacySelect as Select,
  Spin,
  Table,
  Text,
} from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { type FC, memo, useEffect, useMemo, useState } from 'react';
import { useHistory } from 'react-router';
import { CareTypeMultiSelector } from '../../../../../../../components/CareTypeSelector/CareTypeSelector';
import { toastApi } from '../../../../../../../components/Toast/Toast';
import { PATH_ADD_OR_EDIT_PRICING_RULE_V2, PATH_SERVICE_SETTING } from '../../../../../../../router/paths';
import {
  deletePricingRule,
  upsertPricingRule,
} from '../../../../../../../store/pricingRule/actions/private/pricingRule.actions';
import { companyPricingRuleMapBox } from '../../../../../../../store/pricingRule/pricingRule.boxes';
import {
  selectPricingRuleIdList,
  selectPricingRuleList,
} from '../../../../../../../store/pricingRule/pricingRule.selectors';
import { getAllCompanyFullServiceInfoList } from '../../../../../../../store/service/actions/public/service.actions';
import { Scene } from '../../../../../../../store/service/scene.enum';
import { useRouteParams } from '../../../../../../../utils/RoutePath';
import { useLatestCallback } from '../../../../../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../../../../../utils/hooks/useSerialCallback';
import { SettingTitle } from '../../../../components/SettingTitle';
import {
  PricingRuleNavInfoEnum,
  PricingRuleStatus,
  PricingRuleStatusEnum,
  PricingRuleTypes,
} from '../PricingRules.enum';
import { PricingRulesReporter } from '../PricingRules.report';
import { usePricingRuleList } from '../hooks/usePricingRuleList';
import { usePricingRuleListColumns, usePricingRuleListBasedTableColumns } from '../hooks/usePricingRuleListColumns';
import { MajorLeftArrowOutlined } from '@moego/icons-react';
import { ServicesNav } from '../../../types';
import { ZoneExpandPanel } from '../ExpandPanel/ZoneExpandPanel';

import { type PricingRule } from '@moego/api-web/moego/models/offering/v2/pricing_rule_models';
import { BasedTable } from '../../BasedTable';
import { useQuery } from '../../../../../../../store/utils/useQuery';
import { getBusinessServiceArea } from '../../../../../../../store/serviceArea/serviceArea.actions';
import { currentBusinessIdBox } from '../../../../../../../store/business/business.boxes';

const PricingRuleExpanderEnum: Record<string, React.ComponentType<any>> = {
  [PricingRuleTypes.Zone]: ZoneExpandPanel,
};

export const PricingRuleList: FC<{
  /**
   * 自定义 Pricing rules 上方 care types 选择器可选择 care types 范围
   */
  careTypesScope: ServiceItemType[];
}> = memo((props) => {
  const { careTypesScope } = props;
  const reporter = PricingRulesReporter.getInstance();
  const dispatch = useDispatch();
  const history = useHistory();
  const { childPanel } = useRouteParams(PATH_SERVICE_SETTING);
  const [pricingRuleIdList, pricingRuleList, pricingRuleMap, currentBusinessId] = useSelector(
    selectPricingRuleIdList,
    selectPricingRuleList,
    companyPricingRuleMapBox,
    currentBusinessIdBox,
  );
  const [status, setStatus] = useState<PricingRuleStatus>(PricingRuleStatus.Active);
  const [careTypes, setCareTypes] = useState<ServiceItemType[]>(careTypesScope);
  const { title, description, ruleType } = useMemo(
    () => PricingRuleNavInfoEnum.mapLabels[childPanel as PricingRuleTypes],
    [childPanel],
  );
  const getPricingRuleList = usePricingRuleList(ruleType);
  const isZones = childPanel === PricingRuleTypes.Zone;
  const showExpandedTable = !!PricingRuleExpanderEnum[childPanel as PricingRuleTypes];

  const handleActiveOrInactive = useLatestCallback(async (pricingRuleId: string, isSelected: boolean) => {
    const pricingRule = pricingRuleMap.mustGetItem(pricingRuleId).toJSON();
    try {
      await dispatch(
        upsertPricingRule(
          {
            pricingRuleDef: { ...pricingRule, isActive: !pricingRule.isActive },
            applyToUpcomingAppointments: isSelected,
          },
          { autoToast: false },
        ),
      );
      toastApi.success('Pricing rule updated successfully');
      reporter.reportPricingRuleSettingAction({
        action: pricingRule.isActive ? 'inactive' : 'active',
        ruleType: Number(ruleType) as RuleType,
      });
    } catch (error) {
      if (error.data?.message) {
        AlertDialog.open({
          title: 'Rule cannot be activated',
          content: error.data.message,
          showCancelButton: false,
          confirmText: 'Got it',
          className: 'moe-w-[540px]',
        });
      }
    }
    refresh();
  });
  const handleDelete = useLatestCallback(async (ruleId: string, isApplyToUpcomingAppointments: boolean) => {
    await dispatch(deletePricingRule({ id: ruleId, applyToUpcomingAppointments: isApplyToUpcomingAppointments }));
    toastApi.success('Pricing rule deleted successfully');
    reporter.reportPricingRuleSettingAction({
      action: 'delete',
      ruleType: Number(ruleType) as RuleType,
    });
  });
  const handleGoAddOrEditPricingRule = useLatestCallback((ruleId?: string) => {
    history.push(
      PATH_ADD_OR_EDIT_PRICING_RULE_V2.fully(
        { ruleType },
        { isActive: status === PricingRuleStatus.Active },
        ruleId ? { ruleId } : {},
      ),
    );
  });

  // 获取列配置
  const tableColumns = usePricingRuleListColumns({
    ruleType,
    handleDelete,
    handleActiveOrInactive,
    handleGoAddOrEditPricingRule,
  });

  const basedTableColumns = usePricingRuleListBasedTableColumns({
    ruleType,
    handleDelete,
    handleActiveOrInactive,
    handleGoAddOrEditPricingRule,
  });

  const refresh = useLatestCallback(() => {
    getPricingRuleList(careTypes, status === PricingRuleStatus.Active, {
      pageNum: 1,
      pageSize: showExpandedTable ? 1000 : 10,
    });
  });

  const loadMore = useLatestCallback((pagination: PaginationState) => {
    getPricingRuleList(careTypes, status === PricingRuleStatus.Active, {
      pageNum: pagination.pageIndex,
      pageSize: pagination.pageSize,
    });
  });

  const loadData = useSerialCallback(async () => {
    // 用 bff
    await dispatch(getAllCompanyFullServiceInfoList({ inactive: true, withAddon: true }));
    await dispatch(getAllCompanyFullServiceInfoList({ inactive: false, withAddon: true }));
  });

  const handleRenderExpandedRow = useLatestCallback((record: PricingRule) => {
    const ExpanderComponent = PricingRuleExpanderEnum[childPanel as string];
    if (ExpanderComponent) {
      return <ExpanderComponent pricingRule={record} />;
    }
    return null;
  });

  const handleGetRowKey = useLatestCallback((record: PricingRule) => {
    return record.id;
  });

  useQuery(getBusinessServiceArea(currentBusinessId), isZones);

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    reporter.reportPricingRulePageView({ ruleType });
  }, [ruleType]);

  useEffect(() => {
    refresh();
  }, [careTypes, status]);
  return (
    <div className="moe-w-full moe-flex moe-flex-col moe-mb-[100px]">
      <SettingTitle
        className="moe-mb-xs"
        headerRight={
          <Button variant="primary" onPress={() => handleGoAddOrEditPricingRule()}>
            Add new rule
          </Button>
        }
        title={
          <div>
            <div className="moe-flex moe-flew-row moe-gap-x-[4px] moe-items-center">
              {childPanel && (
                <IconButton
                  className="moe-bg-white moe-p-[8px]"
                  icon={<MajorLeftArrowOutlined className="moe-h-[24px] moe-w-[24px]" />}
                  onPress={() =>
                    history.goBackWithFallback(PATH_SERVICE_SETTING.build({ panel: ServicesNav.PricingRules }))
                  }
                />
              )}
              <Heading size={2}>{title}</Heading>
            </div>
          </div>
        }
      />
      <Text variant="small" className="moe-text-tertiary moe-mb-m">
        {description}
      </Text>
      <div className="moe-flex moe-gap-x-[16px] moe-mb-[32px]">
        {!isZones && (
          <CareTypeMultiSelector
            classNames={{
              control: 'moe-w-[300px]',
              formItemWrapper: 'moe-flex-row moe-inline-flex',
            }}
            scene={Scene.BDPricingRule}
            careTypes={careTypes}
            onChange={(values) => setCareTypes(values)}
          />
        )}
        <Select
          value={status}
          className="moe-w-[300px]"
          onChange={(value) => setStatus(value)}
          options={PricingRuleStatusEnum.values.map((item) => PricingRuleStatusEnum.mapLabels[item])}
        />
      </div>
      {!showExpandedTable ? (
        <Table
          columns={tableColumns}
          data={pricingRuleList}
          getRowId={(record) => record.id}
          isLoading={loadData.isBusy() || pricingRuleIdList.isLoading()}
          onRowClick={(row) => handleGoAddOrEditPricingRule(row.original.id)}
          onPaginationChange={loadMore}
          pagination={{
            totalSize: pricingRuleIdList.total,
            pageSize: pricingRuleIdList.pageSize,
            pageIndex: pricingRuleIdList.pageNum,
            pageSizeOptions: [5, 10, 20],
          }}
        />
      ) : (
        <Spin isLoading={loadData.isBusy() || pricingRuleIdList.isLoading()}>
          <BasedTable
            noDataArea={() => <Empty />}
            showHeaderWhenEmpty
            data={pricingRuleList}
            columns={basedTableColumns}
            expandable={() => true}
            expandedRowRender={handleRenderExpandedRow}
            rowKey={handleGetRowKey}
            onRowClick={(row) => handleGoAddOrEditPricingRule(row.id)}
            renderExpandedDesc={(isExpanded) => (isExpanded ? 'Collapse' : 'Show details')}
          />
        </Spin>
      )}
    </div>
  );
});
