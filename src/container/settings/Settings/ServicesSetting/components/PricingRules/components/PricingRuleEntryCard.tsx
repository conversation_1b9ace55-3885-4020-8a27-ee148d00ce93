import { MajorChevronRightOutlined } from '@moego/icons-react';
import { Heading, Tag, Text, cn } from '@moego/ui';
import React, { type FC } from 'react';
import { useHistory } from 'react-router';
import { Condition } from '../../../../../../../components/Condition';
import { SvgIcon } from '../../../../../../../components/Icon/Icon';
import { PATH_SERVICE_SETTING } from '../../../../../../../router/paths';
import { useLatestCallback } from '../../../../../../../utils/hooks/useLatestCallback';
import { ServicesNav } from '../../../types';
import { type PricingRuleTypes } from '../PricingRules.enum';

interface PricingRuleEntryCardProps {
  id: PricingRuleTypes;
  title: string;
  description: string;
  icon: string | JSX.Element;
  actionCount?: string;
  className?: string;
  isMultipleActiveCount?: boolean;
  rowCount?: number;
}
export const PricingRuleEntryCard: FC<PricingRuleEntryCardProps> = (props) => {
  const history = useHistory();
  const { id, title, description, actionCount, icon, isMultipleActiveCount, className, rowCount = 2 } = props;
  const handleClick = useLatestCallback(() => {
    history.push(PATH_SERVICE_SETTING.build({ panel: ServicesNav.PricingRules, childPanel: id }));
  });
  const activeLabel = isMultipleActiveCount ? `${actionCount} Active` : 'Active';
  return (
    <div
      onClick={handleClick}
      className={cn(
        'moe-border moe-border-divider',
        'moe-flex moe-items-start moe-cursor-pointer',
        'moe-w-[calc(50%-12px)] moe-p-8px-300 moe-rounded-8px-200 moe-gap-8px-200',
        'hover:moe-bg-neutral-sunken-light',
        className,
      )}
    >
      <div className="moe-h-full">
        <div
          className={cn(
            'moe-bg-brand-subtle moe-text-brand',
            'moe-flex moe-items-center moe-justify-center',
            'moe-w-8px-500 moe-h-8px-500 moe-rounded-8px-50',
          )}
        >
          {typeof icon === 'string' ? <SvgIcon src={icon} size={24} color="currentColor" /> : icon}
        </div>
      </div>
      <div className="moe-flex moe-flex-col moe-gap-[4px] moe-flex-1">
        <div className="moe-flex moe-items-center moe-gap-x-[8px]">
          <Heading size={5}>{title}</Heading>
          <Condition if={Number(actionCount) > 0}>
            <Tag label={activeLabel} size="s" color="success" isBordered={false} />
          </Condition>
        </div>
        <Text variant="small" className="moe-text-tertiary moe-break-words moe-min-h-[40px]" ellipsis={{ rowCount }}>
          {description}
        </Text>
      </div>
      <div className="moe-h-full moe-flex moe-items-center moe-justify-center">
        <MajorChevronRightOutlined color="currentColor" />
      </div>
    </div>
  );
};
