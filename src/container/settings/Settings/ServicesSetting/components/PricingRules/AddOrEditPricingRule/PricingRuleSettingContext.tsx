import { type ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { type ConditionGroup, type Effect } from '@moego/api-web/moego/models/offering/v2/pricing_rule_defs';
import { RuleType } from '@moego/api-web/moego/models/offering/v2/pricing_rule_enums';
import { type PricingRule } from '@moego/api-web/moego/models/offering/v2/pricing_rule_models';
import { type useForm } from '@moego/ui';
import { createContext, useContext } from 'react';
import { ZoneConfigTypeKeys } from './utils/AddOrEditPricingRule.config';

export interface LocalEffect extends Omit<Effect, 'value'> {
  value: number | null | undefined;
}
export interface LocalConditionGroup extends Omit<ConditionGroup, 'effect'> {
  effect: LocalEffect;
}
export interface LocalPricingRuleConfiguration {
  conditionGroups: LocalConditionGroup[];
}

export interface LocalPricingRule extends Omit<PricingRule, 'ruleConfiguration'> {
  ruleConfiguration: LocalPricingRuleConfiguration;
  draftServiceFilter: ServiceItemType[];
  activeRuleConfiguration: ZoneConfigTypeKeys;
}

export type PricingRuleForm = ReturnType<typeof useForm<LocalPricingRule>>;

export interface PricingRuleSettingContextProps {
  ruleType: RuleType;
  isEdit: boolean;
  ruleId?: string;
  form?: PricingRuleForm;
}

export const PricingRuleSettingContext = createContext<PricingRuleSettingContextProps>({
  ruleType: RuleType.UNSPECIFIED,
  isEdit: false,
});

export const usePricingRuleSettingContext = () => useContext(PricingRuleSettingContext);
