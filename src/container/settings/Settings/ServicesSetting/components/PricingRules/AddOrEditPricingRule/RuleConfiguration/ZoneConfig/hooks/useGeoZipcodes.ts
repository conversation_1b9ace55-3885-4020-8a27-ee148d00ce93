import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { createZipcodeOptions } from './zipcodeOptions';
import { GrowthBookFeatureList } from '../../../../../../../../../../utils/growthBook/growthBook.config';
import { useQuery } from '@tanstack/react-query';
import { useMemo } from 'react';

export const useGeoZipcodes = () => {
  const zipcodeOptions = createZipcodeOptions({});
  const isEnableGeoFencing = useFeatureIsOn(GrowthBookFeatureList.EnableGeoFencing);

  const { data } = useQuery(zipcodeOptions);

  const zipcodeItems = useMemo(() => {
    if (data && data.allZipcodes.length) {
      return data.assignedToCurrent
        .flatMap((item) => item.zipcodes)
        .map((zipcode) => {
          return {
            key: zipcode,
            value: zipcode,
            title: zipcode,
          };
        });
    }
    return [];
  }, [data]);

  return {
    zipcodeItems: isEnableGeoFencing ? zipcodeItems : [],
    isEnableGeoFencing: isEnableGeoFencing && !!data?.allZipcodes.length,
  };
};
