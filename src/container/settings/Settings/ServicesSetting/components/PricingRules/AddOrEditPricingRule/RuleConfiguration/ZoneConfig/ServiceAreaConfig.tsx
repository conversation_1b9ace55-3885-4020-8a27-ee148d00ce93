import React, { memo, useMemo } from 'react';
import { Table, Button, useFieldArray } from '@moego/ui';
import { MinorPlusOutlined } from '@moego/icons-react';
import { useServiceAreaColumns } from './hooks/useServiceAreaColumns';
import { RuleType, EffectType, ConditionType } from '@moego/api-web/moego/models/offering/v2/pricing_rule_enums';
import { usePricingRuleSettingContext } from '../../PricingRuleSettingContext';
import { useLatestCallback } from '../../../../../../../../../utils/hooks/useLatestCallback';
import { pricingRuleFormHelperFactory } from '../../utils/PricingRuleFormHelperFactory';
import { ServiceAreaConfigProps } from './ZoneConfig.types';

export const ServiceAreaConfig = memo<ServiceAreaConfigProps>((props) => {
  const { form, ruleType } = usePricingRuleSettingContext();
  const helper = pricingRuleFormHelperFactory.getHelper(Number(ruleType));

  const {
    fields: serviceAreaFields = [],
    append,
    remove,
  } = useFieldArray({
    control: form?.control,
    name: 'ruleConfiguration.conditionGroups',
  });

  const fields = useMemo(() => {
    return serviceAreaFields
      .map((field, index) => ({
        ...field,
        conditionGroupsIndex: index,
      }))
      .filter((field) => field.conditions?.[0]?.type === ConditionType.SERVICE_AREA);
  }, [serviceAreaFields]);

  // 创建新的规则条件
  const createNewRuleCondition = useLatestCallback(() => {
    return {
      conditions: [helper.createCondition()],
      effect: helper.createEffect(),
    };
  });

  // 添加规则
  const handleAddRule = useLatestCallback(() => {
    const newCondition = createNewRuleCondition();
    append(newCondition);
  });

  // 删除规则
  const handleDeleteRule = useLatestCallback((id: string) => {
    const index = serviceAreaFields.findIndex((field) => field.id === id);
    if (index !== -1) {
      remove(index);
    }
  });

  // 将表单数据转换为 ServiceAreaRule 格式用于显示
  const rules = useMemo(() => {
    return fields.map((field) => {
      const condition = field.conditions?.[0];
      const effect = field.effect;

      // 获取 service area id
      const serviceAreaId = condition?.value?.numberValues?.values?.[0] || '';

      // 获取价格和单位
      const priceValue = effect?.value || 0;
      const priceUnit = effect?.type || EffectType.FIXED_INCREASE;

      // 获取四舍五入设置
      const roundToInteger = effect?.roundingPosition === 0;

      return {
        id: field.id,
        serviceArea: serviceAreaId,
        priceUplift: priceValue,
        unit: priceUnit,
        roundToInteger,
        conditionGroupsIndex: field.conditionGroupsIndex,
      };
    });
  }, [fields]);

  const columns = useServiceAreaColumns({
    ruleType: RuleType.ZONE,
    handleDeleteRule,
    rules,
  });

  if (!form) return null;

  return (
    <div className="moe-flex moe-flex-col moe-gap-6 moe-w-full">
      <div className="moe-flex moe-flex-col moe-gap-2">
        <Table columns={columns} data={rules} className="moe-w-full" size="m" getRowId={(row) => row.id} />
        <Button
          variant="tertiary"
          icon={<MinorPlusOutlined className="moe-w-6 moe-h-6" />}
          onPress={handleAddRule}
          className="moe-w-fit"
        >
          Add
        </Button>
      </div>
    </div>
  );
});
