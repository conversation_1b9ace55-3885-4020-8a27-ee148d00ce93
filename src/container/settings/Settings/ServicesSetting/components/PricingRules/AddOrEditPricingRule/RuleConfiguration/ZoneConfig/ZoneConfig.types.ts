import { EffectType } from '@moego/api-web/moego/models/offering/v2/pricing_rule_enums';

export interface ServiceAreaRule {
  id: string;
  serviceArea: string;
  priceUplift: number;
  unit: EffectType;
  roundToInteger: boolean;
  conditionGroupsIndex: number;
}

export interface ZipcodeRule {
  id: string;
  zipcode: string;
  priceUplift: number;
  unit: EffectType;
  roundToInteger: boolean;
  conditionGroupsIndex: number;
}

export interface ServiceAreaConfigProps {
  onRulesChange?: (rules: ServiceAreaRule[]) => void;
}

export interface ZipcodeConfigProps {
  onRulesChange?: (rules: ZipcodeRule[]) => void;
}
