import {
  ConditionType,
  EffectType,
  RuleApplyType,
  RuleType,
} from '@moego/api-web/moego/models/offering/v2/pricing_rule_enums';
import { BasePricingRuleFormHelper } from './BasePricingRuleFormHelper';
import { LocalPricingRule } from '../PricingRuleSettingContext';
import { Operator } from '@moego/api-web/moego/utils/v2/condition_messages';
import { PricingRuleRecord } from '../../../../../../../../store/pricingRule/pricingRule.boxes';
import { getDefaultSelectedType } from '../RuleConfiguration/ZoneConfig/ZoneConfig.utils';

export class ZonePricingRuleFormHelper extends BasePricingRuleFormHelper {
  protected ruleType = RuleType.ZONE;

  public createCondition() {
    return {
      type: ConditionType.SERVICE_AREA,
      operator: Operator.EQ,
      value: { numberValues: { values: [] } },
    };
  }

  public createZipcodeCondition() {
    return {
      type: ConditionType.ZIPCODE,
      operator: Operator.EQ,
      value: { stringValues: { values: [] } },
    };
  }

  public createEffect() {
    return {
      type: EffectType.FIXED_INCREASE,
      value: null,
      roundingPosition: undefined,
    };
  }

  protected initAddFormCustomizedValue(): Partial<LocalPricingRule> {
    return { ruleApplyType: RuleApplyType.APPLY_TO_EACH, type: RuleType.ZONE };
  }

  protected initEditFormCustomizedValue(pricingRule: PricingRuleRecord): Partial<LocalPricingRule> {
    const activeRuleConfiguration = getDefaultSelectedType(pricingRule.ruleConfiguration.conditionGroups);
    return {
      activeRuleConfiguration,
    };
  }
}
