import { Dispatchable } from 'amos';
import { RecordProps } from '../../../../../../../../store/utils/RecordMap';
import { LocalEffect, LocalPricingRule } from '../PricingRuleSettingContext';
import { PricingRuleRecord } from '../../../../../../../../store/pricingRule/pricingRule.boxes';
import { getPricingRule } from '../../../../../../../../store/pricingRule/actions/private/pricingRule.actions';
import { RuleApplyType, RuleType } from '@moego/api-web/moego/models/offering/v2/pricing_rule_enums';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { clone, cloneDeep, isUndefined } from 'lodash';
import { Condition, Effect, PricingRuleUpsertDef } from '@moego/api-web/moego/models/offering/v2/pricing_rule_defs';
import { BasicInfoFields, PricingRuleServiceFormConfig, ZoneConfigTypeEnum } from './AddOrEditPricingRule.config';

/**
 * 所有 pricing rule 的表单初始化逻辑的基类
 *
 * - 提供基础的表单初始化逻辑
 * - 提供基础的表单值
 * - 提供基础的表单 effect 即页面初始化时需要执行的 actions
 *
 * @class BasePricingRuleFormHelper
 */
export abstract class BasePricingRuleFormHelper {
  protected abstract ruleType: RuleType;

  public createZipcodeCondition?(): Condition;

  /**
   * 自定义创建表单时需要初始化的值
   *
   * @memberof BasePricingRuleFormHelper
   */
  protected abstract initAddFormCustomizedValue(): Partial<LocalPricingRule>;

  /**
   * 自定义编辑表单时需要初始化的值
   *
   * @memberof BasePricingRuleFormHelper
   */
  protected abstract initEditFormCustomizedValue(
    service: RecordProps<PricingRuleRecord>,
    isActive?: boolean,
  ): Partial<LocalPricingRule>;

  /**
   * 创建一个新的 condition
   *
   * @abstract
   * @return {*}  {Condition}
   * @memberof BasePricingRuleFormHelper
   */
  public abstract createCondition(): Condition;

  /**
   * 创建一个新的 effect
   *
   * @abstract
   * @return {*}  {LocalEffect}
   * @memberof BasePricingRuleFormHelper
   */
  public abstract createEffect(): LocalEffect;

  /**
   * 自定义初始化需要 init 的 actions
   *
   * @memberof BasePricingRuleFormHelper
   */
  protected initCustomizedEffect?(): Array<Dispatchable> {
    return [];
  }

  /**
   * 需要初始化的 actions
   *
   * @return {*}
   * @memberof BasePricingRuleFormHelper
   */
  public getInitEffectActions(ruleId: string) {
    return [getPricingRule({ id: ruleId }), ...(this.initCustomizedEffect?.() ?? [])];
  }

  /**
   * 获取 pricing rule 的适用服务范围跟组件相关，提供这个属性主要是给 form validate 使用
   *
   * @return {*}
   * @memberof BasePricingRuleFormHelper
   */
  protected get applicableServiceCheckScope() {
    const { basicInfoFields } = PricingRuleServiceFormConfig.mapLabels[this.ruleType] ?? {};
    if (!isUndefined(basicInfoFields) && basicInfoFields.includes(BasicInfoFields.AllCareTypeApplicableService)) {
      return 'all';
    }
    return 'boarding';
  }

  /**
   * 初始化表单的值
   *
   * @memberof BasePricingRuleFormHelper
   */
  public initFormValue(params: {
    pricingRule?: RecordProps<PricingRuleRecord>;
    isActive?: boolean;
  }): Partial<LocalPricingRule> {
    const { pricingRule, isActive = true } = params;
    if (!pricingRule) {
      return {
        ruleName: '',
        companyId: '',
        isActive,
        type: this.ruleType,
        ruleApplyType: RuleApplyType.APPLY_TO_EACH,
        needInSameLodging: false,
        allBoardingApplicable: false,
        allDaycareApplicable: false,
        selectedBoardingServices: [],
        selectedDaycareServices: [],
        selectedGroomingServices: [],
        selectedAddonServices: [],
        draftServiceFilter: [],
        ruleConfiguration: {
          conditionGroups: [
            {
              conditions: [this.createCondition()],
              effect: this.createEffect(),
            },
          ],
        },
        activeRuleConfiguration: ZoneConfigTypeEnum.ServiceArea,
        ...this.initAddFormCustomizedValue(),
      };
    }
    const draftServiceFilter = [];
    if (pricingRule.allDaycareApplicable || pricingRule.selectedDaycareServices.length > 0) {
      draftServiceFilter.push(ServiceItemType.DAYCARE);
    }
    if (pricingRule.allBoardingApplicable || pricingRule.selectedBoardingServices.length > 0) {
      draftServiceFilter.push(ServiceItemType.BOARDING);
    }
    return { ...pricingRule, draftServiceFilter, ...this.initEditFormCustomizedValue(pricingRule, isActive) };
  }

  /**
   * 前端表单值转换为后端表单值
   *
   * @param {LocalPricingRule} localPricingRule
   * @memberof BasePricingRuleFormHelper
   */
  public transformLocalPricingRuleToServer(localPricingRule: LocalPricingRule): PricingRuleUpsertDef {
    const data = clone(localPricingRule);
    const { draftServiceFilter, activeRuleConfiguration, ...rest } = data;
    const applicableServiceParams =
      this.applicableServiceCheckScope === 'all'
        ? {
            allBoardingApplicable: draftServiceFilter.includes(ServiceItemType.BOARDING)
              ? data.allBoardingApplicable
              : false,
            allDaycareApplicable: draftServiceFilter.includes(ServiceItemType.DAYCARE)
              ? data.allDaycareApplicable
              : false,
            selectedBoardingServices: draftServiceFilter.includes(ServiceItemType.BOARDING)
              ? data.selectedBoardingServices
              : [],
            selectedDaycareServices: draftServiceFilter.includes(ServiceItemType.DAYCARE)
              ? data.selectedDaycareServices
              : [],
          }
        : {
            allBoardingApplicable: data.allBoardingApplicable,
            allDaycareApplicable: data.allDaycareApplicable,
            selectedBoardingServices: data.selectedBoardingServices,
            selectedDaycareServices: data.selectedDaycareServices,
          };

    const filteredConditionGroups = data.ruleConfiguration.conditionGroups.filter((conditionGroup) =>
      conditionGroup.conditions.some((condition) => condition.type === +activeRuleConfiguration),
    );

    return {
      ...rest,
      ...applicableServiceParams,
      ruleConfiguration: {
        ...data.ruleConfiguration,
        conditionGroups: filteredConditionGroups.map((conditionGroup) => {
          const cloneEffect = cloneDeep(conditionGroup.effect);

          if (cloneEffect.roundingPosition !== 0) {
            // 如果为 false 则不传
            delete cloneEffect.roundingPosition;
          }

          return {
            ...conditionGroup,
            effect: cloneEffect as Effect,
          };
        }),
      },
    };
  }
}
