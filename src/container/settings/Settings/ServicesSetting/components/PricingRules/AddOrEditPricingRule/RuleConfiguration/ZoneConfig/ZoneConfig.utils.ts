import { LocalConditionGroup } from '../../PricingRuleSettingContext';
import { ZoneConfigTypeKeys } from '../../utils/AddOrEditPricingRule.config';

export const hasValidValues = (conditionGroup: LocalConditionGroup): boolean => {
  const firstCondition = conditionGroup?.conditions?.[0];
  const values = firstCondition?.value?.numberValues?.values || firstCondition?.value?.stringValues?.values;

  return Array.isArray(values) && values.length > 0;
};

export const getDefaultSelectedType = (conditionGroups: LocalConditionGroup[]): ZoneConfigTypeKeys | undefined => {
  try {
    if (!Array.isArray(conditionGroups)) {
      return undefined;
    }

    // 查找第一个有有效数字值的条件组
    const validConditionGroup = conditionGroups.find(hasValidValues);

    return validConditionGroup?.conditions?.[0]?.type;
  } catch {}
};
