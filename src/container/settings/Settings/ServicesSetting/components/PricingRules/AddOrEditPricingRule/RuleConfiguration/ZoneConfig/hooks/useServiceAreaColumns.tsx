import React, { useMemo } from 'react';
import { Input, Select, IconButton, Heading, Tooltip, createColumnHelper, Form, Controller } from '@moego/ui';
import { MinorInfoOutlined, MinorTrashOutlined } from '@moego/icons-react';
import { useSelector } from 'amos';
import {
  businessServiceAreaListBox,
  serviceAreaMapBox,
} from '../../../../../../../../../../store/serviceArea/serviceArea.boxes';

import { currentBusinessIdBox } from '../../../../../../../../../../store/business/business.boxes';
import { usePriceEnum } from '../../../hooks/usePriceEnum';
import { EffectType, RuleType } from '@moego/api-web/moego/models/offering/v2/pricing_rule_enums';
import { ServiceAreaRule } from '../ZoneConfig.types';
import { usePricingRuleSettingContext } from '../../../PricingRuleSettingContext';
import { RoundToInteger } from '../components/RoundToInteger';
import { ZonePricingRuleFormHelper } from '../../../utils/ZonePricingRuleFormHelper';

const columnHelper = createColumnHelper<ServiceAreaRule>();

export const useServiceAreaColumns = (params: {
  ruleType: RuleType;
  handleDeleteRule: (id: string) => void;
  rules: ServiceAreaRule[];
}) => {
  const { ruleType, handleDeleteRule, rules } = params;
  const { form } = usePricingRuleSettingContext();
  const [currentBusinessId] = useSelector(currentBusinessIdBox);
  const [serviceAreaIds] = useSelector(businessServiceAreaListBox.getList(currentBusinessId));
  const [serviceAreaMap] = useSelector(serviceAreaMapBox);
  const { priceEnum, priceOptions } = usePriceEnum(ruleType);

  const serviceAreas = useMemo(() => {
    return serviceAreaIds.toArray().map((id: number) => {
      const serviceArea = serviceAreaMap.mustGetItem(id);
      return {
        id: String(serviceArea.areaId),
        label: serviceArea.areaName,
        value: String(serviceArea.areaId),
      };
    });
  }, [serviceAreaIds, serviceAreaMap]);

  return [
    columnHelper.accessor('serviceArea', {
      header: () => (
        <Form.Label className="moe-text-secondary" isRequired>
          Service area
        </Form.Label>
      ),
      size: 224,
      cell: ({ row }) => (
        <Controller
          name={`ruleConfiguration.conditionGroups.${row.original.conditionGroupsIndex}.conditions.0.value.numberValues.values`}
          control={form?.control}
          rules={{ required: { value: true, message: 'Service area is required' } }}
          render={({ field, fieldState }) => {
            const currentValue = Array.isArray(field.value) ? field.value : [];
            const conditionGroups = form?.getValues('ruleConfiguration.conditionGroups') || [];

            const allSelectedServiceAreas = useMemo(() => {
              return conditionGroups.flatMap((conditionGroup) => {
                return conditionGroup.conditions.flatMap((condition) => {
                  return condition.value.numberValues?.values ?? [];
                });
              });
            }, [conditionGroups]);

            return (
              <Select.Multiple
                errorMessage={fieldState.error?.message}
                mode="value"
                className="moe-w-[178px]"
                items={serviceAreas}
                helpTextClassNames={{ error: 'moe-text-nowrap' }}
                isInvalid={fieldState.invalid}
                disabledKeys={allSelectedServiceAreas}
                {...field}
                value={currentValue}
              />
            );
          }}
        />
      ),
    }),
    columnHelper.accessor('priceUplift', {
      header: () => (
        <Form.Label className="moe-text-secondary" isRequired>
          Price uplift
        </Form.Label>
      ),
      size: 200,
      cell: ({ row }) => (
        <div className="moe-flex moe-gap-[12px] moe-items-start">
          <Controller
            name={`ruleConfiguration.conditionGroups.${row.original.conditionGroupsIndex}.effect.value`}
            control={form?.control}
            rules={{ required: { value: true, message: 'Price uplift is required' } }}
            render={({ field, fieldState }) => {
              const priceUnitValue = form?.watch(
                `ruleConfiguration.conditionGroups.${row.original.conditionGroupsIndex}.effect.type`,
              );
              const priceUnit = priceUnitValue || new ZonePricingRuleFormHelper().createEffect().type;
              const { min, max } = priceEnum.mapLabels[priceUnit];
              const isPercentage =
                priceUnit === EffectType.PERCENTAGE_INCREASE || priceUnit === EffectType.PERCENTAGE_DISCOUNT;

              return (
                <Input.Number
                  minValue={min}
                  maxValue={max}
                  precision={isPercentage ? 0 : 2}
                  className="!moe-w-[90px]"
                  helpTextClassNames={{ error: 'moe-text-nowrap' }}
                  placeholder="Input"
                  errorMessage={fieldState.error?.message}
                  isInvalid={fieldState.invalid}
                  {...field}
                  value={field.value ?? null}
                />
              );
            }}
          />

          <Form.Item
            rules={{ required: true }}
            name={`ruleConfiguration.conditionGroups.${row.original.conditionGroupsIndex}.effect.type`}
          >
            <Select className="!moe-w-[90px]" items={priceOptions} />
          </Form.Item>
        </div>
      ),
    }),
    columnHelper.accessor('roundToInteger', {
      minSize: 138,
      header: () => (
        <div className="moe-flex moe-items-center moe-gap-2">
          <Heading size="6" className="moe-text-secondary">
            Round to integer
          </Heading>
          <Tooltip
            content="Calculated prices will be rounded to the nearest whole number. For example, $80.33 will be recorded as $80.00, and $80.66 as $81.00."
            side="top"
          >
            <MinorInfoOutlined className="moe-text-tertiary" />
          </Tooltip>
        </div>
      ),
      cell: ({ row }) => <RoundToInteger form={form} conditionGroupsIndex={row.original.conditionGroupsIndex} />,
    }),
    columnHelper.display({
      id: 'actions',
      cell: ({ row }) =>
        rules.length > 1 ? (
          <IconButton
            icon={<MinorTrashOutlined />}
            variant="secondary"
            showBorder={false}
            onPress={() => handleDeleteRule(row.original.id)}
          />
        ) : null,
    }),
  ];
};
