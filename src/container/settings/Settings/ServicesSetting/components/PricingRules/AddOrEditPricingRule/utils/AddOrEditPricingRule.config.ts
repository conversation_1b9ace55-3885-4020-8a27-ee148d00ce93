import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { ConditionType, RuleApplyType, RuleType } from '@moego/api-web/moego/models/offering/v2/pricing_rule_enums';
import { createEnum, EnumValues } from '../../../../../../../../store/utils/createEnum';

export enum BasicInfoFields {
  RuleName = 'ruleName',
  Status = 'status',
  AllCareTypeApplicableService = 'allCareTypeApplicableService',
  BoardingApplicableService = 'boardingApplicableService',
  GroomingApplicableService = 'groomingApplicableService',
}

export enum RuleConfigurationFields {
  ApplicableDate = 'applicableDate',
  Surcharge = 'surcharge',
  ApplicableType = 'applicableType',
  SameLodging = 'sameLodging',
  Configuration = 'configuration',
  ZoneConfig = 'ZoneConfig',
}

export interface PricingRuleServiceFormFields {
  basicInfoFields?: BasicInfoFields[];
  ruleConfigurationFields?: RuleConfigurationFields[];
}

export const PricingRuleServiceFormConfig = createEnum<string, RuleType, PricingRuleServiceFormFields>({
  PeakDate: [
    RuleType.PEAK_DATE,
    {
      basicInfoFields: [BasicInfoFields.RuleName, BasicInfoFields.AllCareTypeApplicableService, BasicInfoFields.Status],
      ruleConfigurationFields: [RuleConfigurationFields.ApplicableDate, RuleConfigurationFields.Surcharge],
    },
  ],
  MultiplePets: [
    RuleType.MULTIPLE_PET,
    {
      basicInfoFields: [BasicInfoFields.RuleName, BasicInfoFields.AllCareTypeApplicableService, BasicInfoFields.Status],
      ruleConfigurationFields: [
        RuleConfigurationFields.ApplicableType,
        RuleConfigurationFields.SameLodging,
        RuleConfigurationFields.Configuration,
      ],
    },
  ],
  MultipleNights: [
    RuleType.MULTIPLE_STAY,
    {
      basicInfoFields: [BasicInfoFields.RuleName, BasicInfoFields.BoardingApplicableService, BasicInfoFields.Status],
      ruleConfigurationFields: [RuleConfigurationFields.Configuration],
    },
  ],
  Zone: [
    RuleType.ZONE,
    {
      basicInfoFields: [BasicInfoFields.RuleName, BasicInfoFields.GroomingApplicableService, BasicInfoFields.Status],
      ruleConfigurationFields: [RuleConfigurationFields.ZoneConfig],
    },
  ],
});

export type PricingRuleValidCareType = ServiceItemType.BOARDING | ServiceItemType.DAYCARE | ServiceItemType.GROOMING;

export const CareTypeApplicableServiceConfig = createEnum({
  Boarding: [
    ServiceItemType.BOARDING,
    {
      isAllApplicableKey: 'allBoardingApplicable',
      selectedServiceIdKey: 'selectedBoardingServices',
      allAssociatedKey: 'allBoardingAssociated',
      associatedServiceIdKey: 'associatedBoardingServiceIds',
      selectedAddonIdKey: 'selectedAddonServices',
      isAllAddonApplicableKey: 'allAddonApplicable',
      allAddonAssociatedKey: 'allAddonAssociated',
      associatedAddonServiceIdsKey: 'associatedAddonServiceIds',
    },
  ],
  Daycare: [
    ServiceItemType.DAYCARE,
    {
      isAllApplicableKey: 'allDaycareApplicable',
      selectedServiceIdKey: 'selectedDaycareServices',
      allAssociatedKey: 'allDaycareAssociated',
      associatedServiceIdKey: 'associatedDaycareServiceIds',
      selectedAddonIdKey: 'selectedAddonServices',
      isAllAddonApplicableKey: 'allAddonApplicable',
      allAddonAssociatedKey: 'allAddonAssociated',
      associatedAddonServiceIdsKey: 'associatedAddonServiceIds',
    },
  ],
  Grooming: [
    ServiceItemType.GROOMING,
    {
      selectedAddonIdKey: 'selectedAddonServices',
      isAllAddonApplicableKey: 'allAddonApplicable',
      allAssociatedKey: 'allGroomingAssociated',
      associatedServiceIdKey: 'associatedGroomingServiceIds',
      allAddonAssociatedKey: 'allAddonAssociated',
      associatedAddonServiceIdsKey: 'associatedAddonServiceIds',
      isAllApplicableKey: 'allGroomingApplicable',
      selectedServiceIdKey: 'selectedGroomingServices',
    },
  ],
} as const);

export const RuleConfigurationConfig = createEnum({
  MultiplePets: [
    RuleType.MULTIPLE_PET,
    {
      labelVisible: true,
      prefixLabel: 'If # of pets ≥',
      priceReductionLabel: 'apply a price reduction of',
    },
  ],
  MultipleNights: [
    RuleType.MULTIPLE_STAY,
    {
      labelVisible: false,
      prefixLabel: 'If # of nights ≥',
      priceReductionLabel: 'apply a price reduction of',
    },
  ],
});

export const PeakDateRuleConfigEffectApplyType = createEnum({
  EachPet: [RuleApplyType.APPLY_TO_EACH, 'for each pet'],
  FirstPet: [RuleApplyType.APPLY_TO_FIRST_PET, 'for first pet'],
});

export const ApplicableDateType = createEnum({
  SpecificDate: [ConditionType.DATE_RANGE, 'Specific date(s)'],
  RepeatDate: [ConditionType.REPEAT_DATES, 'Repeat dates'],
});

export const ZoneConfigTypeEnum = createEnum({
  ServiceArea: [
    ConditionType.SERVICE_AREA,
    {
      label: 'Service area',
      description: 'Service area is set up on the map',
    },
  ],
  Zipcode: [
    ConditionType.ZIPCODE,
    {
      label: 'Zipcode',
      description: 'Zipcode is set up on the map',
    },
  ],
});

export type ZoneConfigTypeKeys = EnumValues<typeof ZoneConfigTypeEnum>;
