import { cn, Select, Markup, Switch, Tabs, Text, Tooltip, useWatch } from '@moego/ui';
import { useSelector } from 'amos';
import React, { Fragment, useCallback, useMemo, useState } from 'react';
import { selectPricingRuleAssociatedServices } from '../../../../../../../../store/pricingRule/pricingRule.selectors';
import { getCompanyBasicServiceInfoList } from '../../../../../../../../store/service/actions/public/service.actions';
import { AllServicesItemType, ServiceType } from '../../../../../../../../store/service/category.boxes';
import { selectAllCategoriesOfAllServices } from '../../../../../../../../store/service/category.selectors';
import { useLatestCallback } from '../../../../../../../../utils/hooks/useLatestCallback';

import { CareTypeApplicableServiceConfig } from '../utils/AddOrEditPricingRule.config';
import { companyServiceMapBox } from '../../../../../../../../store/service/service.boxes';
import { listAssociatedServices } from '../../../../../../../../store/pricingRule/actions/private/pricingRule.actions';
import { useQuery } from '../../../../../../../../store/utils/useQuery';
import { usePricingRuleSettingContext } from '../PricingRuleSettingContext';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { selectCurrentBusiness } from '../../../../../../../../store/business/business.selectors';

interface ServiceOption {
  label: string;
  value: string;
  isDisabled: boolean;
  description?: string;
}

interface ServiceGroup {
  label: string;
  options: ServiceOption[];
}

export const GroomingApplicableService = () => {
  const { form, ruleType, ruleId } = usePricingRuleSettingContext();
  const serviceItemType = ServiceItemType.GROOMING;
  const [serviceMap, business, allService, pricingRuleAssociatedServices] = useSelector(
    companyServiceMapBox,
    selectCurrentBusiness,
    selectAllCategoriesOfAllServices(),
    selectPricingRuleAssociatedServices(ruleId),
  );

  // 本地状态
  const [tab, setTab] = useState(ServiceType.Service);
  const isAddon = tab === ServiceType.Addon;

  const { associatedServiceIdKey, allAssociatedKey, associatedAddonServiceIdsKey, allAddonAssociatedKey } =
    CareTypeApplicableServiceConfig.mapLabels[serviceItemType];

  const createServiceOptions = useCallback(
    (
      serviceType: number,
    ): {
      options: ServiceGroup[];
      optionMap: Map<string, ServiceOption>;
      flattedServiceIds: string[];
    } => {
      const currentCareTypePricingRuleAssociatedServices: string[] =
        pricingRuleAssociatedServices[associatedServiceIdKey] || [];
      const isCurrentCareTypeAllSelected = pricingRuleAssociatedServices[allAssociatedKey] || false;
      const isCurrentAddonAllSelected = pricingRuleAssociatedServices[allAddonAssociatedKey] || false;
      const currentAddonCareTypePricingRuleAssociatedServices: string[] =
        pricingRuleAssociatedServices[associatedAddonServiceIdsKey] || [];

      const serviceKey =
        serviceType === ServiceType.Addon
          ? AllServicesItemType.serviceAddon
          : `${ServiceType.Service}-${serviceItemType}`;

      const serviceList = allService[serviceKey] || [];
      const optionMap = new Map<string, ServiceOption>();
      const flattedServiceIds = new Set<string>();
      const groupOptions = serviceList
        .filter((service) => service.options.length > 0)
        .map<ServiceGroup>((services) => ({
          label: services.label,
          options: services.options.map((originOption) => {
            const { label, value, description } = originOption;
            const key = value.toString();

            const option = {
              label,
              value: key,
              isDisabled: isAddon
                ? isCurrentAddonAllSelected || currentAddonCareTypePricingRuleAssociatedServices.includes(key)
                : isCurrentCareTypeAllSelected || currentCareTypePricingRuleAssociatedServices.includes(key),
              description,
            };
            optionMap.set(key, option);
            flattedServiceIds.add(key);

            return option;
          }),
        }));

      return {
        options: groupOptions,
        optionMap,
        flattedServiceIds: Array.from(flattedServiceIds),
      };
    },
    [
      pricingRuleAssociatedServices,
      allService,
      isAddon,
      serviceItemType,
      associatedServiceIdKey,
      allAssociatedKey,
      allAddonAssociatedKey,
      associatedAddonServiceIdsKey,
    ],
  );

  const {
    options: serviceOptions,
    optionMap: serviceOptionMap,
    flattedServiceIds: serviceFlattedServiceIds,
  } = useMemo(() => createServiceOptions(ServiceType.Service), [createServiceOptions]);
  const {
    options: addonOptions,
    optionMap: addonOptionMap,
    flattedServiceIds: addonFlattedServiceIds,
  } = useMemo(() => createServiceOptions(ServiceType.Addon), [createServiceOptions]);
  const currentOptions = isAddon ? addonOptions : serviceOptions; // 计算当前选项和状态
  const currentFlattedServiceIds = isAddon ? addonFlattedServiceIds : serviceFlattedServiceIds;
  const currentOptionMap = isAddon ? addonOptionMap : serviceOptionMap;

  const {
    isAllApplicableKey: isAllGroomingApplicableKey,
    selectedServiceIdKey: selectedGroomingServiceIdKey,
    isAllAddonApplicableKey,
    selectedAddonIdKey,
  } = CareTypeApplicableServiceConfig.mapLabels[serviceItemType];

  const [isAllAddonApplicable, isAllGroomingApplicable, selectedGroomingServiceIds, selectedAddonIds] = useWatch({
    control: form?.control,
    name: [isAllAddonApplicableKey, isAllGroomingApplicableKey, selectedGroomingServiceIdKey, selectedAddonIdKey],
  });

  // 监听 selectedGroomingServiceIdKey 字段的错误状态
  const groomingServiceError = form?.formState.errors[selectedGroomingServiceIdKey];

  const serviceValues = useMemo(() => {
    const groomingServiceIds = selectedGroomingServiceIds || [];
    const addonServiceIds = selectedAddonIds || [];

    if (isAllAddonApplicable) {
      return [...groomingServiceIds, ...addonFlattedServiceIds];
    }

    if (isAllGroomingApplicable) {
      return [...addonServiceIds, ...serviceFlattedServiceIds];
    }

    return [...groomingServiceIds, ...addonServiceIds];
  }, [
    isAllAddonApplicable,
    isAllGroomingApplicable,
    selectedGroomingServiceIds,
    selectedAddonIds,
    serviceFlattedServiceIds,
    addonFlattedServiceIds,
  ]);

  const disabledKeys = useMemo(() => {
    return currentFlattedServiceIds.filter((id) => currentOptionMap.get(id)?.isDisabled || false);
  }, [currentFlattedServiceIds, currentOptionMap]);
  const hasDisabledServiceIds = disabledKeys.length > 0;

  // 动态键值
  const isAllApplicableKey = isAddon ? isAllAddonApplicableKey : isAllGroomingApplicableKey;
  const selectedServiceIdKey = isAddon ? selectedAddonIdKey : selectedGroomingServiceIdKey;
  const isAllApplicable = !!(isAddon ? isAllAddonApplicable : isAllGroomingApplicable);

  const handleChangeAll = useLatestCallback((isAll: boolean) => {
    form?.setValue(isAllApplicableKey, isAll, { shouldDirty: true });
    form?.setValue(selectedServiceIdKey, isAll ? currentFlattedServiceIds : [], { shouldDirty: true });
    form?.clearErrors(selectedGroomingServiceIdKey);
  });

  useQuery(listAssociatedServices({ type: ruleType, excludePricingRuleId: ruleId }));
  useQuery(getCompanyBasicServiceInfoList({ serviceItemType, serviceType: ServiceType.Service }));
  useQuery(getCompanyBasicServiceInfoList({ serviceItemType, serviceType: ServiceType.Addon }));

  return (
    <Select.Multiple
      isRequired
      showSelectAll
      label="Applicable services"
      placeholder="Select applicable service"
      itemClassNames={{ base: 'moe-relative' }}
      classNames={{
        header: 'moe-p-0',
        footer: 'moe-p-0',
      }}
      overlayClassNames={{
        base: '!moe-max-h-[436px]',
      }}
      disabledKeys={disabledKeys}
      mode={'value'}
      isClearable
      value={serviceValues}
      isInvalid={!!groomingServiceError}
      errorMessage={groomingServiceError?.message}
      formatOptionLabel={(value) => {
        const service = serviceMap.mustGetItem(+value.key);
        const option = currentOptionMap.get(value.key.toString());
        const label = option?.label || value.key ? service.name : value.key;

        if (service.inactive) {
          return `${label} (inactive)`;
        }
        return label;
      }}
      header={
        <div className="moe-w-full moe-max-h-[300px] moe-flex moe-flex-col">
          <div className="moe-flex moe-px-s moe-pt-xs moe-w-full">
            <Tabs
              classNames={{
                base: 'moe-w-full',
                panel: 'moe-pt-none',
                tabList: 'moe-border-none',
              }}
              selectedKey={`${tab}`}
              onChange={(k) => setTab(+k)}
              disableAnimation
            >
              <Tabs.Item key={ServiceType.Service} label="Grooming services" />
              <Tabs.Item key={ServiceType.Addon} label="Add-ons" />
            </Tabs>
          </div>
        </div>
      }
      footer={
        <div className="moe-p-s">
          <Switch isSelected={isAllApplicable} onChange={handleChangeAll} isDisabled={hasDisabledServiceIds}>
            {isAddon ? 'All add-ons (including new add-ons)' : 'All services (including new services)'}
          </Switch>
        </div>
      }
      onChange={(values) => {
        if (!values.length) {
          form?.setValue(selectedAddonIdKey, [], { shouldDirty: true });
          form?.setValue(selectedGroomingServiceIdKey, []);
          form?.setValue(isAllGroomingApplicableKey, false);
          form?.setValue(isAllAddonApplicableKey, false);
          return;
        }

        form?.clearErrors(selectedGroomingServiceIdKey);
        form?.setValue(selectedServiceIdKey, values, { shouldDirty: true });
      }}
    >
      {currentOptions.map((group) => {
        return (
          <Select.Section key={group.label} title={group.label}>
            {group.options.map((option) => {
              const isSelected = serviceValues.includes(option.value);
              const service = serviceMap.mustGetItem(+option.value);
              const price = service.price;

              return (
                <Select.Item
                  key={option.value}
                  textValue={option.label}
                  title={
                    <ServiceOptionItem
                      isSelected={isSelected}
                      isDisabled={option.isDisabled}
                      label={option.label}
                      price={business.formatAmount(price)}
                      description={option.description}
                    />
                  }
                />
              );
            })}
          </Select.Section>
        );
      })}
    </Select.Multiple>
  );
};

const ServiceOptionItem = ({
  label,
  price,
  isDisabled,
  isSelected,
  description,
}: {
  label: string;
  price: string;
  isDisabled: boolean;
  isSelected: boolean;
  description?: string;
}) => {
  const OptionText = isSelected ? Markup : Text;

  const optionContent = (
    <div className="moe-flex moe-items-center moe-gap-xs moe-pointer-events-auto moe-justify-between moe-w-full">
      <div className="moe-flex moe-items-center moe-gap-xs">
        <div className="moe-flex moe-flex-col moe-items-start">
          <OptionText as="span" variant="small" className={cn('moe-text-primary', { 'moe-text-disabled': isDisabled })}>
            {label}
          </OptionText>
          {description && (
            <Text variant="caption" as="div" className={cn('moe-text-tertiary', { 'moe-text-disabled': isDisabled })}>
              {description}
            </Text>
          )}
        </div>
      </div>

      {price && (
        <Text variant="caption" as="div" className={cn('moe-text-tertiary', { 'moe-text-disabled': isDisabled })}>
          {price}
        </Text>
      )}
    </div>
  );

  if (isDisabled) {
    return <Tooltip content="Service already associated with another existing pricing rule.">{optionContent}</Tooltip>;
  }

  return <Fragment>{optionContent}</Fragment>;
};
