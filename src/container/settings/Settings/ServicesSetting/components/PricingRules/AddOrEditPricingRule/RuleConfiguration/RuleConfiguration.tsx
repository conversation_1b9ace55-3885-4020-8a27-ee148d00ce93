import React, { memo } from 'react';

import { FormSection } from '../../components/FormSection';
import { RuleConfigurationFields } from '../utils/AddOrEditPricingRule.config';
import { ApplicableDate } from './ApplicableDate/ApplicableDate';
import { ApplicableType } from './ApplicableType';
import { Configuration } from './Configuration';
import { SameLodging } from './SameLodging';
import { Surcharge } from './Surcharge';
import { PricingRuleServiceFormSections } from '../../PricingRules.enum';
import { ZoneConfig } from './ZoneConfig/ZoneConfig';

export interface RuleConfigurationProps {
  fields: RuleConfigurationFields[];
}

const FieldsComponentMap = {
  [RuleConfigurationFields.ApplicableDate]: ApplicableDate,
  [RuleConfigurationFields.Surcharge]: Surcharge,
  [RuleConfigurationFields.ApplicableType]: ApplicableType,
  [RuleConfigurationFields.SameLodging]: SameLodging,
  [RuleConfigurationFields.Configuration]: Configuration,
  [RuleConfigurationFields.ZoneConfig]: ZoneConfig,
};

export const RuleConfiguration = memo<RuleConfigurationProps>((props) => {
  const { fields } = props;
  return (
    <FormSection section={PricingRuleServiceFormSections.RuleConfiguration}>
      {fields.map((field) => {
        const FieldComponent = FieldsComponentMap[field];
        return <FieldComponent key={field} />;
      })}
    </FormSection>
  );
});
