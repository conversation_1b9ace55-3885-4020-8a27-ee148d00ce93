import React, { memo } from 'react';
import { FormSection } from '../../components/FormSection';
import { BasicInfoFields } from '../utils/AddOrEditPricingRule.config';
import { AllCareTypeApplicableService } from './AllCareTypeApplicableService';
import { BoardingApplicableService } from './BoardingApplicableService';
import { RuleName } from './RuleName';
import { Status } from './Status';
import { PricingRuleServiceFormSections } from '../../PricingRules.enum';
import { GroomingApplicableService } from './GroomingApplicableService';

export interface BasicInfoProps {
  fields: BasicInfoFields[];
}

const FieldsComponentMap = {
  [BasicInfoFields.Status]: Status,
  [BasicInfoFields.RuleName]: RuleName,
  [BasicInfoFields.BoardingApplicableService]: BoardingApplicableService,
  [BasicInfoFields.AllCareTypeApplicableService]: AllCareTypeApplicableService,
  [BasicInfoFields.GroomingApplicableService]: GroomingApplicableService,
};

export const BasicInfo = memo<BasicInfoProps>((props) => {
  const { fields } = props;
  return (
    <FormSection section={PricingRuleServiceFormSections.BasicInfo}>
      {fields.map((field) => {
        const FieldComponent = FieldsComponentMap[field];
        return <FieldComponent key={field} />;
      })}
    </FormSection>
  );
});
