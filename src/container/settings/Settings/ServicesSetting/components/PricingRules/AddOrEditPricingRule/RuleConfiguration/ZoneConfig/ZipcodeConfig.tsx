import React, { memo, useMemo } from 'react';
import { Table, Button, useFieldArray } from '@moego/ui';
import { MinorPlusOutlined } from '@moego/icons-react';
import { useZipcodeColumns } from './hooks/useZipcodeColumns';
import { RuleType, EffectType, ConditionType } from '@moego/api-web/moego/models/offering/v2/pricing_rule_enums';
import { usePricingRuleSettingContext } from '../../PricingRuleSettingContext';
import { useLatestCallback } from '../../../../../../../../../utils/hooks/useLatestCallback';
import { ZipcodeConfigProps } from './ZoneConfig.types';
import { pricingRuleFormHelperFactory } from '../../utils/PricingRuleFormHelperFactory';

export const ZipcodeConfig = memo<ZipcodeConfigProps>((props) => {
  const { form, ruleType } = usePricingRuleSettingContext();
  const helper = pricingRuleFormHelperFactory.getHelper(Number(ruleType));

  const createNewZipcodeRuleCondition = useLatestCallback(() => {
    return {
      conditions: [helper.createZipcodeCondition!()],
      effect: helper.createEffect(),
    };
  });

  const {
    fields: zipcodeFields = [],
    append,
    remove,
  } = useFieldArray({
    control: form?.control,
    name: 'ruleConfiguration.conditionGroups',
  });

  const fields = useMemo(() => {
    return zipcodeFields
      .map((field, index) => ({
        ...field,
        conditionGroupsIndex: index,
      }))
      .filter((field) => field.conditions?.[0]?.type === ConditionType.ZIPCODE);
  }, [zipcodeFields]);

  const handleAddRule = useLatestCallback(() => {
    const newCondition = createNewZipcodeRuleCondition();
    append(newCondition);
  });

  const handleDeleteRule = useLatestCallback((id: string) => {
    const index = zipcodeFields.findIndex((field) => field.id === id);
    if (index !== -1) {
      remove(index);
    }
  });

  const rules = useMemo(() => {
    return fields.map((field) => {
      const condition = field.conditions?.[0];
      const effect = field.effect;

      const zipcode = condition?.value?.stringValues?.values?.[0] || '';

      const priceValue = effect?.value || 0;
      const priceUnit = effect?.type || EffectType.FIXED_INCREASE;

      const roundToInteger = effect?.roundingPosition === 0;

      return {
        id: field.id,
        zipcode,
        priceUplift: priceValue,
        unit: priceUnit,
        roundToInteger,
        conditionGroupsIndex: field.conditionGroupsIndex,
      };
    });
  }, [fields]);

  const columns = useZipcodeColumns({
    ruleType: RuleType.ZONE,
    handleDeleteRule,
    rules,
  });

  if (!form) return null;

  return (
    <div className="moe-flex moe-flex-col moe-gap-6 moe-w-full">
      <div className="moe-flex moe-flex-col moe-gap-2">
        <Table columns={columns} data={rules} className="moe-w-full" size="m" getRowId={(row) => row.id} />
        <Button
          variant="tertiary"
          icon={<MinorPlusOutlined className="moe-w-6 moe-h-6" />}
          onPress={handleAddRule}
          className="moe-w-fit"
        >
          Add
        </Button>
      </div>
    </div>
  );
});

ZipcodeConfig.displayName = 'ZipcodeConfig';
