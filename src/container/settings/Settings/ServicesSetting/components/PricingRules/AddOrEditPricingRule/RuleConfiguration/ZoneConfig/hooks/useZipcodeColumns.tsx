import React, { useMemo, useState } from 'react';
import {
  Input,
  Select,
  IconButton,
  Heading,
  Tooltip,
  createColumnHelper,
  Form,
  Controller,
  Text,
  UseFormReturn,
} from '@moego/ui';
import { MinorInfoOutlined, MinorTrashOutlined } from '@moego/icons-react';
import { usePriceEnum } from '../../../hooks/usePriceEnum';
import { EffectType, RuleType } from '@moego/api-web/moego/models/offering/v2/pricing_rule_enums';
import { ZipcodeRule } from '../ZoneConfig.types';
import { LocalPricingRule, usePricingRuleSettingContext } from '../../../PricingRuleSettingContext';
import { RoundToInteger } from '../components/RoundToInteger';
import { ZonePricingRuleFormHelper } from '../../../utils/ZonePricingRuleFormHelper';
import { ReturnTypeAmosAction } from '../../../../../../../../../../types/common';
import { getZipcodeList } from '../../../../../../../../../../store/serviceArea/serviceArea.actions';
import { useDispatch } from 'amos';
import { useAsyncCallback } from '../../../../../../../../../../utils/hooks/useAsyncCallback';
import { useGeoZipcodes } from './useGeoZipcodes';

const columnHelper = createColumnHelper<ZipcodeRule>();

export const useZipcodeColumns = (params: {
  ruleType: RuleType;
  handleDeleteRule: (id: string) => void;
  rules: ZipcodeRule[];
}) => {
  const { ruleType, handleDeleteRule, rules } = params;
  const { priceEnum, priceOptions } = usePriceEnum(ruleType);
  const { form } = usePricingRuleSettingContext();
  const { zipcodeItems, isEnableGeoFencing } = useGeoZipcodes();

  return [
    columnHelper.accessor('zipcode', {
      header: () => (
        <Form.Label className="moe-text-secondary" isRequired>
          Zipcode
        </Form.Label>
      ),
      size: 224,
      cell: ({ row }) => {
        return (
          <Controller
            name={`ruleConfiguration.conditionGroups.${row.original.conditionGroupsIndex}.conditions.0.value.stringValues.values`}
            control={form?.control}
            rules={{ required: { value: true, message: 'Zipcode is required' } }}
            render={({ field, fieldState }) => (
              <ZipcodeSelect
                items={zipcodeItems}
                field={field}
                fieldState={fieldState}
                form={form}
                isEnableGeoFencing={isEnableGeoFencing}
              />
            )}
          />
        );
      },
    }),
    columnHelper.accessor('priceUplift', {
      header: () => (
        <Form.Label className="moe-text-secondary" isRequired>
          Price uplift
        </Form.Label>
      ),
      size: 200,
      cell: ({ row }) => (
        <div className="moe-flex moe-gap-[12px] moe-items-start">
          <Controller
            name={`ruleConfiguration.conditionGroups.${row.original.conditionGroupsIndex}.effect.value`}
            control={form?.control}
            rules={{ required: { value: true, message: 'Price uplift is required' } }}
            render={({ field, fieldState }) => {
              const priceUnitValue = form?.watch(
                `ruleConfiguration.conditionGroups.${row.original.conditionGroupsIndex}.effect.type`,
              );
              const priceUnit = priceUnitValue || new ZonePricingRuleFormHelper().createEffect().type;
              const { min, max } = priceEnum.mapLabels[priceUnit];
              const isPercentage =
                priceUnit === EffectType.PERCENTAGE_INCREASE || priceUnit === EffectType.PERCENTAGE_DISCOUNT;

              return (
                <Input.Number
                  minValue={min}
                  maxValue={max}
                  precision={isPercentage ? 0 : 2}
                  className="!moe-w-[90px]"
                  helpTextClassNames={{ error: 'moe-text-nowrap' }}
                  placeholder="Input"
                  errorMessage={fieldState.error?.message}
                  isInvalid={fieldState.invalid}
                  {...field}
                  value={field.value ?? null}
                />
              );
            }}
          />

          <Form.Item
            rules={{ required: true }}
            name={`ruleConfiguration.conditionGroups.${row.original.conditionGroupsIndex}.effect.type`}
          >
            <Select className="!moe-w-[90px]" items={priceOptions} />
          </Form.Item>
        </div>
      ),
    }),
    columnHelper.accessor('roundToInteger', {
      minSize: 138,
      header: () => (
        <div className="moe-flex moe-items-center moe-gap-2">
          <Heading size="6" className="moe-text-secondary">
            Round to integer
          </Heading>
          <Tooltip
            content="Calculated prices will be rounded to the nearest whole number. For example, $80.33 will be recorded as $80.00, and $80.66 as $81.00."
            side="top"
          >
            <MinorInfoOutlined className="moe-text-tertiary" />
          </Tooltip>
        </div>
      ),
      cell: ({ row }) => <RoundToInteger form={form} conditionGroupsIndex={row.original.conditionGroupsIndex} />,
    }),
    columnHelper.display({
      id: 'actions',
      cell: ({ row }) =>
        rules.length > 1 ? (
          <IconButton
            icon={<MinorTrashOutlined />}
            variant="secondary"
            showBorder={false}
            onPress={() => handleDeleteRule(row.original.id)}
          />
        ) : null,
    }),
  ];
};

const ZipcodeSelect: React.FC<{
  field: any;
  fieldState: any;
  form: UseFormReturn<LocalPricingRule> | undefined;
  items: {
    key: string;
    value: string;
    title: string;
  }[];
  isEnableGeoFencing: boolean;
}> = ({ field, fieldState, form, items, isEnableGeoFencing }) => {
  const conditionGroups = form?.getValues('ruleConfiguration.conditionGroups') || [];
  const [searchValue, setSearchValue] = useState('');
  const [zipcodeList, setZipcodeList] = useState<Awaited<ReturnTypeAmosAction<typeof getZipcodeList>>['data']>([]);
  const dispatch = useDispatch();

  const allSelectedZipcode = useMemo(() => {
    return conditionGroups.flatMap((conditionGroup) => {
      return conditionGroup.conditions.flatMap((condition) => {
        return condition.value.stringValues?.values ?? [];
      });
    });
  }, [conditionGroups]);

  const handleSearchValueChange = useAsyncCallback(async (value: string) => {
    value = value.trim();
    if (!value) {
      return;
    }
    setSearchValue(value);
    const result = await dispatch(getZipcodeList({ prefix: value }));
    setZipcodeList(result.data);
  });

  const zipcodeItems = useMemo(() => {
    if (items.length) {
      return items;
    }
    return zipcodeList.map((item) => {
      return {
        key: item.zipCode,
        value: item.zipCode,
        title: item.zipCode,
      };
    });
  }, [zipcodeList, items]);

  return (
    <Select.Multiple
      renderEmpty={() => {
        return (
          <Text
            variant="small"
            className="moe-flex moe-items-center moe-justify-center moe-w-full moe-h-[32px] moe-px-spacing-xs moe-text-disabled"
          >
            {searchValue ? 'No data' : 'Enter zipcode'}
          </Text>
        );
      }}
      items={zipcodeItems}
      errorMessage={fieldState.error?.message}
      mode="value"
      className="moe-w-[178px]"
      helpTextClassNames={{ error: 'moe-text-nowrap' }}
      isInvalid={fieldState.invalid}
      disabledKeys={allSelectedZipcode}
      {...(!isEnableGeoFencing && {
        isLoading: handleSearchValueChange.loading,
        searchValue: searchValue,
        onSearchValueChange: handleSearchValueChange,
      })}
      {...field}
    />
  );
};
