import { EffectType, RuleType } from '@moego/api-web/moego/models/offering/v2/pricing_rule_enums';
import { useSelector } from 'amos';
import { useMemo } from 'react';
import { selectCurrentBusiness } from '../../../../../../../../store/business/business.selectors';
import { createEnum } from '../../../../../../../../store/utils/createEnum';
import { MAX_FIXED_PRICE, MAX_PERCENTAGE_PRICE, MIN_FIXED_PRICE, MIN_PERCENTAGE_PRICE } from '../../PricingRules.utils';

export const usePriceEnum = (ruleType: RuleType) => {
  const [business] = useSelector(selectCurrentBusiness);
  const currency = business.printCurrency();
  const priceEnum = useMemo(() => {
    const result = createEnum({
      FixedDiscount: [
        EffectType.FIXED_DISCOUNT,
        {
          suffix: currency,
          min: MIN_FIXED_PRICE,
          max: MAX_FIXED_PRICE,
        },
      ],
      PercentageDiscount: [
        EffectType.PERCENTAGE_DISCOUNT,
        {
          suffix: '%',
          min: MIN_PERCENTAGE_PRICE,
          max: MAX_PERCENTAGE_PRICE,
        },
      ],
      FixedIncrease: [
        EffectType.FIXED_INCREASE,
        {
          suffix: currency,
          min: MIN_FIXED_PRICE,
          max: MAX_FIXED_PRICE,
        },
      ],
      PercentageIncrease: [
        EffectType.PERCENTAGE_INCREASE,
        {
          suffix: '%',
          min: MIN_PERCENTAGE_PRICE,
          max: MAX_PERCENTAGE_PRICE,
        },
      ],
    });
    return result;
  }, [currency]);

  const priceOptions = useMemo(() => {
    let priceOptionValues: EffectType[];
    switch (ruleType) {
      case RuleType.MULTIPLE_PET:
      case RuleType.MULTIPLE_STAY:
        priceOptionValues = [EffectType.FIXED_DISCOUNT, EffectType.PERCENTAGE_DISCOUNT];
        break;
      case RuleType.PEAK_DATE:
      case RuleType.ZONE:
        priceOptionValues = [EffectType.FIXED_INCREASE, EffectType.PERCENTAGE_INCREASE];
        break;
      default:
        throw new Error('Invalid rule type');
    }
    return priceOptionValues.map((key) => ({
      value: key,
      label: priceEnum.mapLabels[key].suffix,
    }));
  }, [priceEnum, ruleType]);

  return { priceEnum, priceOptions };
};
