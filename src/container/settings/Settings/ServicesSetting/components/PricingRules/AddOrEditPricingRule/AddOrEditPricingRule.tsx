import { type UpsertPricingRuleParams } from '@moego/api-web/moego/api/offering/v2/pricing_rule_api';
import { Source, type RuleType } from '@moego/api-web/moego/models/offering/v2/pricing_rule_enums';
import { Button, cn, Heading, IconButton, MinorCloseOutlined, Spin, Tag, Text, useForm, useFormState } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import { cloneDeep } from 'lodash';
import React, { useEffect, useMemo } from 'react';
import { useHistory } from 'react-router';
import { useMount } from 'react-use';
import { toastApi } from '../../../../../../../components/Toast/Toast';
import { ScrollerProvider } from '../../../../../../../layout/components/ScrollerProvider';
import { PATH_ADD_OR_EDIT_PRICING_RULE_V2, PATH_SERVICE_SETTING } from '../../../../../../../router/paths';
import {
  checkConfiguration,
  upsertPricingRule,
} from '../../../../../../../store/pricingRule/actions/private/pricingRule.actions';
import { companyPricingRuleMapBox } from '../../../../../../../store/pricingRule/pricingRule.boxes';
import { isNormal } from '../../../../../../../store/utils/identifier';
import { useRouteParams, useRouteQueryV2, useRouteState } from '../../../../../../../utils/RoutePath';
import { abortNavigation } from '../../../../../../../utils/abortNavigation';
import { useBizIdReadyEffect } from '../../../../../../../utils/hooks/useBizIdReadyEffect';
import { useLatestCallback } from '../../../../../../../utils/hooks/useLatestCallback';
import { useUnsavedConfirmGlobalV2 } from '../../../../../../../utils/hooks/useUnsavedConfirmGlobalV2';
import { ServicesNav } from '../../../types';
import { PricingRuleConfig, PricingRuleModalAction } from '../PricingRules.enum';
import { PricingRulesReporter } from '../PricingRules.report';
import { usePricingRuleSaveModal } from '../hooks/usePricingRuleSaveModal';
import { AddOrEditPricingRuleForm } from './AddOrEditPricingRuleForm';
import { type LocalPricingRule, PricingRuleSettingContext } from './PricingRuleSettingContext';
import { useRuleIsDeactivate } from './hooks/useRuleIsDeactivate';
import { pricingRuleFormHelperFactory } from './utils/PricingRuleFormHelperFactory';
import { useSerialCallback } from '@moego/tools';
import { useBool } from '../../../../../../../utils/hooks/useBool';

export const AddOrEditPricingRule = () => {
  const reporter = PricingRulesReporter.getInstance();
  const history = useHistory();
  const dispatch = useDispatch();
  const { ruleType } = useRouteParams(PATH_ADD_OR_EDIT_PRICING_RULE_V2);
  const { ruleId } = useRouteQueryV2(PATH_ADD_OR_EDIT_PRICING_RULE_V2);
  const { isActive } = useRouteState(PATH_ADD_OR_EDIT_PRICING_RULE_V2);
  const isEditMode = isNormal(ruleId);
  const form = useForm<LocalPricingRule>({});
  const [pricingRuleMap] = useSelector(companyPricingRuleMapBox);
  const helper = pricingRuleFormHelperFactory.getHelper(Number(ruleType));
  const data = isEditMode ? pricingRuleMap.mustGetItem(ruleId) : undefined;
  const pricingRule = useMemo(() => helper.initFormValue({ pricingRule: data?.toJSON(), isActive }), [data, isActive]);
  const openPricingRuleSaveModal = usePricingRuleSaveModal();
  const { title, nav, unsaveLabel, description } = PricingRuleConfig.mapLabels[Number(ruleType) as RuleType];
  const { isDeactivate } = useRuleIsDeactivate(isEditMode, true, form);
  const header = `${isEditMode ? 'Edit' : 'Add'} ${title}`;
  const isSaveSuccess = useBool();

  const savePricingRule = useLatestCallback(async (source: UpsertPricingRuleParams) => {
    const data = cloneDeep(source);
    if (!isEditMode) {
      delete data.pricingRuleDef.id;
    }
    await dispatch(upsertPricingRule(data));
    return true;
  });

  const handleGoBack = useLatestCallback(() => {
    history.push(PATH_SERVICE_SETTING.build({ panel: ServicesNav.PricingRules, childPanel: nav }));
    setTimeout(() => {
      isSaveSuccess.close();
    }, 100);
  });

  const handleApplyModalSave = useLatestCallback(async (isApplyToUpcomingAppointments: boolean) => {
    const pricingRuleDef = helper.transformLocalPricingRuleToServer(form.getValues());
    const result = await savePricingRule({
      pricingRuleDef,
      applyToUpcomingAppointments: isApplyToUpcomingAppointments,
    });
    reporter.reportPricingRuleSettingAction({
      action: isEditMode ? 'edit' : 'save',
      ruleType: Number(ruleType) as RuleType,
    });
    if (result) {
      isSaveSuccess.open();
      toastApi.success('New pricing rule added!');
      handleGoBack();
    }
  });

  const handleSave = useSerialCallback(() => {
    // 检查服务选择字段是否为空
    const formValues = form.getValues();
    const {
      allAddonApplicable,
      allGroomingApplicable,
      selectedGroomingServices = [],
      selectedAddonServices = [],
    } = formValues;

    // 检查四个字段是否都没有值
    const hasNoServiceValues =
      !allAddonApplicable &&
      !allGroomingApplicable &&
      selectedGroomingServices.length === 0 &&
      selectedAddonServices.length === 0;

    if (hasNoServiceValues) {
      form.setError('selectedGroomingServices', {
        type: 'required',
        message: 'Applicable services is required',
      });
    }

    return form.handleSubmit(async () => {
      const { isValid } = await dispatch(
        checkConfiguration({ pricingRuleDef: helper.transformLocalPricingRuleToServer(form.getValues()) }),
      );
      if (isValid) {
        await openPricingRuleSaveModal({
          action:
            isDeactivate && isEditMode ? PricingRuleModalAction.DeactivateEditSave : PricingRuleModalAction.NewSave,
          onSave: handleApplyModalSave,
        });
      }
    })();
  });

  const handleUnsaveConfirm = useLatestCallback(async () => {
    const isFormValid = await form.trigger();
    if (!isFormValid) {
      await form.handleSubmit(() => {})();
      toastApi.error(`${header} save failed. Please fill in the required fields.`);
      abortNavigation();
      return;
    }
    await handleSave();
  });

  const getData = useSerialCallback(async () => {
    if (ruleId) {
      await dispatch(helper.getInitEffectActions(ruleId));
    }
  });

  useBizIdReadyEffect(() => {
    if (ruleId) {
      getData();
    }
  }, [ruleId]);

  useEffect(() => {
    if (pricingRule) {
      form.reset({ ...pricingRule });
    }
  }, [pricingRule]);

  useMount(() => {
    reporter.reportPricingRuleSettingPageView({
      ruleType: Number(ruleType) as RuleType,
      mode: isEditMode ? 'edit' : 'add',
    });
  });

  return (
    <ScrollerProvider
      style={{
        padding: 0,
        maxHeight: '100vh',
      }}
    >
      <PricingRuleSettingContext.Provider
        value={{
          form,
          ruleId,
          isEdit: isEditMode,
          ruleType: Number(ruleType) as RuleType,
        }}
      >
        <UnsavedConfirmBinder
          form={form}
          unsaveLabel={unsaveLabel}
          onCancel={handleGoBack}
          onConfirm={handleUnsaveConfirm}
          isSaveSuccess={isSaveSuccess.value}
        />
        <div className="moe-pb-[16px] moe-px-[24px] moe-w-full moe-font-manrope">
          <div className="moe-flex moe-w-full moe-h-[72px] moe-justify-between moe-items-center moe-sticky moe-top-[0px] moe-z-[1] moe-bg-white">
            <IconButton icon={<MinorCloseOutlined />} onPress={handleGoBack} color="transparent" size="xl" />
            <Button onPress={handleSave} isLoading={handleSave.isBusy()} size="l">
              Save
            </Button>
          </div>
          <div className="moe-flex moe-justify-center moe-mt-[16px] [@media(min-width:1450px)]:moe-grid [@media(min-width:1450px)]:moe-grid-cols-12 [@media(min-width:1450px)]:moe-gap-x-[24px]">
            <div className="[@media(max-width:1450px)]:moe-w-[670px] [@media(min-width:1450px)]:moe-col-start-4 [@media(min-width:1450px)]:moe-col-span-6">
              <Heading
                size="2"
                className={cn('moe-mb-xl moe-flex moe-items-center', {
                  'moe-mb-xs': !!description,
                })}
              >
                {header}
                {data?.source === Source.ENTERPRISE_HUB && (
                  <Tag className="moe-ml-[4px]" color="discover" label="Corporate" />
                )}
              </Heading>
              {!!description && (
                <Text variant="small" className="moe-text-tertiary moe-mb-m">
                  {description}
                </Text>
              )}
              <Spin
                isLoading={getData.isBusy()}
                classNames={{
                  base: 'moe-w-full moe-h-full',
                  container: 'moe-w-full moe-h-full',
                  iconContainer: 'moe-top-[20%]',
                }}
              >
                <AddOrEditPricingRuleForm />
              </Spin>
            </div>
          </div>
        </div>
      </PricingRuleSettingContext.Provider>
    </ScrollerProvider>
  );
};

/**
 * Confirm 组件抽出去，避免 isDirty 变化的时候导致整个组件重新渲染
 */
const UnsavedConfirmBinder: React.FC<{
  form: ReturnType<typeof useForm<LocalPricingRule>>;
  unsaveLabel?: string;
  onCancel: () => void;
  onConfirm: () => void;
  isSaveSuccess: boolean;
}> = ({ form, unsaveLabel, onCancel, onConfirm, isSaveSuccess }) => {
  const { isDirty } = useFormState({ control: form.control });

  useUnsavedConfirmGlobalV2({
    showConfirm: isDirty && !isSaveSuccess,
    modalProps: {
      title: unsaveLabel,
      confirmText: 'Save',
      cancelText: 'Discard changes',
      content: 'Would you like to save your changes before exiting?',
      onCancel,
      onConfirm,
    },
  });
  return null;
};
