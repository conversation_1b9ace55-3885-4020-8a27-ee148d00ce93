import React from 'react';
import { Switch, Toolt<PERSON>, Controller, useWatch } from '@moego/ui';
import { EffectType } from '@moego/api-web/moego/models/offering/v2/pricing_rule_enums';
import { UseFormReturn } from '@moego/ui';

interface RoundToIntegerProps {
  form: UseFormReturn<any> | undefined;
  conditionGroupsIndex: number;
}

export const RoundToInteger: React.FC<RoundToIntegerProps> = ({ form, conditionGroupsIndex }) => {
  const priceUplift = useWatch({
    control: form?.control,
    name: `ruleConfiguration.conditionGroups.${conditionGroupsIndex}.effect.type`,
  });
  const isPercentage = priceUplift === EffectType.PERCENTAGE_INCREASE;

  return (
    <Controller
      name={`ruleConfiguration.conditionGroups.${conditionGroupsIndex}.effect.roundingPosition`}
      control={form?.control}
      render={({ field }) => {
        return (
          <Tooltip
            content={!isPercentage ? 'This function can only be applied to calculation by percentage.' : null}
            side="top"
          >
            <div className="moe-w-[32px]">
              <Switch
                isDisabled={!isPercentage}
                isSelected={field.value === 0 && isPercentage}
                onChange={(value) => field.onChange(value ? 0 : null)}
              />
            </div>
          </Tooltip>
        );
      }}
    />
  );
};
