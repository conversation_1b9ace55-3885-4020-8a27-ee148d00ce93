import React, { memo, useEffect, useRef, useState } from 'react';
import { Heading, SegmentControl, Typography } from '@moego/ui';
import { ServiceAreaConfig } from './ServiceAreaConfig';
import { ZipcodeConfig } from './ZipcodeConfig';
import { getDefaultSelectedType } from './ZoneConfig.utils';
import { usePricingRuleSettingContext } from '../../PricingRuleSettingContext';
import { useStore } from 'amos';
import { PATH_SETTING_BUSINESS_INFO } from '../../../../../../../../../router/paths';
import { currentBusinessIdBox } from '../../../../../../../../../store/business/business.boxes';
import { ZoneConfigTypeEnum, ZoneConfigTypeKeys } from '../../utils/AddOrEditPricingRule.config';
import { BusinessInfoNav } from '../../../../../../BusinessSetting/types';
import { ConditionType, RuleType } from '@moego/api-web/moego/models/offering/v2/pricing_rule_enums';
import { pricingRuleFormHelperFactory } from '../../utils/PricingRuleFormHelperFactory';

export interface ZoneConfigProps {
  onConfigTypeChange?: (type: ZoneConfigTypeKeys) => void;
  onServiceAreaRulesChange?: (rules: any[]) => void;
  onZipcodeRulesChange?: (rules: any[]) => void;
}

export const ZoneConfig = memo<ZoneConfigProps>((props) => {
  const { form } = usePricingRuleSettingContext();
  const { onConfigTypeChange, onServiceAreaRulesChange, onZipcodeRulesChange } = props;
  const isFirstUpdate = useRef(false);
  const store = useStore();

  const [selectedType, setSelectedType] = useState<ZoneConfigTypeKeys>(ZoneConfigTypeEnum.ServiceArea);

  const isServiceArea = +selectedType === ZoneConfigTypeEnum.ServiceArea;

  const handleTypeChange = (value: string) => {
    const newType = value as unknown as ZoneConfigTypeKeys;
    setSelectedType(newType);
    onConfigTypeChange?.(newType);
    form?.setValue('activeRuleConfiguration', newType);

    if (Number(newType) === ZoneConfigTypeEnum.Zipcode) {
      // 切换 zipcode 的时候，如果当前没有 zipcode 条件，则添加一个
      const conditionGroups = form?.getValues('ruleConfiguration.conditionGroups');
      const zipCodeFields = conditionGroups?.filter((field) => field.conditions?.[0]?.type === ConditionType.ZIPCODE);
      if (!zipCodeFields?.length) {
        const helper = pricingRuleFormHelperFactory.getHelper(RuleType.ZONE);
        const newConditionGroup = {
          conditions: [helper.createZipcodeCondition!()],
          effect: helper.createEffect(),
        };
        form?.setValue(
          'ruleConfiguration.conditionGroups',
          conditionGroups ? conditionGroups.concat(newConditionGroup) : [newConditionGroup],
        );
      }
    }
  };

  useEffect(() => {
    if (form?.getValues('ruleConfiguration.conditionGroups') && !isFirstUpdate.current) {
      const defaultSelectedType = getDefaultSelectedType(form?.getValues('ruleConfiguration.conditionGroups'));
      defaultSelectedType && handleTypeChange(String(defaultSelectedType));
      isFirstUpdate.current = true;
    }
  }, [form?.getValues('ruleConfiguration.conditionGroups')]);

  return (
    <div className="moe-flex moe-flex-col moe-gap-6 moe-w-full">
      {/* 配置类型选择 */}
      <div className="moe-flex moe-flex-col moe-gap-2 moe-items-start moe-justify-start moe-relative moe-w-full">
        {/* 标题 */}
        <Heading size="6">Configure by</Heading>

        {/* 分段控制器 */}
        <SegmentControl
          value={String(selectedType)}
          onChange={handleTypeChange}
          className="moe-w-[400px] moe-flex moe-gap-xxs moe-items-center"
          itemClassName="moe-h-[36px] moe-px-4 moe-py-2 flex-1 moe-justify-center"
        >
          {ZoneConfigTypeEnum.values.map((item) => (
            <SegmentControl.Item value={String(item)}>{ZoneConfigTypeEnum.mapLabels[item].label}</SegmentControl.Item>
          ))}
        </SegmentControl>

        {/* 说明文本 */}
        {isServiceArea && (
          <Typography.Text
            variant="small"
            className="moe-text-tertiary"
            onClick={() => {
              window.open(
                PATH_SETTING_BUSINESS_INFO.queried(
                  { panel: BusinessInfoNav.ServiceArea },
                  { id: store.select(currentBusinessIdBox).toString() },
                ),
                '_blank',
                'noopener,noreferrer',
              );
            }}
          >
            Service area is set up on{' '}
            <Typography.Link variant="small">
              Settings {'>'} Business {'>'} Service area
            </Typography.Link>
            .
          </Typography.Text>
        )}
      </div>

      {/* 配置内容 */}
      <div className="moe-w-full">
        {isServiceArea ? (
          <ServiceAreaConfig onRulesChange={onServiceAreaRulesChange} />
        ) : (
          <ZipcodeConfig onRulesChange={onZipcodeRulesChange} />
        )}
      </div>
    </div>
  );
});

ZoneConfig.displayName = 'ZoneConfig';
