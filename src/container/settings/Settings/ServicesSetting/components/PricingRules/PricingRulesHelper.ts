import {
  type Condition,
  type RepeatDatesWeek,
  type Effect,
} from '@moego/api-web/moego/models/offering/v2/pricing_rule_defs';
import {
  ConditionType,
  EffectType,
  RuleApplyType,
  RuleType,
} from '@moego/api-web/moego/models/offering/v2/pricing_rule_enums';
import { type PricingRule } from '@moego/api-web/moego/models/offering/v2/pricing_rule_models';
import { Operator, type StringDateRange } from '@moego/api-web/moego/utils/v2/condition_messages';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import { CareTypeApplicableServiceConfig } from './AddOrEditPricingRule/utils/AddOrEditPricingRule.config';
import { PricingRuleConfig } from './PricingRules.enum';
import { selectCompanyCareTypeNameMap } from '../../../../../../store/careType/careType.selectors';
import { WeekDayEnum } from '../../../../../../utils/DateTimeUtil';
import { store } from '../../../../../../provider';
import { companyServiceMapBox } from '../../../../../../store/service/service.boxes';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';

export class PricingRulesHelper {
  private static formatDateRangeText(dateRange: StringDateRange) {
    const business = store.select(selectCurrentBusiness);
    return `${business.formatDate(dateRange?.startDate)} - ${business.formatDate(dateRange?.endDate)}`;
  }

  private static getSpecificDateText(condition: Condition) {
    const { value } = condition;
    if (!value.dateRange) {
      return '';
    }
    return this.formatDateRangeText(value.dateRange);
  }

  private static getWeekDayText(week: RepeatDatesWeek) {
    const { dayOfWeeks } = week;
    const days = dayOfWeeks.map((day) => WeekDayEnum.mapLabels[day]);

    let daysText = '';
    switch (days.length) {
      case 1:
        daysText = `${days[0].fullName}s`;
        break;
      case 2:
        daysText = days.map((day) => `${day.fullName}s`).join(' and ');
        break;
      default:
        const lastDay = days.pop();
        daysText = `${days.map((day) => `${day.fullName}`).join(', ')} and ${lastDay?.fullName}`;
        break;
    }

    return daysText;
  }

  static getRepeatDateText(condition: Condition) {
    const { value } = condition;
    const { week, dateRange } = value.repeatDates ?? {};
    const weekText = week ? `on ${this.getWeekDayText(week)}` : '';
    const dateRangeText = dateRange ? `from ${this.formatDateRangeText(dateRange)}` : '';
    return `Recurring ${weekText} ${dateRangeText}`;
  }

  /**
   * 获取时间文案，目前 pricing rule 有两种 date 类型
   * - 特定日期
   * - 重复日期
   */
  public static getDateText(rule: PricingRule) {
    const {
      ruleConfiguration: { conditionGroups },
    } = rule;
    const firstCondition = conditionGroups.find((group) =>
      group.conditions.find(
        (condition) => condition.type === ConditionType.DATE_RANGE || condition.type === ConditionType.REPEAT_DATES,
      ),
    )?.conditions[0];
    if (!firstCondition) {
      return '';
    }
    switch (firstCondition.type) {
      case ConditionType.DATE_RANGE:
        return this.getSpecificDateText(firstCondition);
      case ConditionType.REPEAT_DATES:
        return this.getRepeatDateText(firstCondition);
      default:
        throw new Error(`Unsupported condition type: ${firstCondition.type}`);
    }
  }

  private static getServiceDisplayText(serviceId: string) {
    const serviceMap = store.select(companyServiceMapBox);
    const service = serviceMap.mustGetItem(+serviceId);
    if (service.inactive) {
      return `${service.name} (inactive)`;
    }
    return service.name;
  }

  /**
   * 获取 pricing rule 的生效 service
   *
   * @static
   * @param {PricingRule} rule
   * @param {boolean} showServiceName
   * @return {*}
   * @memberof PricingRulesHelper
   */
  public static getRuleApplicableServices(rule: PricingRule, showServiceName = false) {
    const companyCareTypeNameMap = store.select(selectCompanyCareTypeNameMap);
    const applicableServices: string[] = [];
    CareTypeApplicableServiceConfig.values.map((serviceItemType) => {
      const careTypeName = companyCareTypeNameMap.getName(serviceItemType);
      const { isAllApplicableKey, selectedServiceIdKey, isAllAddonApplicableKey, selectedAddonIdKey } =
        CareTypeApplicableServiceConfig.mapLabels[serviceItemType];

      if (rule[isAllApplicableKey]) {
        applicableServices.push(`All ${careTypeName.toLowerCase()} services`);
      } else if (rule[selectedServiceIdKey].length > 0) {
        if (showServiceName) {
          applicableServices.push(
            rule[selectedServiceIdKey]
              .map((serviceId) => this.getServiceDisplayText(serviceId))
              .filter(Boolean)
              .join(', '),
          );
        } else {
          applicableServices.push(`${rule[selectedServiceIdKey].length} services`);
        }
      }
      if (serviceItemType === ServiceItemType.GROOMING) {
        if (rule[isAllAddonApplicableKey]) {
          applicableServices.push(`All ${careTypeName.toLowerCase()} add-ons`);
        } else if (rule[selectedAddonIdKey].length > 0) {
          if (showServiceName) {
            applicableServices.push(
              rule[selectedAddonIdKey]
                .map((serviceId) => this.getServiceDisplayText(serviceId))
                .filter(Boolean)
                .join(', '),
            );
          } else {
            applicableServices.push(`${rule[selectedAddonIdKey].length} add-ons`);
          }
        }
      }
    });
    return applicableServices;
  }

  public static getEffectText(effect: Effect, ruleType: RuleType) {
    const business = store.select(selectCurrentBusiness);
    let value = '';
    let prefix = '';
    let suffix = '';

    switch (ruleType) {
      case RuleType.PEAK_DATE:
        prefix = '+';
        suffix = '';
        break;
      case RuleType.MULTIPLE_PET:
      case RuleType.MULTIPLE_STAY:
        prefix = '';
        suffix = ' off';
        break;
      default:
        break;
    }

    switch (effect.type) {
      case EffectType.FIXED_DISCOUNT:
      case EffectType.FIXED_INCREASE:
        value = business.formatAmount(effect.value);
        break;
      case EffectType.PERCENTAGE_DISCOUNT:
      case EffectType.PERCENTAGE_INCREASE:
        value = `${effect.value}%`;
        break;
    }

    return `${prefix}${value}${suffix}`;
  }

  /**
   * 获取 pricing rule 生效规则
   *
   * @static
   * @param {PricingRule} rule
   * @return {*}
   * @memberof PricingRulesHelper
   */
  public static getRuleEffectText(rule: PricingRule) {
    const {
      type,
      ruleConfiguration: { conditionGroups },
    } = rule;
    return conditionGroups.map((conditionGroup) => this.getEffectText(conditionGroup.effect, type)).join(', ');
  }

  /**
   * 获取 pricing rule 生效规则
   *
   * @static
   * @param {PricingRule} rule
   * @return {*}
   * @memberof PricingRulesHelper
   */
  static getRuleApplyText(rule: PricingRule) {
    let result = '';
    switch (rule.ruleApplyType) {
      case RuleApplyType.APPLY_TO_EACH:
        result = 'for each pet';
        break;
      case RuleApplyType.APPLY_TO_ADDITIONAL:
        result = 'for additional pet(s)';
        break;
      case RuleApplyType.APPLY_TO_FIRST_PET:
        result = 'for first pet';
        if (rule.isChargePerLodging) {
          result += ' in each lodging';
        }
        break;
      default:
        break;
    }
    return result;
  }

  public static getRuleOperatorText(operator: Operator) {
    switch (operator) {
      case Operator.EQ:
        return '=';
      case Operator.NE:
        return '≠';
      case Operator.GT:
        return '>';
      case Operator.GE:
        return '≥';
      case Operator.LT:
        return '<';
      case Operator.LE:
        return '≤';
      case Operator.LIKE:
        return 'like';
      case Operator.NOT_LIKE:
        return 'not like';
      default:
        return '';
    }
  }

  /**
   * 拼装 rule 配置文本
   *
   * @static
   * @param {PricingRule} rule
   * @return {*}  {string[]}
   * @memberof PricingRulesHelper
   */
  public static getRuleConfigurationText(rule: PricingRule): string[] {
    const result: string[] = [];
    const { rulePrefixLabel = '' } = PricingRuleConfig.mapLabels[rule.type];
    rule.ruleConfiguration.conditionGroups.forEach((conditionGroup) => {
      const condition = conditionGroup.conditions[0];
      const effect = conditionGroup.effect;
      const operatorText = this.getRuleOperatorText(condition.operator);
      const prefix = `${rulePrefixLabel} ${operatorText} ${condition.value.numberValue ?? ''}`;
      const effectText = `apply a ${this.getEffectText(effect, rule.type)}`;
      const suffix = this.getRuleApplyText(rule);
      result.push(`${prefix}, ${effectText} ${suffix}`);
    });
    return result;
  }

  public static getRuleTooltipText(rule: PricingRule) {
    const result: string[] = [];
    const { tooltipText } = PricingRuleConfig.mapLabels[rule.type];
    rule.ruleConfiguration.conditionGroups.forEach((conditionGroup) => {
      const condition = conditionGroup.conditions[0];
      const effect = conditionGroup.effect;
      const operatorText = this.getRuleOperatorText(condition.operator);
      const suffix = this.getRuleApplyText(rule);
      result.push(
        `${operatorText} ${condition.value.numberValue} ${tooltipText}, ${this.getEffectText(effect, rule.type)} ${suffix}`,
      );
    });
    return result;
  }
}
