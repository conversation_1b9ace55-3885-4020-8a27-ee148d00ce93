import React, { memo, useMemo } from 'react';
import { type PricingRule } from '@moego/api-web/moego/models/offering/v2/pricing_rule_models';
import { Heading, Text } from '@moego/ui';
import { ConditionType, EffectType } from '@moego/api-web/moego/models/offering/v2/pricing_rule_enums';
import { serviceAreaMapBox } from '../../../../../../../store/serviceArea/serviceArea.boxes';
import { useSelector } from 'amos';
import { usePriceEnum } from '../AddOrEditPricingRule/hooks/usePriceEnum';

export interface ZoneExpandPanelProps {
  pricingRule: PricingRule;
}
export const ZoneExpandPanel = memo<ZoneExpandPanelProps>((props) => {
  const { pricingRule } = props;
  const [serviceAreaMap] = useSelector(serviceAreaMapBox);
  const { priceEnum } = usePriceEnum(pricingRule.type);

  const zoneList = useMemo(() => {
    return pricingRule.ruleConfiguration.conditionGroups
      .filter(
        (field) =>
          !!field.conditions?.[0]?.value.numberValues?.values?.length ||
          !!field.conditions?.[0]?.value.stringValues?.values?.length,
      )
      .flatMap((field) => {
        const condition = field.conditions?.[0];
        const isServiceArea = condition?.type === ConditionType.SERVICE_AREA;
        const effect = field.effect;

        const ids = condition?.value?.numberValues?.values || condition?.value?.stringValues?.values || [];

        // 获取价格和单位
        const priceValue = effect?.value || 0;
        const priceUnit = effect?.type || EffectType.FIXED_INCREASE;

        // 获取四舍五入设置
        const roundToInteger = effect?.roundingPosition === 0;

        const label = isServiceArea
          ? ids.map((id: string) => serviceAreaMap.mustGetItem(Number(id)).areaName).join(',')
          : ids.join(',');

        return {
          label,
          priceUplift: priceValue,
          unit: priceUnit,
          roundToInteger,
        };
      });
  }, [pricingRule, serviceAreaMap]);

  return (
    <div className="moe-bg-neutral-sunken-0 moe-rounded-s moe-py-s moe-w-full moe-flex moe-flex-col moe-gap-s moe-ml-[-16px] moe-pr-s">
      {/* 表头 */}
      <div className="moe-flex moe-items-center moe-justify-between moe-w-full">
        <ZoneExpandPanelItem>
          <Heading className="moe-text-secondary" size={6}>
            Zipcode / Service area
          </Heading>
        </ZoneExpandPanelItem>
        <ZoneExpandPanelItem>
          <Heading className="moe-text-secondary" size={6}>
            Price uplift
          </Heading>
        </ZoneExpandPanelItem>
        <ZoneExpandPanelItem>
          <Heading className="moe-text-secondary" size={6}>
            Round to integer
          </Heading>
        </ZoneExpandPanelItem>
      </div>

      {/* 表格数据行 */}
      {zoneList.map((zone) => (
        <div className="moe-flex moe-items-center moe-justify-between moe-w-full">
          <ZoneExpandPanelItem>{zone.label}</ZoneExpandPanelItem>
          <ZoneExpandPanelItem>
            {zone.unit === EffectType.FIXED_INCREASE ? (
              <>
                {priceEnum.mapLabels[zone.unit].suffix}
                {zone.priceUplift}
              </>
            ) : (
              <>
                {zone.priceUplift}
                {priceEnum.mapLabels[zone.unit].suffix}
              </>
            )}
          </ZoneExpandPanelItem>
          <ZoneExpandPanelItem>{zone.roundToInteger ? 'Yes' : 'No'}</ZoneExpandPanelItem>
        </div>
      ))}
    </div>
  );
});

const ZoneExpandPanelItem = memo((props) => {
  const { children } = props;

  return (
    <div className="moe-flex moe-items-center moe-justify-between moe-w-[288px] moe-px-s moe-h-[18px] moe-py-xs">
      <Text variant="small">{children}</Text>
    </div>
  );
});

ZoneExpandPanel.displayName = 'ZoneExpandPanel';
