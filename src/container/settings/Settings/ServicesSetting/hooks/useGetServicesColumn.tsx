import { ServiceModelSource } from '@moego/api-web/moego/models/offering/v1/service_models';
import { Tag, Text, Tooltip } from '@moego/ui';
import { useSelector } from 'amos';
import React, { useEffect, useMemo } from 'react';
import { type BasedTableColumnProps } from '../../../../../components/BasedTable/types';
import { CoatTypeNames } from '../../../../../components/PetInfo/CoatTypeNames';
import { PetSizes } from '../../../../../components/PetInfo/PetSizes';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { taxMapBox } from '../../../../../store/business/tax.boxes';
import { selectBDFeatureEnable } from '../../../../../store/company/company.selectors';
import { lodgingTypeMapBox } from '../../../../../store/lodging/lodgingType.boxes';
import { type PetSizeDTO } from '../../../../../store/onlineBooking/settings/petSize.boxes';
import { type ServiceRecord, companyServiceMapBox } from '../../../../../store/service/service.boxes';
import { withPl } from '../../../../../utils/calculator';
import { isUndefinedOrNullOrEmptyString } from '../../../../../utils/common';
import { useBool } from '../../../../../utils/hooks/useBool';
import { Actions } from '../components/Actions';
import { MaxDuration } from '../components/TableItem/MaxDuration';
import { TextWithTooltip } from '../components/TextWithTooltip';
import { ColumnType, ColumnsMap, ServiceColumns } from '../types';
import { hours2Minutes } from '../utils/inputTransformer';
import { useRenderTypeBreedText } from './useRenderTypeBreedText';
import { selectCompanyCareTypeNameMap } from '../../../../../store/careType/careType.selectors';

interface ServiceColumnProps {
  onEdit: (service: ServiceRecord) => void;
  onDelete: (service: ServiceRecord) => void;
  onDuplicate: (service: ServiceRecord) => void;
  selectedBusinessId?: number;
  serviceType: number;
  serviceItemType: number;
  petSizeList?: PetSizeDTO[];
}

export const useGetServicesColumns = (props: ServiceColumnProps) => {
  const [business, lodgingTypeMap, boardingDaycareFeatureEnable, taxMap, companyServiceMap, companyCareTypeNameMap] =
    useSelector(
      selectCurrentBusiness,
      lodgingTypeMapBox,
      selectBDFeatureEnable,
      taxMapBox,
      companyServiceMapBox,
      selectCompanyCareTypeNameMap,
    );
  const renderTypeBreedText = useRenderTypeBreedText();
  const { serviceType, serviceItemType } = props;

  const columns: Record<number, BasedTableColumnProps<ServiceRecord>> = useMemo(() => {
    return {
      [ColumnType.Services]: {
        width: 320,
        title: 'Services',
        prop: 'name',
        render(data) {
          const headingClassName = `moe-service-${data.serviceId}`;
          const isOverflow = useBool();

          useEffect(() => {
            // 这里用 ref 会有问题，所以直接取了
            const node = document.querySelector<HTMLDivElement>(`.${headingClassName}`);
            if (node) {
              // 会大于 1 才需要展示 tooltip
              isOverflow.as(node.scrollHeight > node.offsetHeight + 1);
            }
          }, []);

          return (
            <div className="moe-flex moe-items-start moe-gap-x-[8px]">
              <div
                style={{ backgroundColor: data.colorCode }}
                className="moe-w-[12px] moe-h-[12px] moe-rounded-full moe-flex-shrink-0 moe-mt-[4px]"
              />
              <Tooltip isDisabled={!isOverflow.value} content={data.name} delay={0} side="top">
                <div
                  className={`moe-mb-0 moe-line-clamp-2 moe-text-base moe-font-bold moe-font-manrope ${headingClassName}`}
                  onClick={() => props.onEdit(data)}
                >
                  {data.source === ServiceModelSource.ENTERPRISE_HUB && (
                    <Tag className="moe-align-bottom moe-mr-[4px]" color="discover" label="Corporate" size="s" />
                  )}
                  {data.name}
                </div>
              </Tooltip>
            </div>
          );
        },
      },
      [ColumnType.Price]: {
        width: 136,
        title: 'Price',
        prop: 'price',
        render: (data) => {
          if (props.selectedBusinessId) {
            const overrideItem = data.locationStaffOverrideList.find(
              (item) => Number(item.locationOverride.businessId) === props.selectedBusinessId,
            );
            if (!isUndefinedOrNullOrEmptyString(overrideItem?.locationOverride.price)) {
              return business.formatAmount(overrideItem!.locationOverride.price!);
            }
          }
          return business.formatAmount(data.price);
        },
      },
      [ColumnType.Tax]: {
        width: 96,
        title: 'Tax',
        prop: 'taxId',
        render: (data) => {
          return taxMap.mustGetItem(data.taxId).taxRate! + '%';
        },
      },
      [ColumnType.Duration]: {
        width: 144,
        title: 'Duration',
        prop: 'duration',
        render: (data) => {
          if (props.selectedBusinessId) {
            const overrideItem = data.locationStaffOverrideList.find(
              (item) => Number(item.locationOverride.businessId) === props.selectedBusinessId,
            );
            if (!isUndefinedOrNullOrEmptyString(overrideItem?.locationOverride.duration)) {
              return withPl(overrideItem!.locationOverride.duration!, 'min');
            }
          }
          return withPl(data.duration, 'min');
        },
      },
      [ColumnType.MaxDuration]: {
        width: 150,
        title: 'Max duration',
        prop: 'duration',
        render: (data) => {
          let maxDuration = withPl(+(hours2Minutes.input?.(data.maxDuration!) || 0), 'hour');
          if (props.selectedBusinessId) {
            const overrideItem = data.locationStaffOverrideList.find(
              (item) => Number(item.locationOverride.businessId) === props.selectedBusinessId,
            );
            if (!isUndefinedOrNullOrEmptyString(overrideItem?.locationOverride.maxDuration)) {
              maxDuration = withPl(+(hours2Minutes.input?.(overrideItem!.locationOverride.maxDuration!) || 0), 'hour');
            }
          }
          return (
            <MaxDuration
              maxDuration={maxDuration}
              enableAutoRollover={data.autoRolloverRule?.enabled}
              targetServiceId={data.autoRolloverRule?.targetServiceId}
              afterMinute={data.autoRolloverRule?.afterMinute}
            />
          );
        },
      },
      [ColumnType.TypeAndBreed]: {
        minWidth: 152,
        title: 'Type & Breed',
        prop: 'breedFilter',
        render: (data) => {
          return renderTypeBreedText(data.breedFilter, data.customizedBreed);
        },
      },
      [ColumnType.PetSize]: {
        width: 180,
        title: 'Weight',
        prop: 'customizedPetSizes',
        render: (data) => (
          <PetSizes
            customizedPetSizes={data.customizedPetSizes}
            petSizeFilter={data.petSizeFilter}
            weightFilter={data.weightFilter}
            weightRange={data.weightRange}
          />
        ),
      },
      [ColumnType.CoatType]: {
        minWidth: 136,
        title: 'Coat type',
        prop: 'coatFilter',
        render(data) {
          const { coatFilter, customizedCoat } = data;
          return <CoatTypeNames value={customizedCoat} coatFilter={coatFilter} />;
        },
      },
      [ColumnType.LodgingTypes]: {
        minWidth: 232,
        title: 'Eligible lodging type',
        prop: 'customizedPetSizes',
        render: (data) => {
          const { customizedLodgings = [], lodgingFilter } = data;
          const values = customizedLodgings.map((id) => lodgingTypeMap.mustGetItem(id).name);

          if (!lodgingFilter) {
            return <div>All lodging types</div>;
          }

          return <TextWithTooltip values={values} />;
        },
      },
      [ColumnType.ApplicableServices]: {
        width: 168,
        title: 'Applicable services',
        prop: 'applicableServices',
        render(data) {
          const { serviceFilterList = [], serviceFilter } = data;
          const serviceTypeList = serviceFilterList.map((item) => {
            return `${companyCareTypeNameMap.getName(+item.serviceItemType)} services`;
          });

          if (!serviceFilter) {
            return <div>All services</div>;
          }

          return <TextWithTooltip values={serviceTypeList} maxCountLimit={1} />;
        },
      },
      [ColumnType.PrerequisiteClass]: {
        width: 268,
        maxWidth: 268,
        title: 'Prerequisite class',
        prop: 'prerequisiteClassIds',
        render(data) {
          const { isRequirePrerequisiteClass, prerequisiteClassIds } = data;
          const prerequisiteClassNameList = prerequisiteClassIds?.map(
            (id) => companyServiceMap.mustGetItem(Number(id)).name,
          );
          if (!isRequirePrerequisiteClass || prerequisiteClassIds.length === 0) {
            return (
              <Text variant="small" className="moe-text-primary">
                -
              </Text>
            );
          }
          return (
            <Text variant="regular-short" ellipsis>
              {prerequisiteClassNameList.join(', ')}
            </Text>
          );
        },
      },
      [ColumnType.Action]: {
        width: 64,
        title: '',
        prop: 'handler',
        stickyRight: true,
        alignRight: true,
        customClassNames: '!moe-pr-s moe-bg-white group-hover:moe-bg-neutral-sunken-light',
        render(data) {
          return (
            <Actions data={data} onEdit={props.onEdit} onDuplicate={props.onDuplicate} onDelete={props.onDelete} />
          );
        },
      },
    };
  }, [props.onDelete, props.onDuplicate, props.onEdit, business, props.selectedBusinessId, lodgingTypeMap, taxMap]);

  const showingColumnsType = ColumnsMap[`${serviceType}-${serviceItemType}`] || ServiceColumns;
  const showingColumns = showingColumnsType
    .filter(({ key }) => boardingDaycareFeatureEnable || key !== ColumnType.ApplicableServices)
    .map(({ key, ...rest }) => {
      return { ...columns[key], ...rest };
    });
  return showingColumns;
};
