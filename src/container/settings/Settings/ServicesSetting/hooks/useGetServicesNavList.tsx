import { useSelector } from 'amos';
import { useMemo } from 'react';
import { selectBDFeatureEnable } from '../../../../../store/company/company.selectors';
import { selectCompanyCareTypeRecordList } from '../../../../../store/careType/careType.selectors';
import { type SettingsLeftNavItem } from '../../types';
import { type ServiceSettingsLeftNav, ServicesNavType, getCareTypesLeftNav, getSettingServicesNavItem } from '../types';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { GrowthBookFeatureList } from '../../../../../utils/growthBook/growthBook.config';

export const useGetServicesNavList = (): SettingsLeftNavItem<ServiceSettingsLeftNav>[] => {
  const [companyCareTypeList, isBD, business] = useSelector(
    selectCompanyCareTypeRecordList,
    selectBDFeatureEnable(),
    selectCurrentBusiness,
  );
  const isMobileBiz = business.isMobileGrooming();
  const isEnableVaryPricingByZone = useFeatureIsOn(GrowthBookFeatureList.EnableVaryPricingByZone);

  const navList = useMemo(() => {
    // Addons - Evaluation -  Service charge
    const otherNavList = [ServicesNavType.Addons];
    const enableEvaluation = companyCareTypeList.some((item) => item.serviceItemType === ServiceItemType.EVALUATION);

    if (enableEvaluation) {
      otherNavList.push(ServicesNavType.Evaluation);
    }
    if (isBD) {
      otherNavList.push(ServicesNavType.PricingRules);
    } else {
      otherNavList.push(ServicesNavType.ServiceCharge);
      isMobileBiz && isEnableVaryPricingByZone && otherNavList.push(ServicesNavType.PricingRules);
    }

    // care types
    const careTypeOmitEvaluation = companyCareTypeList.filter(
      (item) => item.serviceItemType !== ServiceItemType.EVALUATION,
    );
    const careTypesLeftNav = getCareTypesLeftNav(careTypeOmitEvaluation);
    const otherLeftNavList = otherNavList.map((navType) => getSettingServicesNavItem(navType));

    return [careTypesLeftNav, ...otherLeftNavList];
  }, [isBD, companyCareTypeList]);

  return navList;
};
