import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { useSelector } from 'amos';
import React, { useMemo } from 'react';
import SvgAnimalDogSvg from '../../../../../assets/svg/animal-dog.svg';
import SvgCustomFeeSvg from '../../../../../assets/svg/custom-fee.svg';
import SvgExitDoorLoginSvg from '../../../../../assets/svg/exit-door-login.svg';
import SvgInvestmentCalendarSvg from '../../../../../assets/svg/investment-calendar.svg';
import SvgMoonSvg from '../../../../../assets/svg/moon.svg';
import SvgOverTimeFeesSvg from '../../../../../assets/svg/over-time-fees.svg';
import { selectPricingRuleOverview } from '../../../../../store/pricingRule/pricingRule.selectors';
import { createEnum } from '../../../../../store/utils/createEnum';
import { GrowthBookFeatureList } from '../../../../../utils/growthBook/growthBook.config';
import { PricingRuleTypes } from '../components/PricingRules/PricingRules.enum';
import { MajorFeedingOutlined, MajorMedicineOutlined } from '@moego/icons-react';
import SvgZoneGroupSvg from '../../../../../assets/svg/zone-group.svg';
import { selectBDFeatureEnable } from '../../../../../store/company/company.selectors';

export const PricingRuleEntrySections = createEnum({
  DiscountPricing: [
    1,
    {
      title: 'Discount pricing',
      rules: [
        {
          id: PricingRuleTypes.MultiplePets,
          icon: SvgAnimalDogSvg,
          title: 'Multiple pets',
          description: 'Apply a price reduction when booking services for multiple pets from the same household',
          activeCountKey: 'activeMultiPets',
          isMultipleActiveCount: true,
        },
        {
          id: PricingRuleTypes.MultipleNights,
          icon: SvgMoonSvg,
          title: 'Multiple nights/days',
          description: 'Apply a price reduction for pet(s) staying over a certain number of consecutive nights/days',
          activeCountKey: 'activeMultiStays',
          isMultipleActiveCount: true,
        },
      ],
    },
  ],
  DemandBasedPricing: [
    2,
    {
      title: 'Demand-based pricing',
      rules: [
        {
          id: PricingRuleTypes.PeakDates,
          icon: SvgInvestmentCalendarSvg,
          title: 'Peak dates',
          description: 'Apply an additional fee for bookings on high-demand dates, such as holidays',
          activeCountKey: 'activePeakDate',
          isMultipleActiveCount: true,
        },
      ],
    },
  ],
  Surcharges: [
    3,
    {
      title: 'Surcharges',
      rules: [
        {
          id: PricingRuleTypes.LatePickUpAndEarlyDropOff,
          icon: SvgExitDoorLoginSvg,
          title: 'Late pick-up/Early drop-off',
          description: 'Apply extra charges for pickups or drop-offs outside standard operating hours.',
          activeCountKey: 'activeOffHoursFee',
          isMultipleActiveCount: true,
        },
        {
          id: PricingRuleTypes.OverTimeFees,
          icon: SvgOverTimeFeesSvg,
          title: 'Exceed 24-hour period',
          description: 'Apply an additional fee when a pet’s stay exceeds a full 24-hour period',
          activeCountKey: 'active24HoursCharge',
          isMultipleActiveCount: false,
        },
        {
          id: PricingRuleTypes.FeedingCharge,
          icon: <MajorFeedingOutlined />,
          title: 'Feeding charge',
          description: 'Apply an extra charge for special feeding requests such as owner-provided food.',
          activeCountKey: 'activeFeedingCharge',
          isMultipleActiveCount: true,
        },
        {
          id: PricingRuleTypes.MedicationCharge,
          icon: <MajorMedicineOutlined />,
          title: 'Medication charge',
          description: 'Apply an extra charge for administering medications.',
          activeCountKey: 'activeMedicationCharge',
          isMultipleActiveCount: false,
        },
        {
          id: PricingRuleTypes.CustomFees,
          icon: SvgCustomFeeSvg,
          title: 'Custom fees',
          description: 'Apply extra charges automatically or manually based on specific needs',
          activeCountKey: 'activeCustomFees',
          isMultipleActiveCount: true,
        },
      ],
    },
  ],
});

export const MobilePricingRuleEntrySections = createEnum({
  DemandBasedPricing: [
    2,
    {
      title: 'Demand-based pricing',
      rules: [
        {
          id: PricingRuleTypes.Zone,
          icon: SvgZoneGroupSvg,
          title: 'Zones',
          description:
            "Adjust pricing based on the customer's location. Differential pricing can be applied across different zipcodes or service areas. This feature is only applicable to grooming services.",
          activeCountKey: 'activeZonePricing',
          isMultipleActiveCount: true,
          className: 'moe-h-[132px]',
          rowCount: 3,
        },
      ],
    },
  ],
});

export const usePricingRulesEntrySection = () => {
  const [pricingRuleOverview, isBD] = useSelector(selectPricingRuleOverview, selectBDFeatureEnable());
  const isFeedingMedicationEnabled = useFeatureIsOn(GrowthBookFeatureList.EnableFeedingMedicationPricingRule);

  return useMemo(() => {
    const entrySections = isBD ? PricingRuleEntrySections : MobilePricingRuleEntrySections;

    return entrySections.values.map((sectionId) => {
      const section = entrySections.mapLabels[sectionId];

      let rules = section.rules.map((rule) => {
        const actionCount =
          (pricingRuleOverview[rule.activeCountKey as keyof typeof pricingRuleOverview] as string) ?? '';
        return { ...rule, actionCount };
      });

      if (sectionId === PricingRuleEntrySections.Surcharges && !isFeedingMedicationEnabled) {
        rules = rules.filter(
          (rule) => ![PricingRuleTypes.FeedingCharge, PricingRuleTypes.MedicationCharge].includes(rule.id),
        );
      }

      return {
        ...section,
        rules,
        showDiscountPricingSettingsModal: sectionId === PricingRuleEntrySections.DiscountPricing,
      };
    });
  }, [isFeedingMedicationEnabled, pricingRuleOverview]);
};
