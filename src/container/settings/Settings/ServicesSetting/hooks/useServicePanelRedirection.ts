import { useSelector } from 'amos';
import { useEffect } from 'react';
import { useHistory } from 'react-router';
import { PATH_SERVICE_SETTING } from '../../../../../router/paths';
import { selectBDFeatureEnable } from '../../../../../store/company/company.selectors';
import { useRouteParams } from '../../../../../utils/RoutePath';
import { ServicesNav } from '../types';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';

export const useServicePanelRedirection = () => {
  const history = useHistory();
  const { panel } = useRouteParams(PATH_SERVICE_SETTING);
  const [isBD, business] = useSelector(selectBDFeatureEnable(), selectCurrentBusiness);
  useEffect(() => {
    if (!isBD && !business.isMobileGrooming() && panel === ServicesNav.PricingRules) {
      history.replace(PATH_SERVICE_SETTING.build({ panel: ServicesNav.Services }));
    }
    if (isBD && panel === ServicesNav.ServiceCharges) {
      history.replace(PATH_SERVICE_SETTING.build({ panel: ServicesNav.Services }));
    }
  }, [isBD, panel, business]);
};
