import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { SurchargeType } from '@moego/api-web/moego/models/order/v1/service_charge_enums';
import React from 'react';
import { Switch } from '../../../../components/SwitchCase';
import { PATH_SERVICE_SETTING } from '../../../../router/paths';
import { useRouteParams } from '../../../../utils/RoutePath';
import { GrowthBookFeatureList } from '../../../../utils/growthBook/growthBook.config';
import { CustomFees } from './components/PricingRules/CustomFees/CustomFees';
import { FeedingCharge } from './components/PricingRules/FeedingCharge/FeedingCharge';
import { LatePickUpAndEarlyDropOff } from './components/PricingRules/LatePickUpAndEarlyDropOff/LatePickUpAndEarlyDropOff';
import { PricingRuleList } from './components/PricingRules/PricingRuleList/PricingRuleList';
import { PricingRuleTypes } from './components/PricingRules/PricingRules.enum';
import { PricingRulesLanding } from './components/PricingRules/PricingRulesLanding/PricingRulesLanding';
import { RedirectToAddOrEdit } from './components/PricingRules/components/RedirectToAddOrEdit';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';

export const PricingRulesSetting = () => {
  const { childPanel } = useRouteParams(PATH_SERVICE_SETTING);
  const isFeedingMedicationEnabled = useFeatureIsOn(GrowthBookFeatureList.EnableFeedingMedicationPricingRule);

  return (
    <Switch>
      <Switch.Case
        if={
          childPanel === PricingRuleTypes.PeakDates ||
          childPanel === PricingRuleTypes.MultipleNights ||
          childPanel === PricingRuleTypes.MultiplePets ||
          childPanel === PricingRuleTypes.Zone
        }
      >
        <PricingRuleList
          careTypesScope={
            [PricingRuleTypes.PeakDates, PricingRuleTypes.MultipleNights, PricingRuleTypes.MultiplePets].includes(
              childPanel as PricingRuleTypes,
            )
              ? [ServiceItemType.BOARDING, ServiceItemType.DAYCARE]
              : []
          }
        />
      </Switch.Case>
      <Switch.Case if={childPanel === PricingRuleTypes.LatePickUpAndEarlyDropOff}>
        <LatePickUpAndEarlyDropOff />
      </Switch.Case>
      <Switch.Case if={childPanel === PricingRuleTypes.CustomFees}>
        <CustomFees />
      </Switch.Case>
      <Switch.Case if={isFeedingMedicationEnabled && childPanel === PricingRuleTypes.FeedingCharge}>
        <FeedingCharge />
      </Switch.Case>
      <Switch.Case if={childPanel === PricingRuleTypes.OverTimeFees}>
        <RedirectToAddOrEdit surchargeType={SurchargeType.CHARGE_24_HOUR} />
      </Switch.Case>
      <Switch.Case if={isFeedingMedicationEnabled && childPanel === PricingRuleTypes.MedicationCharge}>
        <RedirectToAddOrEdit surchargeType={SurchargeType.MEDICATION_FEE} />
      </Switch.Case>
      <Switch.Case else>
        <PricingRulesLanding />
      </Switch.Case>
    </Switch>
  );
};
