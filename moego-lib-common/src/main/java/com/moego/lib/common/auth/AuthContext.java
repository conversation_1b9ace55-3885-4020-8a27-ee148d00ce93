/*
 * @since 2022-06-22 19:59:04
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.lib.common.auth;

import static com.moego.lib.common.exception.ExceptionUtil.bizException;

import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.core.TypeRef;
import com.moego.lib.common.thread.ThreadContextHolder;
import com.moego.lib.common.util.JsonUtil;
import java.util.Map;
import org.springframework.util.StringUtils;

public record AuthContext(
        Long todoUserId,
        Long accountId,
        Long enterpriseId,
        Long companyId,
        // Account structure 改造后, context 里的 business id 不再可靠.
        // 对于新的业务接口, 请在接口入参里定义 business id, 并自行调用 moego-svc-organization 接口检查入参的 business id 与 context 里的 company id
        // 的对应关系.
        // 对于老接口, 可以先保持使用 context 里的 business id, 排期推动下线迁移到新的业务接口.
        @Deprecated Long businessId,
        Long staffId,
        Long sessionId,
        Long subSessionId,
        Long customerId,
        String sessionData,
        String subSessionData,
        String obName,
        String impersonator,
        String brandedAppId) {
    public static final String HK_TODO_USER_ID = "X-Todo-User-Id";

    public static final String HK_ACCOUNT_ID = "X-Moe-Account-Id";

    public static final String HK_ENTERPRISE_ID = "X-Moe-Enterprise-Id";
    public static final String HK_COMPANY_ID = "X-Moe-Company-Id";
    public static final String HK_BUSINESS_ID = "X-Moe-Business-Id";
    public static final String HK_STAFF_ID = "X-Moe-Staff-Id";

    public static final String HK_SESSION_ID = "X-Moe-Session-Id";
    public static final String HK_SUB_SESSION_ID = "X-Moe-Sub-Session-Id";
    public static final String HK_SESSION_DATA = "X-Moe-Session-Data";
    public static final String HK_SUB_SESSION_DATA = "X-Moe-Sub-Session-Data";

    public static final String HK_CUSTOMER_ID = "X-Moe-Customer-Id";
    public static final String HK_OB_NAME = "X-Moe-Ob-Name";
    public static final String HK_IMPERSONATOR = "X-Moe-Impersonator";

    public static final String HK_BRANDED_APP_ID = "X-Moe-Branded-App-Id";

    public void checkValid(AuthType type) {
        switch (type) {
            case DENY -> throw bizException(Code.CODE_FORBIDDEN);
            case TODO_USER -> {
                if (todoUserId == null) {
                    throw bizException(Code.CODE_UNAUTHORIZED_ERROR);
                }
            }
            case ACCOUNT -> {
                if (sessionId == null || sessionId <= 0) {
                    throw bizException(Code.CODE_UNAUTHORIZED_ERROR);
                }
                if (accountId == null || accountId <= 0) {
                    throw bizException(Code.CODE_UNAUTHORIZED_ERROR);
                }
            }
            case COMPANY -> {
                checkValid(AuthType.ACCOUNT);
                if (companyId == null || staffId == null) {
                    throw bizException(Code.CODE_UNAUTHORIZED_ERROR);
                }
            }
            case BUSINESS -> {
                checkValid(AuthType.ACCOUNT);
                if (companyId == null || businessId == null || staffId == null) {
                    throw bizException(Code.CODE_UNAUTHORIZED_ERROR);
                }
            }
            case OB -> {
                // 检查 OB 主会话
                checkOBMainSessionValid();
                // 除了主会话之外，OB 登录态要求必须有子会话
                if (subSessionId == null) {
                    throw bizException(Code.CODE_UNAUTHORIZED_ERROR);
                }
            }
            case OB_EXISTING_CLIENT -> {
                checkValid(AuthType.OB);
                if (customerId == null) {
                    throw bizException(Code.CODE_UNAUTHORIZED_ERROR);
                }
            }
            default -> {}
        }
    }

    public void checkOBMainSessionValid() {
        // OB 主子会话的 account id 都 < -1，不再兼容 customer token (accountId 为 -1)
        if (accountId == null || accountId >= -1) {
            throw bizException(Code.CODE_UNAUTHORIZED_ERROR);
        }
        // sessionId 必填
        if (sessionId == null) {
            throw bizException(Code.CODE_UNAUTHORIZED_ERROR);
        }
    }

    /**
     * Get thread bound auth context.
     *
     * <p color="red"> NOTE:
     * <p color="orange"> Shouldn't return null, if not bound with thread, return empty context.
     * <p color="orange"> Because we use {@link AuthContext} like this: <code>AuthContext.get().getAccountId();</code>
     *
     * @return auth context, empty context if not bound with thread, never null.
     */
    public static AuthContext get() {
        AuthContext authContext = ThreadContextHolder.getContext(AuthContext.class);
        return authContext != null
                ? authContext
                : new AuthContext(null, null, null, null, null, null, null, null, null, null, null, null, null, null);
    }

    public Integer getTodoUserId() {
        return todoUserId != null ? todoUserId.intValue() : null;
    }

    /**
     * account id is long value, please use AuthContext.accountId()
     * @see AuthContext#accountId()
     * @return int value of account id
     */
    @Deprecated
    public Integer getAccountId() {
        return accountId != null ? accountId.intValue() : null;
    }

    public Integer getCompanyId() {
        return companyId != null ? companyId.intValue() : null;
    }

    @Deprecated
    public Integer getBusinessId() {
        return businessId != null ? businessId.intValue() : null;
    }

    public Integer getStaffId() {
        return staffId != null ? staffId.intValue() : null;
    }

    /**
     * please use AuthContext.sessionId()
     * @see AuthContext#sessionId()
     * @return int value of session id
     */
    @Deprecated
    public Integer getCustomerSessionId() {
        return sessionId != null ? sessionId.intValue() : null;
    }

    public Integer getCustomerId() {
        return customerId != null ? customerId.intValue() : null;
    }

    public String getSessionDataStringValue(String key) {
        Object value = StringUtils.hasText(sessionData)
                ? JsonUtil.toBean(sessionData, Map.class).get(key)
                : null;
        return value != null ? value.toString() : null;
    }

    public Long getSessionDataLongValue(String key) {
        var value = getSessionDataStringValue(key);
        return value != null ? Long.parseLong(value) : null;
    }

    public Integer getSessionDataIntegerValue(String key) {
        var value = getSessionDataStringValue(key);
        return value != null ? Integer.parseInt(value) : null;
    }

    public Boolean getSessionDataBooleanValue(String key) {
        var value = getSessionDataStringValue(key);
        return Boolean.parseBoolean(value);
    }

    public String getSubSessionDataStringValue(String key) {
        return StringUtils.hasText(subSessionData)
                ? JsonUtil.toBean(subSessionData, new TypeRef<Map<String, String>>() {})
                        .get(key)
                : null;
    }
}
