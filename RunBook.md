# moego-svc-split-payment Run Book

Run Book For moego-svc-split-payment

## 服务等级

`分账`属于 MoeGo 核心业务场景，服务等级为`S1`，可用性计入`SLA Avalibity`中，在极端情况下`不允许`服务自动降级为不可用状态。

### 可用性指标

- 阈值：`99.99%`
- 定义：当用户支付走上分账场景后，分账不可用或分账持续失败且重试不成功引起支付失败超过1分钟，则该1分钟视为不可用时间段。

## 服务场景
### 正向分账
- 描述：支付正向分账
- 类型：核心场景
- 流程：用户在支付后，如果走上分账策略，资金会从平台账户自动拆分到平台、商户、贷款供应商等分账参与方
- API：
    - `/moego.service.split.payment.SplitPaymentService/SplitPayment` 正向分账接口
### 逆向分账
- 描述：支付逆向分账
- 类型：核心场景
- 流程：用户在退款后，如果走上分账策略，资金会根据分账策略从各个分账方收回资金到平台账户
- API：
    - `/moego.service.split.payment.SplitPaymentService/ReverseSplitPayment` 逆向分账接口
    - `/moego.service.split.payment.SplitPaymentService/SyncReverseSplitPayment` 同步逆向分账接口
### 分账重试
- 描述：重试接口，供定时任务调度使用
- 类型：辅助场景
- 流程：重试第一次分账失败的请求
- API：
    - `/moego.service.split.payment.SplitPaymentService/RetrySplitPaymentForTask` 重试分账接口
### 分账批量charge
- 描述：分账针对小金额charge进行打批处理，供定时任务调度使用
- 类型：辅助场景
- 流程：定时任务自动触发
- API：
    - `/moego.service.split.payment.SplitPaymentService/BatchChargeForTask` 打批接口
### 重试分账批量charge
- 描述：重试分账打批charge，供定时任务调度使用
- 类型：辅助场景
- 流程：定时任务自动触发
- API：
    - `/moego.service.split.payment.SplitPaymentService/RetryBatchChargeForTask` 重试打批接口
### 批量获取分账子单
- 描述：获取分账子单，供下游回查使用
- 类型：辅助场景
- 流程：下游回查
- API：
    - `/moego.service.split.payment.SplitPaymentService/BatchGetDetailList` 批量获取分账子单

## 运维手册
### 服务不可用
- 收到告警后，查看 [Service APM](https://us5.datadoghq.com/apm/services/moego-svc-split-payment/operations/grpc.server/resources)
    - 检查 Resources 中 Requests, p95 latency, errors top5 的接口情况
        - 如果 Requests 为 0，则应排查网关服务状态
        - 如果 p95 latency 大幅度增加，则应排查具体的 API
        - 如果 errors top5 中有某个接口错误率大幅度增加，则应根据具体错误码进行 debug
    - 检查 Traces & Logs 查看是否有报错
        - 如果有报错，则根据报错信息进行 debug
            - 检查split-payment PG 库数据情况
                - 正向分账看`split_record`&`split_detail`
                - 逆向分账看`split_reverse_record`&`split_reverse_detail`
                - 小额打批处理看`batchable_charge` & `charge_batch`
            - 首次分账失败可以依赖重试，如果多次重试均失败需要详细排查，此时大概率是第三方调用有问题
            - 第三方请求报错的话处理看日志还可以查看表 `third_party_invoke_exception_record`
        - 如果是 latency 上升，则优先根据 trace 查看耗时分布
            - 检查是否有[DB 慢查询](https://us5.datadoghq.com/apm/traces?query=env%3Ans-production%20%40base_service%3Amoego-svc-split-payment%20%40component%3A%22gorm.io%2Fgorm.v1%22&agg_m=count&agg_m_source=base&agg_t=count&cols=core_service%2Ccore_resource_name%2Clog_duration%2Clog_http.method%2Clog_http.status_code&fromUser=false&graphType=waterfall&historicalData=true&messageDisplay=inline&panel_tab=flamegraph&query_translation_version=v0&refresh_mode=sliding&shouldShowLegend=true&sort=desc&sort_by=%40duration&sort_order=desc&spanType=all&spanViewType=metadata&storage=hot&view=spans&start=1730700199432&end=1730703799432&paused=false)
            - 检查上下游服务是否有异常，检查上下游调用耗时
    - 检查 Deployments 中每个版本的请求状况
        - 如果出现版本聚集，则优先考虑回滚
    - 检查 Infrastructure 和 Runtime 查看是否有资源异常
        - 如果单容器负载过高，则优先考虑重启容器
        - 如果整个 ReplicaSet 负载过高，则优先考虑通过 TF 调整 HPA 进行人工扩容
    - 如果确认是下游有问题，经决策后可以通过[mis](https://mis.moego.pet/pay_ops/traffic_switch_new)操作不调用下游先恢复业务
    - 如果确认是本服务有问题，且无法快速恢复，已经严重影响正常支付，经决策后可通过[mis](https://mis.moego.pet/pay_ops/traffic_switch_new)把分账策略全部关掉
    - 如果以上步骤都无法解决问题，则应直接联系服务 Owner 进行快速恢复